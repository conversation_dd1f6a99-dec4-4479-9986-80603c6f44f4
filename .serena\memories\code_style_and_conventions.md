# Code Style and Conventions

## Linting

- The project uses ESLint with the standard Next.js configurations: `next/core-web-vitals` and `next/typescript`. This enforces best practices for React, Next.js, and TypeScript.
- To run the linter, use the `npm run lint` command.

## Naming Conventions

- **Components**: `PascalCase` (e.g., `MyComponent.tsx`).
- **Interfaces & Types**: `PascalCase` (e.g., `interface UserInfo`).
- **Constants**: `UPPER_SNAKE_CASE` (e.g., `const MAX_ITEMS = 10;`).
- **Variables & Functions**: `camelCase` (e.g., `const itemCount = 5;`, `function fetchData() {}`).

## UI Conventions

The project has standardized the use of Ant Design components to ensure a consistent user experience.

### Button Standardization

- **Add (Page-level)**: Primary button with `PlusOutlined` icon.
  - `<Button type="primary" icon={<PlusOutlined />}>Add</Button>`
- **View/Edit (in Tables)**: Link-style button.
  - `<Button type="link">Edit</Button>`
- **Delete/Disable (in Tables)**: Dangerous link-style button.
  - `<Button type="link" danger>Delete</Button>`
- **Enable (in Tables)**: Link-style button.
  - `<Button type="link">Enable</Button>`
- **Back (Page-level)**: Default button with `ArrowLeftOutlined` icon.
  - `<Button icon={<ArrowLeftOutlined />}>Back</Button>`
- **Form Submit**: Primary button with `htmlType="submit"`.
  - `<Button type="primary" htmlType="submit">Submit</Button>`
- **Modal Cancel**: Default button.
  - `<Button>Cancel</Button>`
- **Modal Confirm/OK**: Primary button.
  - `<Button type="primary">OK</Button>`