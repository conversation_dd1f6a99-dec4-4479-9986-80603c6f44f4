# Codebase Structure and Patterns

## Directory Structure

The project follows the Next.js App Router structure.

- `app/(dashboard)/`: Contains the main application pages and layouts for the authenticated part of the app.
- `app/components/`: Home for reusable React components used across the application (e.g., `DataTable`, `Modal`).
- `app/services/api/`: Holds the API client logic. Each file typically corresponds to a specific API resource (e.g., `customerApi.ts`).
  - `baseApi.ts`: A crucial base class that encapsulates shared `axios` logic, including setting the base URL, injecting authentication tokens, and handling 401 (Unauthorized) responses by redirecting to the login page.
- `app/interfaces/`: Contains all TypeScript type and interface definitions, particularly Data Transfer Objects (DTOs) that define the shape of data exchanged with the API.

## Key Architectural Patterns

### Authentication

- The application uses **Token-Based Authentication (JWT)**.
- After a successful login, an `accessToken` and a `user` object are stored in the browser's `localStorage`.
- The `BaseApi` class automatically retrieves the `accessToken` from `localStorage` and adds it to the `Authorization` header of every outgoing request.
- If an API request returns a 401 status, the `BaseApi` interceptor catches it, clears the token and user data from `localStorage`, and redirects the user to the login page.

### List Page Pattern (Data Fetching)

- List pages (e.g., customer list, user list) follow a standardized pattern for handling server-side pagination, sorting, and filtering.
- **Core Component**: A custom `DataTable` component (`@/app/components/DataTable`) is used, which wraps the Ant Design `Table`.
- **State Management**: Each list page manages its own state for data (`dataSource`), loading status (`loading`), item count (`total`), pagination (`currentPage`, `pageSize`), sorting (`sortField`, `sortOrder`), and filtering (`searchInfos`).
- **Data Flow**:
  1. A `fetchData` function is responsible for calling the relevant API.
  2. It constructs a `BaseQueryParams` object from the current state.
  3. On success, it updates the `dataSource` and `total` states.
  4. `useEffect` is used to trigger `fetchData` whenever its dependencies (like `currentPage`, `sortField`, `searchInfos`) change.
  5. The `DataTable` component's callbacks (`onPageChange`, `onSort`, `onFilter`) update the corresponding state variables, which in turn triggers the `useEffect` to refetch data.
  6. When sorting or filtering, `currentPage` is reset to `1`.