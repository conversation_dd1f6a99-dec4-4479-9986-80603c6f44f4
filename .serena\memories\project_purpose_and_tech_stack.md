# Project Purpose and Tech Stack

## Project Purpose

This project, named "a-life", is an internal business management system, likely functioning as a CRM (Customer Relationship Management) or ERP (Enterprise Resource Planning) system. Its primary purpose is to manage various business operations including customers, sales, budgets, and reporting.

## Tech Stack

- **Framework**: Next.js 15 with React 18 (using the App Router)
- **Language**: TypeScript
- **UI Library**: Ant Design 5
- **Styling**: Tailwind CSS
- **API Communication**: Axios
- **Charting**: Chart.js, ECharts, Recharts
- **Utilities**: date-fns, moment, lodash, crypto-js