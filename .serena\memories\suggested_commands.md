# Suggested Commands

This project uses `npm` as the package manager.

## Development

- **Run the development server**:
  ```bash
  npm run dev
  ```
  This command starts the Next.js application in development mode with Turbopack for faster performance. The application will be available at `http://localhost:3000` by default.

## Production

- **Build the application**:
  ```bash
  npm run build
  ```
  This command creates a production-ready build of the application.

- **Start the production server**:
  ```bash
  npm run start
  ```
  This command starts the application in production mode. It should be run after a successful `npm run build`.

## Code Quality

- **Lint files**:
  ```bash
  npm run lint
  ```
  This command runs ESLint to check the code for style issues and potential errors based on the configured Next.js rules.

## Testing

- No dedicated testing script (e.g., `npm test`) was found in `package.json`. Further investigation might be needed to determine the testing strategy.