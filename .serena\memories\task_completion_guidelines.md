# Task Completion Guidelines

Before considering a task complete and finalizing your changes, please adhere to the following guidelines based on the project's setup.

## 1. Code Formatting and Style

- Ensure your code adheres to the naming and UI conventions outlined in the `code_style_and_conventions.md` memory.
- This includes correct `PascalCase`, `camelCase`, and `UPPER_SNAKE_CASE` usage.
- Ensure all new UI elements, especially buttons, follow the established standardization to maintain a consistent user experience.

## 2. Run Linter

- After making your changes, run the linter to catch any potential issues and ensure code quality.
- Execute the following command from the project root:
  ```bash
  npm run lint
  ```
- Fix any errors or warnings reported by the linter.

## 3. Verify Application Functionality

- Run the application in development mode (`npm run dev`) to manually test your changes.
- Ensure that the new features work as expected and that no existing functionality has been broken (regression testing).
- Pay special attention to data-fetching pages, ensuring that pagination, sorting, and filtering still work correctly.

## 4. Final Review

- Once all checks have passed, your task can be considered complete. The code should be ready for commit and review.