## UI 約定 (UI Conventions)

本專案採用以下 UI 函式庫與約定：

*   **命名規範 (Naming Conventions)**：
    *   元件 (Components): PascalCase (首字母大寫駝峰式)，例如 `MyComponent.tsx`。
    *   介面 (Interfaces): PascalCase (首字母大寫駝峰式)，例如 `interface UserInfo`。
    *   常數 (Constants): UPPER_SNAKE_CASE (全大寫蛇形)，例如 `MAX_USERS`。
    *   其他變數與函式 (Variables & Functions): camelCase (小寫駝峰式)，例如 `fetchData`。
*   **UI 函式庫 (UI Library)**：主要使用 [Ant Design (antd)](https://ant.design/) 作為元件庫。
*   **樣式 (Styling)**：使用 [Tailwind CSS](https://tailwindcss.com/) 進行原子化 CSS 樣式設定。
*   **按鈕標準化 (Button Standardization)**：
    *   頁面層級**新增**按鈕：`type="primary"`, `icon={<PlusOutlined />}`。
    *   表格操作**檢視/編輯**按鈕：`type="link"`。
    *   表格操作**刪除/停用**按鈕：`type="link"`, `danger`。
    *   表格操作**啟用**按鈕：`type="link"`。
    *   頁面**返回**按鈕：`type="default"`, `icon={<ArrowLeftOutlined />}`。
    *   表單**提交**按鈕：`type="primary"`, `htmlType="submit"`。
    *   彈窗**取消**按鈕：`type="default"` 或使用標準 `<button>` 搭配適當樣式。
    *   彈窗**確認/OK**按鈕：`type="primary"`。
*   **可重用元件 (Reusable Components)**：
    *   `DataTable`: 封裝 Ant Design Table，提供統一的分頁、排序、篩選處理邏輯。
    *   `TableSelectModal`: 提供一個可搜尋、可單選/多選的表格選擇彈窗。
    *   `Modal`: (可能為 Ant Design Modal 的封裝或自訂元件) 提供統一的彈窗樣式與行為。
    *   `SignaturePad`: 提供手寫簽名功能。

## 後端整合與狀態管理 (Backend Integration & State Management)

*   **基礎 API 服務 (`BaseApi`)**: 
    *   `app/services/api/baseApi.ts` 為所有 API 服務的基礎類別。
    *   封裝 `axios` 實例，統一處理 API 基礎 URL (`baseURL`)。
    *   透過攔截器 (interceptors) 自動在請求標頭 (Request Headers) 加入 `Authorization: Bearer <token>`。
    *   攔截 API 回應，若狀態碼為 401 (Unauthorized)，則清除本地儲存的認證資訊並導向登入頁面。
    *   各業務邏輯相關的 API 服務 (如 `customerApi`, `siteApi` 等) 應繼承 `BaseApi` 並定義各自的 API 端點方法。
*   **認證機制 (Authentication)**:
    *   採用 Token-Based Authentication (JWT)。
    *   使用者登入成功後，後端應返回 `accessToken` 及使用者基本資訊。
*   **本地儲存 (`localStorage`)**:
    *   使用 `localStorage` 持久化儲存 `accessToken`。
    *   使用者基本資訊 (通常是一個 JSON 物件) 序列化後以 `user` 的鍵名儲存於 `localStorage`。
    *   `BaseApi` 會從 `localStorage` 讀取 `accessToken` 以附加到 API 請求中。
    *   登出或遭遇 401 錯誤時，應清除 `localStorage` 中的 `accessToken` 和 `user` 資料。

## 列表頁面模式 (分頁、排序、篩選) (List Page Patterns)

本專案中的列表頁面（通常命名為 `page.tsx`）普遍採用以下模式來處理後端分頁、排序與篩選：

*   **核心元件**: 使用自訂的 `DataTable` 元件 (`@/app/components/DataTable`)，它封裝了 Ant Design 的 `Table` 並整合了分頁、排序觸發器和基於表頭的篩選輸入框。

*   **必要狀態 (State)**:
    *   `dataSource` (例如 `users`, `customers`): `useState<DataType[]>([]` - 儲存當前頁面顯示的資料。
    *   `loading`: `useState<boolean>(true)` - 控制表格的載入狀態。
    *   `total`: `useState<number>(0)` - 資料總筆數，用於 `DataTable` 的分頁器。
    *   `currentPage`: `useState<number>(1)` - 目前頁碼。
    *   `pageSize`: `useState<number>(10)` - 每頁顯示的筆數。
    *   `sortField`: `useState<string | null>(null)` - 當前排序的欄位 (API 所需的欄位名稱)。
    *   `sortOrder`: `useState<'ascend' | 'descend' | null>(null)` - 當前排序的順序。
    *   `searchInfos`: `useState<{ SearchField: string; SearchValue: string }[] | undefined>(undefined)` - (或類似結構) 儲存篩選條件。

*   **API 參數 (`BaseQueryParams`)**: 在呼叫列表 API 時，需組合以下參數傳遞給後端：
    ```typescript
    const apiParams: BaseQueryParams = {
      UsingPaging: true, // 固定為 true
      PageIndex: currentPage,
      NumberOfPperPage: pageSize,
      // 僅在 sortField 和 sortOrder 有值時加入
      SortOrderInfos: sortField && sortOrder 
        ? [{
            SortField: sortField,
            SortOrder: sortOrder === 'ascend' ? 'asc' : 'desc' // antd sorter order 轉為 API 需要的 asc/desc
          }]
        : undefined,
      SearchTermInfos: searchInfos, // 直接傳遞篩選狀態
    };
    ```

*   **資料獲取流程 (`fetchData` function)**:
    1.  定義一個異步函數 (通常命名為 `fetchData` 或類似) 用於獲取資料。
    2.  在函數開始時設置 `setLoading(true)`。
    3.  根據當前的狀態 (`currentPage`, `pageSize`, `sortField`, `sortOrder`, `searchInfos`) 組合 `apiParams`。
    4.  呼叫對應的 API 服務方法 (例如 `someApi.getList(apiParams)`)。
    5.  處理 API 回應：成功時，使用 `setDataSource`, `setTotal`, `setCurrentPage`, `setPageSize` 更新狀態；失敗時顯示錯誤訊息。
    6.  在 `finally` 區塊中設置 `setLoading(false)`。

*   **觸發更新 (`useEffect`)**: 
    *   在元件首次掛載時呼叫 `fetchData`。
    *   使用 `useEffect` 監聽 `currentPage`, `pageSize`, `sortField`, `sortOrder`, `searchInfos` 等狀態的變化，並在變化時重新呼叫 `fetchData` (或直接在 `handleXXX` 回調中處理)。

*   **`DataTable` 回調處理**:
    *   `onPageChange = (page, size)`: 更新 `currentPage` 和 `pageSize` 狀態。
    *   `onSort = (sorter)`: 解析 Ant Design Table 傳來的 `sorter` 物件，更新 `sortField` 和 `sortOrder` 狀態。**重要**: 排序變更後，應將 `currentPage` 重設為 `1` 再觸發 `fetchData`。
    *   `onFilter = (currentSearchInfos)`: 更新 `searchInfos` 狀態。**重要**: 篩選變更後，應將 `currentPage` 重設為 `1` 再觸發 `fetchData`。

*   **範例程式碼片段 (基於 `permissions/accounts/page.tsx`)**: 
    ```tsx
    // ... (imports and other code) ...
    
    export default function AccountsPage() {
      // --- State Definitions ---
      const [users, setUsers] = useState<UserInfoListItem[]>([]);
      const [loading, setLoading] = useState(true);
      const [total, setTotal] = useState(0);
      const [currentPage, setCurrentPage] = useState(1);
      const [pageSize, setPageSize] = useState(10);
      const [sortField, setSortField] = useState<string | null>(null);
      const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(null);
      // Note: Search is handled slightly differently in the example, 
      // but typically you'd have a state like:
      const [searchInfos, setSearchInfos] = useState<SearchTermInfo[] | undefined>(undefined);
      
      // --- Fetch Data Function ---
      const fetchData = useCallback(async () => {
        setLoading(true);
        try {
          const apiParams: BaseQueryParams = {
            UsingPaging: true,
            PageIndex: currentPage,
            NumberOfPperPage: pageSize,
            SortOrderInfos: sortField && sortOrder
              ? [{ SortField: sortField, SortOrder: sortOrder === 'ascend' ? 'asc' : 'desc' }]
              : undefined,
            SearchTermInfos: searchInfos,
          };
          const response = await userInfoApi.getUserInfoList(apiParams); // Replace with your API call
          if (response.isSuccess && response.body) {
            setUsers(response.body.Detail || []);
            setTotal(response.body.RecordCount || 0);
            // It's often safer to trust the backend's returned pagination state
            // setCurrentPage(response.body.PageIndex || 1);
            // setPageSize(response.body.NumberOfPperPage || 10);
          } else {
            // Handle error
          }
        } catch (error) {
          // Handle error
        } finally {
          setLoading(false);
        }
      }, [currentPage, pageSize, sortField, sortOrder, searchInfos]); // Dependencies

      // --- useEffect to Fetch Data ---
      useEffect(() => {
        fetchData();
      }, [fetchData]); // Trigger fetch when fetchData function reference changes (due to dependencies)

      // --- DataTable Callbacks ---
      const handlePageChange = (page: number, size: number) => {
        setCurrentPage(page);
        setPageSize(size);
      };

      const handleSort = (sorter: SorterResult<UserInfoListItem> | SorterResult<UserInfoListItem>[]) => {
        const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
        const newSortField = currentSorter?.field as string | null ?? null;
        const newSortOrder = currentSorter?.order ?? null;
        
        // Check if sort actually changed
        if (newSortField !== sortField || newSortOrder !== sortOrder) {
           setSortField(newSortField);
           setSortOrder(newSortOrder);
           if (currentPage !== 1) {
               setCurrentPage(1); // Reset page to 1 on sort change
           } else {
               // If already on page 1, changing sort needs to trigger fetchData
               // This depends on how your useEffect dependency is set up.
               // Alternatively, call fetchData() directly here if needed.
           }
        }
      };

      const handleFilter = (currentSearchInfos: SearchTermInfo[]) => {
         const newSearchInfos = currentSearchInfos.length > 0 ? currentSearchInfos : undefined;
         // Check if filters actually changed
         if (JSON.stringify(newSearchInfos) !== JSON.stringify(searchInfos)) { 
             setSearchInfos(newSearchInfos);
             if (currentPage !== 1) {
                 setCurrentPage(1); // Reset page to 1 on filter change
             } else {
                // Similar logic as handleSort for triggering fetch if needed
             }
         }
      };

      // --- DataTable Render ---
      return (
        // ... (Provider, layout divs) ...
        <DataTable<UserInfoListItem>
          columns={columns} // Define your columns
          dataSource={users}
          loading={loading}
          total={total}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onSort={handleSort}
          onFilter={handleFilter}
          rowKey="UserInfoId" // Important: Set your unique row key
        />
        // ... (Modals, etc.) ...
      );
    }
    ```
