# 房屋單位 (Unit) API

檔案路徑: `AlifeApi.WebApi.Controllers.UnitsController`
基本路徑: `/api/units`

---

## 1. 取得房屋單位列表 (分頁)

取得篩選後的房屋單位列表，支援分頁。

-   **Method:** `POST`
-   **URL:** `/api/units/list`
-   **Request Body:**

    ```json
    {
      "SiteCode": "A01",
      "BuildingId": 1,
      "PageNumber": 1,
      "PageSize": 10
    }
    ```
    -   `SiteCode` (string, optional): 案場代碼。
    -   `BuildingId` (integer, optional): 建築物 ID。
    -   `PageNumber` (integer, required): 頁碼。
    -   `PageSize` (integer, required): 每頁筆數。

-   **Success Response (200 OK):**
    回傳一個包含分頁資訊和單位列表的物件。

    ```json
    {
      "Details": [
        {
          "UnitId": 1,
          "FloorId": 1,
          "BuildingId": 1,
          "SiteCode": "A01",
          "UnitNumber": "A1",
          "Status": "可售",
          "TotalArea": 40.5
        }
      ],
      "PageNumber": 1,
      "PageSize": 10,
      "TotalPages": 5,
      "TotalRecords": 50,
      "HasPreviousPage": false,
      "HasNextPage": true
    }
    ```

---

## 2. 取得單一房屋單位

根據 ID 取得單一房屋單位的詳細資料。

-   **Method:** `GET`
-   **URL:** `/api/units/{id}`
-   **URL Parameter:**
    -   `id` (integer, required): 房屋單位的 ID。

-   **Success Response (200 OK):**

    ```json
    {
      "UnitId": 1,
      "FloorId": 1,
      "BuildingId": 1,
      "SiteCode": "A01",
      "UnitNumber": "A1",
      "UnitType": "住宅",
      "Layout": "3房2廳2衛",
      "Orientation": "座北朝南",
      "MainArea": 25.5,
      "TotalArea": 40.5,
      "ListPrice": 12000000,
      "Status": "可售"
    }
    ```

-   **Error Response (404 Not Found):**

    ```json
    {
      "Message": "找不到 ID 為 999 的房屋單位。"
    }
    ```

---

## 3. 建立新的房屋單位

建立一個新的房屋單位。`SiteCode` 和 `BuildingId` 會根據傳入的 `FloorId` 自動帶入。

-   **Method:** `POST`
-   **URL:** `/api/units`
-   **Request Body:**

    ```json
    {
      "FloorId": 1,
      "UnitNumber": "A8",
      "UnitType": "住宅",
      "Layout": "2房1廳1衛",
      "Orientation": "座東朝西",
      "MainArea": 18.0,
      "TotalArea": 30.0,
      "ListPrice": 8800000,
      "Status": "可售",
      "Remarks": "新建立的單位"
    }
    ```

-   **Success Response (201 Created):**
    回傳新建立的資源，並在 `Location` header 中包含新資源的 URL。

    ```json
    {
      "UnitId": 101,
      "FloorId": 1,
      "BuildingId": 1,
      "SiteCode": "A01",
      "UnitNumber": "A8",
      "Status": "可售"
    }
    ```

-   **Error Response (400 Bad Request):**

    ```json
    {
      "Message": "指定的樓層 ID 不存在。"
    }
    ```

---

## 4. 更新現有的房屋單位

更新一個已存在的房屋單位，只需提供要修改的欄位。

-   **Method:** `PUT`
-   **URL:** `/api/units/{id}`
-   **URL Parameter:**
    -   `id` (integer, required): 欲更新的房屋單位 ID。
-   **Request Body:**

    ```json
    {
      "Status": "保留",
      "ListPrice": 9000000,
      "Remarks": "客戶已預訂"
    }
    ```

-   **Success Response (204 No Content):**
    請求成功，伺服器不回傳任何內容。

-   **Error Response (404 Not Found):**

    ```json
    {
      "Message": "找不到要更新的房屋單位。"
    }
    ```

---

## 5. 刪除房屋單位

根據 ID 刪除一個房屋單位。

-   **Method:** `DELETE`
-   **URL:** `/api/units/{id}`
-   **URL Parameter:**
    -   `id` (integer, required): 欲刪除的房屋單位 ID。

-   **Success Response (204 No Content):**
    請求成功，伺服器不回傳任何內容。

-   **Error Response (404 Not Found):**

    ```json
    {
      "Message": "找不到要刪除的房屋單位。"
    }
    ```

---

## 6. 匯入車位資料

批次匯入車位資料。如果車位已存在，則更新其資訊；如果不存在，則建立新的車位。

-   **Method:** `POST`
-   **URL:** `/api/units/import-parking-spaces`
-   **Request Body:**

    ```json
    {
      "SiteCode": "A01",
      "Data": [
        {
          "FloorLabel": "B1",
          "SpaceNumber": "P01",
          "SpaceType": "標準車位",
          "ListPrice": 2500000,
          "MinimumPrice": 2200000,
          "Status": "可售",
          "Ownership": "產權"
        },
        {
          "FloorLabel": "B2",
          "SpaceNumber": "P55",
          "SpaceType": "身障車位",
          "ListPrice": 2000000,
          "MinimumPrice": 1800000,
          "Status": "可售",
          "Ownership": "產權"
        }
      ]
    }
    ```
    - `SiteCode` (string, required): 欲匯入的案場代碼。
    - `Data` (array, required): 車位資料陣列。
        - `FloorLabel` (string, required): 樓層標籤 (例如 "B1", "B2")。
        - `SpaceNumber` (string, required): 車位編號。
        - `SpaceType` (string, required): 車位類型。
        - `ListPrice` (number, optional): 表價。
        - `MinimumPrice` (number, optional): 底價。
        - `Status` (string, required): 銷售狀態。
        - `Ownership` (string, optional): 權屬 (會寫入備註欄位)。

-   **Success Response (200 OK):**

    ```json
    {
      "Message": "車位資料匯入成功。"
    }
    ```

-   **Error Response (500 Internal Server Error):**

    ```json
    {
      "Message": "匯入過程中發生錯誤: 案場代碼 'A99' 不存在。"
    }
    ``` 