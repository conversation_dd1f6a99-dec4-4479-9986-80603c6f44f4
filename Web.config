<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <handlers>
      <add name="iisnode" path="server.js" verb="*" modules="iisnode" />
    </handlers>
    <rewrite>
      <rules>
        <rule name="NextJS">
          <match url="/*" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
          </conditions>
          <action type="Rewrite" url="server.js" />
        </rule>
      </rules>
    </rewrite>
    <iisnode 
      nodeProcessCommandLine="C:\Program Files\nodejs\node.exe"
      loggingEnabled="true"
      logDirectory="iisnode"
      debuggingEnabled="true"
      watchedFiles="*.js;node_modules\*;.next\*"
      node_env="production" 
    />
    <directoryBrowse enabled="false" />
    <defaultDocument>
      <files>
        <clear />
        <add value="server.js" />
      </files>
    </defaultDocument>
  </system.webServer>
</configuration> 