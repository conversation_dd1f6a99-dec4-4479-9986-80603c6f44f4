'use client';

import React, { useState, useEffect } from 'react';
import Script from 'next/script';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { authApi } from '@/app/services/api/authApi';
import { LoginResponse, StoredUserData } from '@/app/interfaces/dto/auth.dto';
import { encryptHmacSHA1 } from '@/app/utils/crypto';

export default function LoginPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    UserInfoId: '',
    UserPW: '',
    Captcha: '',
    remember: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [captchaImage, setCaptchaImage] = useState('');

  const fetchCaptcha = async () => {
    try {
      const blob = await authApi.getCaptcha();
      const imageUrl = URL.createObjectURL(blob);
      setCaptchaImage(imageUrl);
    } catch (error) {
      console.error('取得驗證碼失敗:', error);
    }
  };

  useEffect(() => {
    fetchCaptcha();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // 加密密碼
      const encryptedPassword = encryptHmacSHA1(formData.UserPW);
      
      const response = await authApi.login({
        UserInfoId: formData.UserInfoId,
        UserPW: encryptedPassword,  // 使用加密後的密碼
        Captcha: formData.Captcha
      });

      if (response.code === "000" && response.isSuccess) {
        // 存儲訪問令牌
        localStorage.setItem('accessToken', response.body.JWTToken);
        
        // 準備要存儲的用戶數據
        const userData: StoredUserData = {
          isLogin: true,
          error: null,
          currentUser: {
            UserInfoId: response.body.UserInfoId,
            UserPW: response.body.UserPW,
            Name: response.body.Name,
            IP: response.body.IP,
            Email: response.body.Email,
            MenuTrees: response.body.MenuTrees,
            JWTToken: response.body.JWTToken,
            JWTTokenExpireTime: response.body.JWTTokenExpireTime,
            CompanyId: response.body.CompanyId,
            Gender: response.body.Gender,
            BirthDate: response.body.BirthDate,
            TelephoneNumber: response.body.TelephoneNumber,
            MobileNumber: response.body.MobileNumber,
            RegisteredAddress: response.body.RegisteredAddress,
            MailingAddress: response.body.MailingAddress,
            EmergencyContactName: response.body.EmergencyContactName,
            EmergencyContactPhone: response.body.EmergencyContactPhone,
            EmergencyContactRelation: response.body.EmergencyContactRelation,
            ServiceUnit: response.body.ServiceUnit,
            HireDate: response.body.HireDate,
            Status: response.body.Status,
            LastLogoutTime: response.body.LastLogoutTime,
            LoginFailedCount: response.body.LoginFailedCount,
            IsInside: response.body.IsInside,
            IsM365: response.body.IsM365,
            IsEmailNotificationEnabled: response.body.IsEmailNotificationEnabled,
            Response: response.body.Response
          }
        };

        // 存儲用戶數據
        localStorage.setItem('user', JSON.stringify(userData));

        router.push('/customers');
      } else {
        setError(response.message || '登入失敗');
        fetchCaptcha();
      }
    } catch (error: any) {
      setError(error.message || '登入請求失敗，請稍後再試');
      fetchCaptcha();
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Script id="auth-redirect" strategy="beforeInteractive">
        {`(function() {
            try {
              var token = localStorage.getItem('accessToken');
              var user = localStorage.getItem('user');
              if (token && user) {
                window.location.replace('/customers');
              }
            } catch(e) {}
          })();`}
      </Script>
      <div className="min-h-screen bg-gradient-to-br from-indigo-800 to-blue-600 flex items-center justify-center p-4">
        <div className="w-full max-w-6xl grid grid-cols-1 md:grid-cols-2 bg-white rounded-xl shadow-2xl overflow-hidden">
          {/* 左側品牌區域 */}
          <div className="hidden md:flex flex-col justify-between relative bg-gradient-to-br from-emerald-500 to-teal-600 p-12">
            <div className="relative z-10">
              <h1 className="text-4xl font-bold text-white mb-2">美學生活</h1>
              <p className="text-white text-lg mb-12">房地產管理系統</p>
              
              <div className="space-y-8">
                <div className="flex items-center gap-5">
                  <div className="w-14 h-14 flex items-center justify-center bg-white/30 backdrop-blur-sm rounded-lg shadow-lg">
                    <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-xl text-white">專業服務</h3>
                    <p className="text-white">提供最優質的房地產代銷服務</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-5">
                  <div className="w-14 h-14 flex items-center justify-center bg-white/30 backdrop-blur-sm rounded-lg shadow-lg">
                    <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-xl text-white">高效管理</h3>
                    <p className="text-white">完整的客戶關係管理系統</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-5">
                  <div className="w-14 h-14 flex items-center justify-center bg-white/30 backdrop-blur-sm rounded-lg shadow-lg">
                    <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-xl text-white">安全可靠</h3>
                    <p className="text-white">完善的資料安全防護機制</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="text-white text-sm mt-12">
              © 2024 美學生活. 版權所有.
            </div>
          </div>

          {/* 右側登入表單 */}
          <div className="p-8 md:p-12 flex flex-col justify-center">
            <div className="mb-10">
              <h2 className="text-3xl font-bold text-gray-800 mb-3">用戶登入</h2>
              <p className="text-gray-600 text-lg">歡迎回來，請輸入您的帳號密碼</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="bg-red-50 text-red-600 px-4 py-3 rounded-lg text-sm font-medium border border-red-200">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {error}
                  </div>
                </div>
              )}
              
              <div>
                <label className="block text-base font-medium text-gray-800 mb-2">
                  使用者名稱
                </label>
                <input
                  type="text"
                  name="UserInfoId"
                  value={formData.UserInfoId}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="請輸入使用者名稱"
                />
              </div>

              <div>
                <label className="block text-base font-medium text-gray-800 mb-2">
                  密碼
                </label>
                <input
                  type="password"
                  name="UserPW"
                  value={formData.UserPW}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="請輸入密碼"
                />
              </div>

              <div>
                <label className="block text-base font-medium text-gray-800 mb-2">
                  驗證碼
                </label>
                <div className="flex gap-4">
                  <input
                    type="text"
                    name="Captcha"
                    value={formData.Captcha}
                    onChange={handleInputChange}
                    required
                    className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="請輸入驗證碼"
                    maxLength={6}
                  />
                  <div 
                    className="w-48 h-12 border border-gray-300 rounded-lg overflow-hidden cursor-pointer"
                    onClick={fetchCaptcha}
                  >
                    {captchaImage && (
                      <img 
                        src={captchaImage} 
                        alt="驗證碼" 
                        className="w-full h-full object-contain"
                      />
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="remember"
                    id="remember"
                    checked={formData.remember}
                    onChange={handleInputChange}
                    className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="remember" className="ml-2 block text-base text-gray-800">
                    記住我
                  </label>
                </div>
                <div>
                  <a href="#" className="text-blue-600 hover:text-blue-800 font-medium">
                    立即註冊
                  </a>
                </div>
              </div>

              <button
                type="submit"
                disabled={loading}
                className={`w-full py-3 px-4 mt-4 text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-colors ${
                  loading ? 'opacity-75 cursor-not-allowed' : ''
                }`}
              >
                {loading ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    登入中...
                  </span>
                ) : '登入'}
              </button>
              
              <div className="text-center mt-6">
                <a href="#" className="text-gray-600 hover:text-blue-600 text-sm">
                  忘記密碼？
                </a>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
} 