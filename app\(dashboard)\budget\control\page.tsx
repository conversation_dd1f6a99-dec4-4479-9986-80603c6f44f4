'use client';

import React, { useEffect, useState } from 'react';
import { Collapse, Table, Divider, Typography, Button, Modal } from 'antd';
import { ColumnsType } from 'antd/es/table';
import budgetData from './budgetData';
import { BarChartOutlined } from '@ant-design/icons';
import Chart from '../../../components/common/Chart';
import { ChartType } from '../../../interfaces/dto/chart.dto';

const { Panel } = Collapse;
const { Title } = Typography;

// 定義預算項目介面
interface BudgetItem {
  id: number;
  category: string;
  item: string;
  content: string;
  budgetAmount: number;
  budgetPercent: number;
  contractedAmount: number;
  invoicedAmount: number;
  recoverableAmount: number;
  balance: number;
}

// 類別清單
const categories = [
  { key: 'media', name: '一、媒體' },
  { key: 'planning', name: '二、企劃成本' },
  { key: 'site', name: '三、現場置建成本' },
  { key: 'maintenance', name: '四、維運成本' },
  { key: 'salary', name: '五、薪資' },
];

// 定義表格欄位
const columns: ColumnsType<BudgetItem> = [
  { title: '項目', dataIndex: 'item', key: 'item', width: 150 },
  { title: '內容', dataIndex: 'content', key: 'content' },
  { title: '預算金額', dataIndex: 'budgetAmount', key: 'budgetAmount', align: 'right' },
  { title: '預算比例', dataIndex: 'budgetPercent', key: 'budgetPercent', align: 'right', render: v => `${(v * 100).toFixed(2)}%` },
  { title: '發包金額', dataIndex: 'contractedAmount', key: 'contractedAmount', align: 'right' },
  { title: '已請款金額', dataIndex: 'invoicedAmount', key: 'invoicedAmount', align: 'right' },
  { title: '可還原金額', dataIndex: 'recoverableAmount', key: 'recoverableAmount', align: 'right' },
  { title: '預算結餘', dataIndex: 'balance', key: 'balance', align: 'right' },
];

// 圖表顏色
const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#d0ed57', '#a4de6c'];

export default function BudgetControlPage() {
  // Chart modal 狀態，chartItems 保存對應分類的 BudgetItem
  const [chartVisible, setChartVisible] = useState(false);
  const [chartItems, setChartItems] = useState<BudgetItem[]>([]);
  const [chartTitle, setChartTitle] = useState('');

  const [data, setData] = useState<BudgetItem[]>([]);

  // Collapse 展開狀態
  const [activeKeys, setActiveKeys] = useState<string[]>(categories.map(c => c.key));

  // 打開 Chart Modal，保存對應分類的 items
  const handleOpenChart = (categoryKey: string, categoryName: string) => {
    const items = data.filter(d => d.category === categoryKey);
    setChartItems(items);
    setChartTitle(categoryName);
    setChartVisible(true);
  };

  useEffect(() => {
    // 從 budgetData 中讀取「行銷費用(media)」與「業務費用(planning)」的測試資料
    const mediaItems = budgetData.find(b => b['大項'] === '行銷費用')?.['小項'] || [];
    const planningItems = budgetData.find(b => b['大項'] === '業務費用')?.['小項'] || [];
    // 現場置建成本樣本資料
    const siteItems: BudgetItem[] = [
      {
        id: mediaItems.length + planningItems.length + 1,
        category: 'site',
        item: '預售基地、EF區、景觀植',
        content: '土地租金：400,000元*11個月；112.12~13.10，共10個月；必要費用；植栽約20萬',
        budgetAmount: 4780000,
        budgetPercent: 0.1172,
        contractedAmount: 9550467,
        invoicedAmount: 7150427,
        recoverableAmount: 0,
        balance: 0,
      },
      {
        id: mediaItems.length + planningItems.length + 2,
        category: 'site',
        item: '接待中心、樣品屋',
        content: '裝修製作：約10萬；拆屋費用：約120萬；雜項工程：約4.7萬；約2場樣品屋',
        budgetAmount: 12110000,
        budgetPercent: 0.3002,
        contractedAmount: 10867429,
        invoicedAmount: 9872577,
        recoverableAmount: 12102,
        balance: 0,
      },
      {
        id: mediaItems.length + planningItems.length + 3,
        category: 'site',
        item: '園藝景觀',
        content: '外牆美化約35萬；後續維護約2萬',
        budgetAmount: 370000,
        budgetPercent: 0.0092,
        contractedAmount: 401300,
        invoicedAmount: 401300,
        recoverableAmount: 0,
        balance: 0,
      },
      {
        id: mediaItems.length + planningItems.length + 4,
        category: 'site',
        item: '空調主機',
        content: '空調設備 (租賃+安裝/維護)，共75萬*113.01~113.10，共10個月',
        budgetAmount: 750000,
        budgetPercent: 0.0186,
        contractedAmount: 997200,
        invoicedAmount: 834300,
        recoverableAmount: 0,
        balance: 0,
      },
      {
        id: mediaItems.length + planningItems.length + 5,
        category: 'site',
        item: '車道設備',
        content: '對講機門口機@1,200元2個，共2,400元；車道遙控器@250元100個，共2.5萬；車道感應讀卡機@1,000元2個，共2,000元；eTag讀取設備@10,000元2個，共2萬；門禁考勤設備@18,000元1台，共1.8萬；影像對講設備@1,200元2個，共2,400元；網路線設備@150元7台，共1,050元；監視設備工資@1,800元13=23,400元；電信雜項工資@1,500元*1台=1,500元；電視線工資',
        budgetAmount: 120000,
        budgetPercent: 0.003,
        contractedAmount: 250195,
        invoicedAmount: 233193,
        recoverableAmount: 17002,
        balance: 0,
      },
    ];
    // 維運成本樣本資料
    const maintenanceItems: BudgetItem[] = [
      {
        id: mediaItems.length + planningItems.length + siteItems.length + 1,
        category: 'maintenance',
        item: '勞務費',
        content: '代書費：約3.2萬元/件，簽證人：11個月=約10萬元；雜工：約10萬 清潔：(32,0001.2)約38,400元11個月=約42.3萬；警衛：1.5萬4班11個月=6萬',
        budgetAmount: 730000,
        budgetPercent: 0.0181,
        contractedAmount: 292500,
        invoicedAmount: 254295,
        recoverableAmount: 0,
        balance: 38205,
      },
      {
        id: mediaItems.length + planningItems.length + siteItems.length + 2,
        category: 'maintenance',
        item: '業務雜支、零用金',
        content: '業務雜支、零用金：預算、文具、郵電、車程費用(例假、十六週壹): 113.01~113.10 共11個月@15,000元/月',
        budgetAmount: 170000,
        budgetPercent: 0.0042,
        contractedAmount: 306940,
        invoicedAmount: 271940,
        recoverableAmount: 0,
        balance: 0,
      },
      {
        id: mediaItems.length + planningItems.length + siteItems.length + 3,
        category: 'maintenance',
        item: '雜項',
        content: '文具用品、影印、傳真、茶點補充、清潔用品、工具、雜支、零用金：113.01~113.10 共11個月@10,000元/月',
        budgetAmount: 120000,
        budgetPercent: 0.003,
        contractedAmount: 63712,
        invoicedAmount: 63712,
        recoverableAmount: 0,
        balance: 0,
      },
      {
        id: mediaItems.length + planningItems.length + siteItems.length + 4,
        category: 'maintenance',
        item: '公關交際費',
        content: '員工餐敘、建經在職訓練、文宣、禮品、郵票等',
        budgetAmount: 250000,
        budgetPercent: 0.0062,
        contractedAmount: 229875,
        invoicedAmount: 229875,
        recoverableAmount: 0,
        balance: 0,
      },
      {
        id: mediaItems.length + planningItems.length + siteItems.length + 5,
        category: 'maintenance',
        item: '交通電訊費(業務)',
        content: '油資、電費、電話費、管理費、業務費：112.11~113.10，共11個月@8萬',
        budgetAmount: 900000,
        budgetPercent: 0.0223,
        contractedAmount: 882484,
        invoicedAmount: 882484,
        recoverableAmount: 0,
        balance: 0,
      },
      {
        id: mediaItems.length + planningItems.length + siteItems.length + 6,
        category: 'maintenance',
        item: '維護修繕費',
        content: '水費、公共意外險、僱主意外險：113.01~113.10',
        budgetAmount: 100000,
        budgetPercent: 0.0025,
        contractedAmount: 194213,
        invoicedAmount: 194213,
        recoverableAmount: 0,
        balance: 0,
      },
    ];
    // 薪資成本樣本資料
    const salaryItems: BudgetItem[] = [
      {
        id: mediaItems.length + planningItems.length + siteItems.length + maintenanceItems.length + 1,
        category: 'salary',
        item: '薪資',
        content: '駐場人員薪資：約3.7萬※1.2倍約44,400元10個月=約45萬元；銷售副理薪資：約6萬1.2倍約7.2萬10個月=約72萬；業務薪資：約3.8萬1.2倍約45,600元10個月=約45.6萬；總計約162.6萬',
        budgetAmount: 2329280,
        budgetPercent: 0.06,
        contractedAmount: 4147546,
        invoicedAmount: 4147546,
        recoverableAmount: 0,
        balance: 0,
      },
      {
        id: mediaItems.length + planningItems.length + siteItems.length + maintenanceItems.length + 2,
        category: 'salary',
        item: '福利',
        content: '福利金：約5萬',
        budgetAmount: 50000,
        budgetPercent: 0.01,
        contractedAmount: 90327,
        invoicedAmount: 90327,
        recoverableAmount: 0,
        balance: 0,
      },
      {
        id: mediaItems.length + planningItems.length + siteItems.length + maintenanceItems.length + 3,
        category: 'salary',
        item: '銷售獎金',
        content: '銷售總金額獎金(約3%)：398萬；特別佣金(約2%)：26.6萬',
        budgetAmount: 4352576,
        budgetPercent: 0.011,
        contractedAmount: 1291577,
        invoicedAmount: 1291577,
        recoverableAmount: 0,
        balance: 0,
      },
      {
        id: mediaItems.length + planningItems.length + siteItems.length + maintenanceItems.length + 4,
        category: 'salary',
        item: '專案、業績獎金',
        content: '專案人員獎金(約7%)：93萬；專案業績獎金(約1%)：13.2萬',
        budgetAmount: 1063144,
        budgetPercent: 0.03,
        contractedAmount: 161337,
        invoicedAmount: 161337,
        recoverableAmount: 0,
        balance: 0,
      },
    ];
    const sample: BudgetItem[] = [
      // 行銷費用
      ...mediaItems.map((d, idx) => ({
        id: idx + 1,
        category: 'media',
        item: d['項目'],
        content: d['說明'],
        budgetAmount: d['預算金額'],
        budgetPercent: typeof d['數字1'] === 'string' ? parseFloat(d['數字1'].replace('%', '')) / 100 : d['數字1'],
        contractedAmount: (d['數字2'] ?? 0) as number,
        invoicedAmount: (d['數字3'] ?? 0) as number,
        recoverableAmount: (d['數字4'] ?? 0) as number,
        balance: (() => {
          const val = d['數字5'];
          if (typeof val === 'string') {
            const num = parseInt(val.replace(/[()%]/g, '').replace(/,/g, ''), 10);
            return val.includes('(') ? -num : num;
          }
          return (val as number) || 0;
        })(),
      })),
      // 業務費用
      ...planningItems.map((d, idx) => ({
        id: mediaItems.length + idx + 1,
        category: 'planning',
        item: d['項目'],
        content: d['說明'],
        budgetAmount: d['預算金額'],
        budgetPercent: typeof d['數字1'] === 'string' ? parseFloat(d['數字1'].replace('%', '')) / 100 : d['數字1'],
        contractedAmount: (d['數字2'] ?? 0) as number,
        invoicedAmount: (d['數字3'] ?? 0) as number,
        recoverableAmount: (d['數字4'] ?? 0) as number,
        balance: (() => {
          const val = d['數字5'];
          if (typeof val === 'string') {
            const num = parseInt(val.replace(/[()%]/g, '').replace(/,/g, ''), 10);
            return val.includes('(') ? -num : num;
          }
          return (val as number) || 0;
        })(),
      })),
      // 現場置建成本
      ...siteItems,
      // 維運成本
      ...maintenanceItems,
      // 薪資成本
      ...salaryItems,
    ];
    setData(sample);
  }, []);

  // 計算小計
  const calcSubtotal = (items: BudgetItem[]) => ({
    budgetAmount: items.reduce((sum, i) => sum + i.budgetAmount, 0),
    contractedAmount: items.reduce((sum, i) => sum + i.contractedAmount, 0),
    invoicedAmount: items.reduce((sum, i) => sum + i.invoicedAmount, 0),
    recoverableAmount: items.reduce((sum, i) => sum + i.recoverableAmount, 0),
    balance: items.reduce((sum, i) => sum + i.balance, 0),
  });

  // 計算總計
  const total = calcSubtotal(data);

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <Title level={4}>預算控制表</Title>
      <div className="flex items-center gap-2 mb-4">
        <Button type="default" onClick={() => {
          if (activeKeys.length === categories.length) setActiveKeys([]);
          else setActiveKeys(categories.map(c => c.key));
        }}>
          {activeKeys.length === categories.length ? '收合全部' : '全部展開'}
        </Button>
      </div>
      <Collapse
        ghost
        activeKey={activeKeys}
        onChange={(keys) => setActiveKeys(keys as string[])}
        items={categories.map(cat => {
          const itemsInCat = data.filter(d => d.category === cat.key);
          const sub = calcSubtotal(itemsInCat);
          return {
            key: cat.key,
            label: <span>{cat.name} (小計: {sub.budgetAmount.toLocaleString()} 元)</span>,
            children: (
              <>
                <div className="flex justify-start mb-2">
                  <Button
                    type="default"
                    size="large"
                    icon={<BarChartOutlined style={{ fontSize: 20 }} />}
                    onClick={() => handleOpenChart(cat.key, cat.name)}
                  >
                    圖表
                  </Button>
                </div>
                <Table
                  columns={columns}
                  dataSource={itemsInCat}
                  rowKey="id"
                  pagination={false}
                  summary={() => (
                    <Table.Summary.Row>
                      <Table.Summary.Cell index={0}>小計</Table.Summary.Cell>
                      <Table.Summary.Cell index={1} />
                      <Table.Summary.Cell index={2} align="right">
                        {sub.budgetAmount.toLocaleString()}
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={3} />
                      <Table.Summary.Cell index={4} align="right">
                        {sub.contractedAmount.toLocaleString()}
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={5} align="right">
                        {sub.invoicedAmount.toLocaleString()}
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={6} align="right">
                        {sub.recoverableAmount.toLocaleString()}
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={7} align="right">
                        {sub.balance.toLocaleString()}
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  )}
                />
              </>
            ),
          };
        })}
      />
      <Divider />
      <Table
        columns={columns}
        dataSource={[]}
        pagination={false}
        showHeader={false}
        summary={() => (
          <Table.Summary.Row>
            <Table.Summary.Cell index={0}>總計</Table.Summary.Cell>
            <Table.Summary.Cell index={1} />
            <Table.Summary.Cell index={2} align="right">
              {total.budgetAmount.toLocaleString()}
            </Table.Summary.Cell>
            <Table.Summary.Cell index={3} />
            <Table.Summary.Cell index={4} align="right">
              {total.contractedAmount.toLocaleString()}
            </Table.Summary.Cell>
            <Table.Summary.Cell index={5} align="right">
              {total.invoicedAmount.toLocaleString()}
            </Table.Summary.Cell>
            <Table.Summary.Cell index={6} align="right">
              {total.recoverableAmount.toLocaleString()}
            </Table.Summary.Cell>
            <Table.Summary.Cell index={7} align="right">
              {total.balance.toLocaleString()}
            </Table.Summary.Cell>
          </Table.Summary.Row>
        )}
      />
      {/* Chart Modal */}
      <Modal
        open={chartVisible}
        title={chartTitle}
        onCancel={() => setChartVisible(false)}
        footer={null}
        width={600}
      >
        <Chart
          type={ChartType.BAR}
          data={{
            labels: chartItems.map(ci => ci.item),
            datasets: [
              { label: '預算金額', data: chartItems.map(ci => ci.budgetAmount), backgroundColor: COLORS[0] },
              { label: '已請款金額', data: chartItems.map(ci => ci.invoicedAmount), backgroundColor: COLORS[1] },
            ],
          }}
          width={500}
          height={400}
          showDownload
          downloadFileName={`圖表-${chartTitle}`}
        />
      </Modal>
    </div>
  );
} 