'use client';

import React from 'react';
import ChartCard from '../../components/common/ChartCard';
import { ChartType } from '../../interfaces/dto/chart.dto';
import { chartDataService } from '../../services/chartDataService';
import EChart from '../../components/common/EChart';
import { Divider, Typography } from 'antd';
const { Title } = Typography;

export default function ChartsPage() {
  // 動態 ECharts 範例清單
  const echartExamples: { name: string; option: any }[] = [
    // 折線圖
    {
      name: '折線圖',
      option: {
        tooltip: { trigger: 'axis' },
        legend: { data: ['銷售額', '利潤'] },
        xAxis: { type: 'category', data: chartDataService.generateLineChartData().labels },
        yAxis: { type: 'value' },
        series: chartDataService.generateLineChartData().datasets.map(d => ({ name: d.label, type: 'line', data: d.data, smooth: true })),
      },
    },
    // 直條圖
    {
      name: '直條圖',
      option: {
        tooltip: {},
        legend: { data: ['已售出', '庫存'] },
        xAxis: { type: 'category', data: chartDataService.generateBarChartData().labels },
        yAxis: { type: 'value' },
        series: chartDataService.generateBarChartData().datasets.map(d => ({ name: d.label, type: 'bar', data: d.data })),
      },
    },
    // 餅圖
    {
      name: '餅圖',
      option: {
        tooltip: { trigger: 'item' },
        legend: { orient: 'vertical', left: 'left' },
        series: [
          { name: '來源', type: 'pie', radius: '50%', data: chartDataService.generatePieChartData().labels.map((l, i) => ({ name: l, value: chartDataService.generatePieChartData().datasets[0].data[i] })) }
        ],
      },
    },
    // 環形圖
    {
      name: '環形圖',
      option: {
        tooltip: { trigger: 'item' },
        legend: { orient: 'vertical', left: 'left' },
        series: [
          { name: '分配', type: 'pie', radius: ['40%', '70%'], data: chartDataService.generateDoughnutChartData().labels.map((l, i) => ({ name: l, value: chartDataService.generateDoughnutChartData().datasets[0].data[i] })) }
        ],
      },
    },
    // 雷達圖
    {
      name: '雷達圖',
      option: {
        tooltip: {},
        radar: { indicator: chartDataService.generateRadarChartData().labels.map(l => ({ name: l, max: 100 })) },
        series: [{ name: '性能', type: 'radar', data: [{ value: chartDataService.generateRadarChartData().datasets[0].data, name: 'A' }] }],
      },
    },
    // 極區圖
    {
      name: '極區圖',
      option: {
        angleAxis: {},
        radiusAxis: { type: 'category', data: chartDataService.generatePieChartData().labels },
        polar: {},
        series: [
          { type: 'bar', data: chartDataService.generatePieChartData().datasets[0].data, coordinateSystem: 'polar' }
        ]
      }
    },
    // 氣泡圖
    {
      name: '氣泡圖',
      option: {
        xAxis: {},
        yAxis: {},
        series: [{ type: 'scatter', symbolSize: 20, data: chartDataService.generateBarChartData().datasets[0].data.map((v, i) => [i, v]) }]
      }
    },
    // 面積圖
    {
      name: '面積圖',
      option: {
        tooltip: { trigger: 'axis' },
        legend: { data: chartDataService.generateLineChartData().datasets.map(d => d.label) },
        xAxis: { type: 'category', data: chartDataService.generateLineChartData().labels },
        yAxis: { type: 'value' },
        series: chartDataService.generateLineChartData().datasets.map(d => ({ name: d.label, type: 'line', data: d.data, areaStyle: {} }))
      }
    },
    // 橫向柱狀圖
    {
      name: '橫向柱狀圖',
      option: {
        tooltip: {},
        legend: { data: chartDataService.generateBarChartData().datasets.map(d => d.label) },
        xAxis: { type: 'value' },
        yAxis: { type: 'category', data: chartDataService.generateBarChartData().labels },
        series: chartDataService.generateBarChartData().datasets.map(d => ({ name: d.label, type: 'bar', data: d.data }))
      }
    },
    // 漏斗圖
    {
      name: '漏斗圖',
      option: {
        tooltip: { trigger: 'item' },
        series: [
          { type: 'funnel', data: chartDataService.generatePieChartData().labels.map((l, i) => ({ name: l, value: chartDataService.generatePieChartData().datasets[0].data[i] })) }
        ]
      }
    },
    // 儀表盤
    {
      name: '儀表盤',
      option: {
        series: [{ type: 'gauge', detail: { formatter: '{value}%' }, data: [{ value: 50, name: '完成率' }] }]
      }
    },
    // 熱力圖
    {
      name: '熱力圖',
      option: {
        xAxis: { type: 'category', data: ['A', 'B', 'C', 'D', 'E'] },
        yAxis: { type: 'category', data: ['V1', 'V2', 'V3'] },
        visualMap: { min: 0, max: 10, orient: 'horizontal', left: 'center', top: 20 },
        series: [{ type: 'heatmap', data: [[0,0,1], [1,1,5], [2,2,9]], label: { show: true } }]
      }
    },
    // 箱型圖
    {
      name: '箱型圖',
      option: {
        tooltip: { trigger: 'item' },
        dataset: [
          { source: [[850, 740, 900, 1070, 930], [960, 940, 960, 955, 975]] },
          { transform: { type: 'boxplot' } }
        ],
        xAxis: { type: 'category', boundaryGap: true },
        yAxis: { type: 'value' },
        series: [{ type: 'boxplot', datasetIndex: 1 }]
      }
    },
    // 蠟燭圖
    {
      name: '蠟燭圖',
      option: {
        xAxis: { type: 'category', data: ['Mon','Tue','Wed','Thu','Fri'] },
        yAxis: { type: 'value' },
        series: [{ type: 'candlestick', data: [[20,35,10,38],[40,35,30,50],[30,46,20,60],[30,10,5,40],[20,30,15,35]] }]
      }
    },
  ];

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* 折線圖 */}
        <ChartCard
          title="月度銷售趨勢"
          subtitle="過去12個月的銷售額和利潤"
          type={ChartType.LINE}
          data={chartDataService.generateLineChartData()}
          height={300}
          options={{
            plugins: {
              title: {
                display: true,
                text: '月度銷售趨勢',
                font: {
                  size: 16
                }
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: '金額 (萬元)'
                }
              },
              x: {
                title: {
                  display: true,
                  text: '月份'
                }
              }
            }
          }}
          footer={
            <div className="text-sm text-gray-500 flex justify-between">
              <span>數據來源: 銷售部門</span>
              <span>更新時間: {new Date().toLocaleDateString()}</span>
            </div>
          }
        />
        
        {/* 柱狀圖 */}
        <ChartCard
          title="案場銷售情況"
          subtitle="各案場已售出與庫存數量"
          type={ChartType.BAR}
          data={chartDataService.generateBarChartData()}
          height={300}
          options={{
            plugins: {
              title: {
                display: true,
                text: '案場銷售情況',
                font: {
                  size: 16
                }
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: '數量 (戶)'
                }
              }
            }
          }}
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* 餅圖 */}
        <ChartCard
          title="客戶來源分布"
          subtitle="按渠道統計的客戶來源"
          type={ChartType.PIE}
          data={chartDataService.generatePieChartData()}
          height={250}
        />
        
        {/* 環形圖 */}
        <ChartCard
          title="預算分配"
          subtitle="各部門預算分配比例"
          type={ChartType.DOUGHNUT}
          data={chartDataService.generateDoughnutChartData()}
          height={250}
        />
        
        {/* 雷達圖 */}
        <ChartCard
          title="業務員績效評估"
          subtitle="各項能力指標比較"
          type={ChartType.RADAR}
          data={chartDataService.generateRadarChartData()}
          height={250}
        />
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">圖表使用說明</h2>
        <p className="mb-4">
          這些圖表組件可以在整個應用程序中重複使用。您可以通過傳遞不同的數據和選項來自定義圖表的外觀和行為。
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium mb-2">支持的圖表類型：</h3>
            <ul className="list-disc list-inside space-y-1 text-gray-700">
              <li>折線圖 (Line Chart)</li>
              <li>柱狀圖 (Bar Chart)</li>
              <li>餅圖 (Pie Chart)</li>
              <li>環形圖 (Doughnut Chart)</li>
              <li>雷達圖 (Radar Chart)</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium mb-2">使用方法：</h3>
            <ol className="list-decimal list-inside space-y-1 text-gray-700">
              <li>導入 ChartCard 組件</li>
              <li>從 chartDataService 獲取數據或提供自定義數據</li>
              <li>選擇圖表類型並設置選項</li>
              <li>根據需要添加標題、副標題和頁腳</li>
            </ol>
          </div>
        </div>
      </div>
      
      <Divider />
      <Title level={4}>ECharts 圖表示例</Title>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
        {echartExamples.map((example, index) => (
          <EChart
            key={index}
            option={example.option}
            style={{ height: 300 }}
            showDownload
            downloadFileName={example.name}
          />
        ))}
      </div>
    </div>
  );
} 