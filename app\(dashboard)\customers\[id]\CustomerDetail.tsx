'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { customerApi } from '../../../services/api/customerApi';
import { Customer, CustomerStatus, UpdateCustomerDto } from '../../../interfaces/dto/customer.dto';
import EditModal from '../../../components/customer/EditModal';
import { message } from 'antd';
import { format } from 'date-fns';
import { Button } from 'antd';
import { ArrowLeftOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';

interface CustomerDetailProps {
  id: string;
}

export default function CustomerDetail({ id }: CustomerDetailProps) {
  const router = useRouter();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    const fetchCustomerDetail = async () => {
      setLoading(true);
      try {
        const customerData = await customerApi.getCustomerById(id);
        setCustomer(customerData);
      } catch (error: any) {
        console.error('Failed to fetch customer detail:', error);
        messageApi.error(error.message || '獲取客戶詳細資料失敗。');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchCustomerDetail();
    }
  }, [id, messageApi]);

  const handleSaveCustomer = async (editedCustomerData: Partial<UpdateCustomerDto>) => {
    if (isSaving) {
      return;
    }
    if (!customer) {
      return;
    }

    try {
      setIsSaving(true);
      
      let userSiteCode: string | undefined = undefined;
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const userData = JSON.parse(userStr);
          userSiteCode = userData?.currentUser?.ServiceUnit;
        }
      } catch (error) {
        console.error("Failed to parse user data from localStorage:", error);
      }

      const updateData: UpdateCustomerDto = {
        Name: editedCustomerData.Name ?? customer.Name,
        PhoneNumber: editedCustomerData.PhoneNumber ?? customer.PhoneNumber,
        ...(editedCustomerData.Gender !== undefined && { Gender: editedCustomerData.Gender }),
        ...(editedCustomerData.Birthday !== undefined && { Birthday: editedCustomerData.Birthday }),
        ...(editedCustomerData.City !== undefined && { City: editedCustomerData.City }),
        ...(editedCustomerData.District !== undefined && { District: editedCustomerData.District }),
        ...(editedCustomerData.Address !== undefined && { Address: editedCustomerData.Address }),
        ...(editedCustomerData.Email !== undefined && { Email: editedCustomerData.Email }),
        ...(editedCustomerData.Occupation !== undefined && { Occupation: editedCustomerData.Occupation }),
        ...(editedCustomerData.LeadSource !== undefined && { LeadSource: editedCustomerData.LeadSource }),
        ...(editedCustomerData.PurchaseConditions !== undefined && { PurchaseConditions: editedCustomerData.PurchaseConditions }),
        ...(editedCustomerData.RequiredPingArea !== undefined && { RequiredPingArea: editedCustomerData.RequiredPingArea }),
        ...(editedCustomerData.RequiredLayout !== undefined && { RequiredLayout: editedCustomerData.RequiredLayout }),
        ...(editedCustomerData.Budget !== undefined && { Budget: editedCustomerData.Budget }),
        ...(editedCustomerData.ImageBase64 !== undefined && { ImageBase64: editedCustomerData.ImageBase64 }),
        SiteCode: userSiteCode ?? customer.SiteCode,
        ...(editedCustomerData.CustomerRecords && { CustomerRecords: editedCustomerData.CustomerRecords }),
      };
      
      if (!updateData.Name || !updateData.PhoneNumber) {
        messageApi.error('姓名和電話是必填項。');
        setIsSaving(false);
        return;
      }

      const response = await customerApi.updateCustomer(customer.CustomerId, updateData);

      if (response.isSuccess) {
        const updatedCustomer = await customerApi.getCustomerById(customer.CustomerId);
        setCustomer(updatedCustomer);
        messageApi.success('客戶資料更新成功！');
        setIsEditModalOpen(false);
      } else {
        messageApi.error(response.message || '客戶資料更新失敗。');
      }
    } catch (error: any) {
      messageApi.error(error.message || '更新客戶資料時發生錯誤。');
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return (
      <div>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (!customer) {
    return <div className="text-center py-10">無法加載客戶資料或客戶不存在。</div>;
  }

  return (
    <div>
      {contextHolder}
      <div className="max-w-7xl mx-auto p-4">
        <div className="mb-4">
          <Button
            onClick={() => router.back()}
            icon={<ArrowLeftOutlined />}
          >
            返回
          </Button>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <div className="flex justify-between items-center bg-white rounded-lg shadow p-4">
              <h1 className="text-2xl font-bold text-gray-800">
                客戶詳情 - {customer.Name}
              </h1>
              <Button
                type="primary"
                onClick={() => setIsEditModalOpen(true)}
                icon={<EditOutlined />}
              >
                編輯
              </Button>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">基本資料</h2>
                <div className="text-sm text-gray-600"><span className="font-medium">建立時間：</span>{customer.CreatedTime ? format(new Date(customer.CreatedTime), 'yyyy-MM-dd HH:mm') : '-'}</div>
                <div className="text-sm text-gray-600"><span className="font-medium">最後更新時間：</span>{customer.UpdatedTime ? format(new Date(customer.UpdatedTime), 'yyyy-MM-dd HH:mm') : '-'}</div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div><label className="text-sm font-medium">姓名</label><p>{customer.Name}</p></div>
                <div><label className="text-sm font-medium">性別</label><p>{customer.Gender || '-'}</p></div>
                <div><label className="text-sm font-medium">生日</label><p>{customer.Birthday ? format(new Date(customer.Birthday), 'yyyy-MM-dd') : '-'}</p></div>
                <div><label className="text-sm font-medium">電話</label><p>{customer.PhoneNumber}</p></div>
                <div><label className="text-sm font-medium">Email</label><p>{customer.Email || '-'}</p></div>
                <div className="md:col-span-1"><label className="text-sm font-medium">地址</label><p>{`${customer.City || ''}${customer.District || ''}${customer.Address || ''}`}</p></div>
                <div><label className="text-sm font-medium">職業</label><p>{customer.Occupation || '-'}</p></div>
                <div><label className="text-sm font-medium">來源</label><p>{customer.LeadSource || '-'}</p></div>
                <div><label className="text-sm font-medium">所屬案場代碼</label><p>{customer.SiteCode || '-'}</p></div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold mb-4">需求資料</h2>
                <div className="grid grid-cols-1 gap-4">
                  <div><label className="text-sm font-medium">需求坪數</label><p>{customer.RequiredPingArea || '-'}</p></div>
                  <div><label className="text-sm font-medium">需求格局</label><p>{customer.RequiredLayout || '-'}</p></div>
                  <div><label className="text-sm font-medium">預算範圍</label><p>{customer.Budget || '-'}</p></div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold mb-4">業務資料</h2>
                <div className="grid grid-cols-1 gap-4">
                  <div className="md:col-span-1"><label className="text-sm font-medium">在意購屋條件</label><p>{customer.PurchaseConditions || '-'}</p></div>
                </div>
              </div>
            </div>

            {customer.ImagePath && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold mb-4">簽名</h2>
                <img 
                  src={customer.ImagePath} 
                  alt="客戶簽名" 
                  className="max-w-xs border border-gray-300 rounded"
                />
              </div>
            )}
          </div>

          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6 h-full">
              <h2 className="text-lg font-semibold mb-4">客戶記錄</h2>
              <div className="space-y-4">
                {(customer.CustomerRecords && customer.CustomerRecords.length > 0) ? (
                  customer.CustomerRecords.map((record) => (
                    <div key={record.CustomerRecordId} className="p-4 border border-gray-200 rounded-md">
                      <div className="flex justify-between items-start gap-4 mb-1">
                        <p className="text-gray-900 break-words flex-grow min-w-0">{record.Notes}</p>
                        <div className="text-sm text-gray-500 whitespace-nowrap flex-shrink-0">
                          {record.RecordedAt ? format(new Date(record.RecordedAt), 'yyyy-MM-dd') : '-'}
                        </div>
                      </div>
                      <div className="text-sm text-gray-600 flex justify-between items-center mt-1">
                        <div className="flex gap-4">
                          <span>類型: {record.RecordType}</span>
                          <span>級別: {record.CustomerLevel}</span>
                        </div>
                        <span className="whitespace-nowrap flex-shrink-0">記錄人: {record.HandledBy || '-'}</span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500">暫無客戶記錄。</p>
                )}
              </div>
            </div>
          </div>
        </div>

        <EditModal
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          customer={customer}
          onSave={handleSaveCustomer}
        />
      </div>
    </div>
  );
} 