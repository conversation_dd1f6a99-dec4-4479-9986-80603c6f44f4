'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { customerApi } from '../../services/api/customerApi';
import { BaseQueryParams, SortOrderInfo, SearchTermInfo } from '../../interfaces/dto/common.dto';
import { Customer, CustomerQueryDto } from '../../interfaces/dto/customer.dto';
import { format } from 'date-fns';
import { zhTW } from 'date-fns/locale';
import DataTable, { SearchInfo, ExtendedColumnType } from '../../components/DataTable';
import type { SorterResult, FilterValue } from 'antd/es/table/interface';
import { Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

const staticColumns: ExtendedColumnType<Customer>[] = [
  {
    title: '客戶姓名',
    dataIndex: 'Name',
    key: 'Name',
    sorter: true,
    allowSearch: true,
  },
  {
    title: '聯絡電話',
    dataIndex: 'PhoneNumber',
    key: 'PhoneNumber',
    sorter: false,
    allowSearch: false,
  },
  {
    title: '電子信箱',
    dataIndex: 'Email',
    key: 'Email',
    sorter: true,
    allowSearch: true,
    render: (Email) => Email || '-',
  },
  {
    title: '建立日期',
    dataIndex: 'CreatedTime',
    key: 'CreatedTime',
    sorter: true,
    allowSearch: false,
    render: (CreatedTime) => CreatedTime && !isNaN(new Date(CreatedTime).getTime()) ? format(new Date(CreatedTime), 'yyyy-MM-dd', { locale: zhTW }) : '-',
  },
  {
    title: '操作',
    key: 'action',
  },
];

const textFilterKeys = staticColumns.filter(c => c.allowSearch).map(c => c.key as string);

export default function CustomersPage() {
  const router = useRouter();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortOrderInfos, setSortOrderInfos] = useState<SortOrderInfo[] | undefined>(undefined);
  const [searchTermInfos, setSearchTermInfos] = useState<SearchTermInfo[] | undefined>(undefined);
  const [totalCount, setTotalCount] = useState(0);

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const apiParams: BaseQueryParams = {
        UsingPaging: true,
        PageIndex: currentPage,
        NumberOfPperPage: pageSize,
        SortOrderInfos: sortOrderInfos,
        SearchTermInfos: searchTermInfos,
      };
      
      const response = await customerApi.getCustomerList(apiParams);
      
      if (response && response.isSuccess && response.body) {
        setCustomers(Array.isArray(response.body.Detail) ? response.body.Detail : []);
        setTotalCount(response.body.RecordCount || 0);
      } else {
        console.error('API request failed or returned unexpected structure:', response);
        setError(response?.message || '無法獲取客戶列表，響應格式錯誤。');
        setCustomers([]);
        setTotalCount(0);
      }
    } catch (err) {
      setError('無法獲取客戶列表，請稍後再試。');
      setCustomers([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, sortOrderInfos, searchTermInfos]);

  useEffect(() => {
    fetchData();
  }, [currentPage, pageSize, sortOrderInfos, searchTermInfos]);

  const handleTablePageChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
  };
  
  const handleSortChange = (sorter: SorterResult<Customer> | SorterResult<Customer>[]) => {
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    let newSortOrderInfos: SortOrderInfo[] | undefined = undefined;
    if (currentSorter && currentSorter.order && currentSorter.field) { 
      newSortOrderInfos = [{
        SortField: currentSorter.field as string,
        SortOrder: currentSorter.order === 'ascend' ? 'asc' : 'desc',
      }];
    }
    
    const hasSortChanged = JSON.stringify(newSortOrderInfos) !== JSON.stringify(sortOrderInfos);

    if (hasSortChanged) {
        setSortOrderInfos(newSortOrderInfos);
        if (currentPage !== 1) {
           setCurrentPage(1); 
        }
    }
  };
  
  const handleFilterChange = (searchInfos: SearchInfo[]) => {
    const textSearchTerms = searchInfos
      .filter(info => staticColumns.find(col => col.key === info.SearchField)?.allowSearch === true)
      .map(info => ({
        SearchField: info.SearchField,
        SearchValue: info.SearchValue,
      }));
      
    const newSearchTermInfos = textSearchTerms.length > 0 ? textSearchTerms : undefined;

    const hasFilterChanged = JSON.stringify(newSearchTermInfos) !== JSON.stringify(searchTermInfos);
    
    if (hasFilterChanged) {
        setSearchTermInfos(newSearchTermInfos);
        if (currentPage !== 1) {
           setCurrentPage(1); 
        }
    }
  };

  const handleViewCustomer = (customerId: string) => {
    router.push(`/customers/${customerId}`);
  };

  const tableColumns = React.useMemo(() => {
    const columnsWithoutStatus = staticColumns.filter(col => col.key !== 'Status');
    const actionColumnIndex = columnsWithoutStatus.findIndex(col => col.key === 'action');
    const columnsWithAction = [...columnsWithoutStatus];
    if (actionColumnIndex !== -1) {
      columnsWithAction[actionColumnIndex] = {
        ...columnsWithAction[actionColumnIndex],
        render: (_, record) => (
          <Button type="link" onClick={() => handleViewCustomer(record.CustomerId)}>
            查看
          </Button>
        ),
      };
  }
    return columnsWithAction;
  }, [handleViewCustomer]);

  return (
      <div className="bg-white rounded-lg shadow">
        <div className="p-4">
        <div className="flex items-center mb-4">
          <Button 
            type="primary" 
            onClick={() => router.push('/customers/register')}
            icon={<PlusOutlined />}
          >
            新增客戶
          </Button>
        </div>
        
        {error && (
          <div className="mb-4 p-4 bg-red-100 text-red-700 border border-red-400 rounded">
            {error}
        </div>
        )}
        
        <DataTable<Customer>
          columns={tableColumns}
          dataSource={customers}
          loading={loading}
          total={totalCount}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handleTablePageChange}
          onSort={handleSortChange}
          onFilter={handleFilterChange}
          rowKey="CustomerId"
        />
      </div>
    </div>
  );
} 