'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import SignaturePad from '../../../components/common/SignaturePad';
import { customerApi } from '../../../services/api/customerApi';
import { crmOptionApi } from '../../../services/api/crmOptionApi';
import { CreateCustomerDto } from '../../../interfaces/dto/customer.dto';
import { message, Button } from 'antd';
import { taiwanDistricts } from '../../../utils/addressData';
import TableSelectModal from '../../../components/common/TableSelectModal';
import { DropdownItem } from '../../../interfaces/dto/common.dto';
import { ArrowLeftOutlined } from '@ant-design/icons';

interface CustomerRegisterFormData {
  Name: string;
  Gender: string;
  Birthday: string;
  Age: number;
  City: string;
  District: string;
  Address: string;
  PhoneNumber: string;
  Email: string;
  Occupation: string;
  LeadSource: string[];
  RequiredPingArea: string;
  RequiredLayout: string;
  Budget: string;
  Note?: string;
  ImageBase64?: string;
  SiteCode?: string;
}

// Define lead source options
const leadSourceOptions: DropdownItem[] = [
  { Value: "接待中心", Name: "接待中心" },
  { Value: "戶外看板", Name: "戶外看板" },
  { Value: "手舉牌", Name: "手舉牌" },
  { Value: "公車", Name: "公車" },
  { Value: "A 字板", Name: "A 字板" },
  { Value: "路邊 / 攔車派報", Name: "路邊 / 攔車派報" },
  { Value: "簡訊", Name: "簡訊" },
  { Value: "網路廣告", Name: "網路廣告" },
  { Value: "FB 名單", Name: "FB 名單" },
  { Value: "官網名單", Name: "官網名單" },
  { Value: "美學官網", Name: "美學官網" },
  { Value: "社群媒體 FB", Name: "社群媒體 FB" },
  { Value: "社交媒體 LINE", Name: "社交媒體 LINE" },
  { Value: "業務邀約", Name: "業務邀約" },
  { Value: "親友推薦", Name: "親友推薦" },
  { Value: "已購介紹", Name: "已購介紹" },
  { Value: "活動", Name: "活動" },
  { Value: "其他", Name: "其他" },
];

// Define occupation options
const occupationOptions: DropdownItem[] = [
    { Value: "海外經商", Name: "海外經商" },
    { Value: "自營商", Name: "自營商" },
    { Value: "軍公教", Name: "軍公教" },
    { Value: "科技業", Name: "科技業" },
    { Value: "製造業", Name: "製造業" },
    { Value: "貿易業", Name: "貿易業" },
    { Value: "醫療業", Name: "醫療業" },
    { Value: "醫律會計", Name: "醫律會計" },
    { Value: "建築業", Name: "建築業" },
    { Value: "傳播媒體", Name: "傳播媒體" },
    { Value: "交通運輸業", Name: "交通運輸業" },
    { Value: "直銷", Name: "直銷" },
    { Value: "家管", Name: "家管" },
    { Value: "電子商務", Name: "電子商務" },
    { Value: "金融業", Name: "金融業" },
    { Value: "服務業", Name: "服務業" },
    { Value: "自由業", Name: "自由業" },
    { Value: "餐飲業", Name: "餐飲業" },
    { Value: "紡織業", Name: "紡織業" },
    { Value: "化工業", Name: "化工業" },
    { Value: "文化出版", Name: "文化出版" },
    { Value: "觀光旅遊業", Name: "觀光旅遊業" },
    { Value: "退休", Name: "退休" },
  { Value: "其他", Name: "其他" },
];

export default function CustomerRegisterPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<CustomerRegisterFormData>({
    Name: '',
    Gender: '',
    Birthday: '',
    Age: 0,
    City: '',
    District: '',
    Address: '',
    PhoneNumber: '',
    Email: '',
    Occupation: '',
    LeadSource: [],
    RequiredPingArea: '',
    RequiredLayout: '',
    Budget: '',
    Note: '',
    ImageBase64: '',
    SiteCode: ''
  });
  const [submitting, setSubmitting] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [availableDistricts, setAvailableDistricts] = useState<string[]>([]);
  const [isLeadSourceModalOpen, setIsLeadSourceModalOpen] = useState(false);

  // CRM 選項狀態
  const [pingAreaOptions, setPingAreaOptions] = useState<DropdownItem[]>([]);
  const [layoutOptions, setLayoutOptions] = useState<DropdownItem[]>([]);
  const [budgetOptions, setBudgetOptions] = useState<DropdownItem[]>([]);

  useEffect(() => {
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const userData = JSON.parse(userStr);
        const serviceUnit = userData?.currentUser?.ServiceUnit;
        if (serviceUnit) {
          setFormData(prev => ({ ...prev, SiteCode: serviceUnit }));
          // 載入 CRM 選項
          loadCrmOptions(serviceUnit);
        } else {
          console.warn("用戶沒有指定的案場代號");
          // 如果沒有案場代號，可以考慮顯示警告或使用預設值
        }
      }
    } catch (error) {
      console.error("Failed to parse user data from localStorage:", error);
    }
  }, []);

  // 載入 CRM 選項
  const loadCrmOptions = async (siteCode: string) => {
    try {
      // 根據圖片顯示的正確參數，使用 CrmOptionTypeId
      const [pingAreaResponse, layoutResponse, budgetResponse] = await Promise.all([
        getCrmOptionsBySiteAndTypeId(siteCode, 1), // 需求坪數
        getCrmOptionsBySiteAndTypeId(siteCode, 2), // 需求格局
        getCrmOptionsBySiteAndTypeId(siteCode, 3)  // 預算範圍
      ]);

      setPingAreaOptions(pingAreaResponse);
      setLayoutOptions(layoutResponse);
      setBudgetOptions(budgetResponse);
    } catch (error) {
      console.error("載入 CRM 選項失敗:", error);
      messageApi.error("載入選項失敗，將使用預設選項");
      // 如果載入失敗，使用預設選項
      setDefaultOptions();
    }
  };

  // 根據案場代號和選項類型ID獲取 CRM 選項
  const getCrmOptionsBySiteAndTypeId = async (siteCode: string, crmOptionTypeId: number): Promise<DropdownItem[]> => {
    try {
      const response = await crmOptionApi.getCrmOptionDropdown({
        SiteCode: siteCode,
        CrmOptionTypeId: crmOptionTypeId,
        OnlyActive: true
      });

      if (response && Array.isArray(response)) {
        return response.map(item => ({
          Value: item.Name, // 使用 Name 作為 Value
          Name: item.Name
        }));
      }
      return [];
    } catch (error) {
      console.error(`載入 CrmOptionTypeId ${crmOptionTypeId} 選項失敗:`, error);
      return [];
    }
  };

  // 設定預設選項（當 API 失敗時使用）
  const setDefaultOptions = () => {
    setPingAreaOptions([
      { Value: "20-30坪", Name: "20-30坪" },
      { Value: "30-40坪", Name: "30-40坪" },
      { Value: "40-50坪", Name: "40-50坪" },
      { Value: "50坪以上", Name: "50坪以上" }
    ]);

    setLayoutOptions([
      { Value: "2房", Name: "2房" },
      { Value: "3房", Name: "3房" },
      { Value: "4房", Name: "4房" },
      { Value: "5房以上", Name: "5房以上" }
    ]);

    setBudgetOptions([
      { Value: "1000萬以下", Name: "1000萬以下" },
      { Value: "1000-1500萬", Name: "1000-1500萬" },
      { Value: "1500-2000萬", Name: "1500-2000萬" },
      { Value: "2000-2500萬", Name: "2000-2500萬" },
      { Value: "2500萬以上", Name: "2500萬以上" }
    ]);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === 'Birthday') {
      const today = new Date();
      const birthDate = new Date(value);
      let age = today.getFullYear() - birthDate.getFullYear();
      const m = today.getMonth() - birthDate.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      setFormData(prev => ({
        ...prev,
        Birthday: value,
        Age: age
      }));
    } else if (name === 'City') {
      const selectedCityData = taiwanDistricts.find(city => city.name === value);
      setAvailableDistricts(selectedCityData ? selectedCityData.districts : []);
      setFormData(prev => ({
        ...prev,
        City: value,
        District: ''
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSignatureSave = (signatureData: string) => {
    setFormData(prev => ({
      ...prev,
      ImageBase64: signatureData
    }));
  };

  // Helper function to get selected lead source names
  const getLeadSourceNames = (selectedValues: string[]): string => {
    if (!selectedValues || selectedValues.length === 0) return '請選擇';
    return selectedValues.join(', ');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    try {
      const createDto: CreateCustomerDto = {
        Name: formData.Name,
        Gender: formData.Gender,
        Birthday: formData.Birthday,
        City: formData.City,
        District: formData.District,
        Address: formData.Address,
        PhoneNumber: formData.PhoneNumber,
        Email: formData.Email || undefined,
        Occupation: formData.Occupation || undefined,
        LeadSource: formData.LeadSource.join(','),
        RequiredPingArea: formData.RequiredPingArea,
        RequiredLayout: formData.RequiredLayout,
        Budget: formData.Budget,
        Note: formData.Note || undefined,
        ImageBase64: formData.ImageBase64 || undefined,
        SiteCode: formData.SiteCode,
        CustomerRecords: []
      };

      console.log("Submitting customer data:", createDto);
      const response = await customerApi.createCustomer(createDto);

      if (response.isSuccess) {
        messageApi.success('客戶資料已成功送出！');
        router.push('/customers');
      } else {
        messageApi.error(response.message || '客戶資料送出失敗。');
      }
    } catch (error) {
      console.error('Failed to submit customer data:', error);
      messageApi.error('提交過程中發生錯誤。');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="p-4">
      {contextHolder}
      <div className="max-w-6xl mx-auto">
        <div className="mb-2">
          <Button
            onClick={() => router.back()}
            icon={<ArrowLeftOutlined />}
          >
            返回
          </Button>
        </div>
        
        <div className="mb-6">
          <h1 className="text-2xl font-bold bg-white rounded-lg shadow p-4 text-gray-800">美學生活</h1>
        </div>
        
        <p className="text-gray-700 mb-8">
          感謝您的蒞臨參觀，懇請撥冗協助填寫以下問券，讓我們能貼近您的購屋需求，以提供更好的住宅規劃服務。並煩請諮詢您加入美學之友，不定期享有專屬禮賓方案。
        </p>

        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
          <div className="space-y-6">
            <div className="text-lg font-semibold bg-blue-500 text-white p-3 rounded">基本資料</div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">姓名</label>
                <input
                  type="text"
                  name="Name"
                  value={formData.Name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">性別</label>
                <select
                  name="Gender"
                  value={formData.Gender}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">請選擇</option>
                  <option value="男">男</option>
                  <option value="女">女</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">出生日期</label>
                <input
                  type="date"
                  name="Birthday"
                  value={formData.Birthday}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">年齡</label>
                <input
                  type="number"
                  name="Age"
                  value={formData.Age}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">縣市</label>
                <select
                  name="City"
                  value={formData.City}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">請選擇</option>
                  {taiwanDistricts.map((city) => (
                    <option key={city.name} value={city.name}>
                      {city.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">區域</label>
                <select
                  name="District"
                  value={formData.District}
                  onChange={handleInputChange}
                  disabled={!formData.City}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
                >
                  <option value="">請選擇</option>
                  {availableDistricts.map((district) => (
                    <option key={district} value={district}>
                      {district}
                    </option>
                  ))}
                </select>
              </div>
              <div className="md:col-span-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">地址</label>
                <input
                  type="text"
                  name="Address"
                  value={formData.Address}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">聯絡電話</label>
                <input
                  type="tel"
                  name="PhoneNumber"
                  value={formData.PhoneNumber}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">電子信箱</label>
                <input
                  type="email"
                  name="Email"
                  value={formData.Email}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">職業</label>
                <select
                  name="Occupation"
                  value={formData.Occupation}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">請選擇</option>
                  {occupationOptions.map(option => (
                    <option key={option.Value} value={option.Value}>{option.Name}</option>
                  ))}
                </select>
              </div>
              <div className="md:col-span-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">您從何處得知本案訊息 (可複選)</label>
                <button
                  type="button"
                  onClick={() => setIsLeadSourceModalOpen(true)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-left hover:bg-gray-50 flex justify-between items-center h-10 min-h-[2.5rem]"
                >
                  <span className={`text-xs line-clamp-2 ${formData.LeadSource.length > 0 ? 'text-gray-900' : 'text-gray-500'}`}>
                    {getLeadSourceNames(formData.LeadSource)}
                  </span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="text-lg font-semibold bg-blue-500 text-white p-3 rounded mt-8">需求資料</div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">需求坪數</label>
                <select
                  name="RequiredPingArea"
                  value={formData.RequiredPingArea}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">請選擇</option>
                  {pingAreaOptions.map(option => (
                    <option key={option.Value} value={option.Value}>{option.Name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">需求格局</label>
                <select
                  name="RequiredLayout"
                  value={formData.RequiredLayout}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">請選擇</option>
                  {layoutOptions.map(option => (
                    <option key={option.Value} value={option.Value}>{option.Name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">預算範圍</label>
                <select
                  name="Budget"
                  value={formData.Budget}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">請選擇</option>
                  {budgetOptions.map(option => (
                    <option key={option.Value} value={option.Value}>{option.Name}</option>
                  ))}
                </select>
              </div>
              <div className="md:col-span-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">備註</label>
                <textarea
                  name="Note"
                  value={formData.Note}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                ></textarea>
              </div>
            </div>

            <div className="text-lg font-semibold bg-blue-500 text-white p-3 rounded mt-8">簽名</div>
            <div className="mt-4">
              <SignaturePad onSave={handleSignatureSave} />
            </div>

            <div className="flex justify-end mt-8">
              <Button
                htmlType="submit"
                type="primary"
                disabled={submitting}
                loading={submitting}
                className="px-6"
              >
                {submitting ? '提交中...' : '送出'}
              </Button>
            </div>
          </div>
        </form>
      </div>
      <TableSelectModal
        isOpen={isLeadSourceModalOpen}
        onClose={() => setIsLeadSourceModalOpen(false)}
        onSelect={(selectedItems: DropdownItem[]) => {
          setFormData(prev => ({ 
            ...prev, 
            LeadSource: selectedItems.map(item => item.Value) 
          }));
          setIsLeadSourceModalOpen(false);
        }}
        title="選擇訊息來源"
        data={leadSourceOptions}
        columns={[{ key: "Name", title: "來源" }]}
        rowKey="Value"
        isMultiSelect={true}
        initialSelectedValues={formData.LeadSource}
        searchKeys={["Name"]}
      />
    </div>
  );
} 