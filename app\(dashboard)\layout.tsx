'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Sidebar from '../components/layout/Sidebar';
import Breadcrumb from '../components/layout/Breadcrumb';
import { App, Avatar, Dropdown, Button, ConfigProvider } from 'antd';
import { UserOutlined, LogoutOutlined } from '@ant-design/icons';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // 取得使用者資訊
  const [userName, setUserName] = useState<string>('');
  const [userRole, setUserRole] = useState<string>('');

  const router = useRouter();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  useEffect(() => {
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const userData = JSON.parse(userStr);
        setUserName(userData.currentUser?.Name || '');
        setUserRole(userData.currentUser?.RoleName || '');
      }
    } catch {
      // ignore
    }
  }, []);

  // 登出處理
  const handleLogout = () => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('user');
    router.push('/login');
  };

  useEffect(() => {
    // 檢查是否已登入
    const token = localStorage.getItem('accessToken');
    if (!token) {
      router.push('/login');
    }
  }, [router]);

  useEffect(() => {
    const originalWarn = console.warn;
    const originalLog = console.log;
    const originalError = console.error;
    const warningToSuppress = "antd: compatible] antd v5 support React";

    console.warn = (...args: any[]) => {
      if (typeof args[0] === 'string' && args[0].includes(warningToSuppress)) {
        return;
      }
      originalWarn.apply(console, args);
    };

    console.log = (...args: any[]) => {
      if (typeof args[0] === 'string' && args[0].includes(warningToSuppress)) {
        return;
      }
      originalLog.apply(console, args);
    };

    console.error = (...args: any[]) => {
      if (
        typeof args[0] === 'string' &&
        args[0].includes(warningToSuppress)
      ) {
        return;
      }
      originalError.apply(console, args);
    };

    return () => {
      console.warn = originalWarn;
      console.log = originalLog;
      console.error = originalError;
    };
  }, []);

  const handleSidebarCollapse = (collapsed: boolean) => {
    setIsSidebarCollapsed(collapsed);
  };

  return (
    <App>
      <ConfigProvider
        theme={{
          components: {
            Message: {
              zIndexPopupBase: 1310,
            },
          },
        }}
      >
        <div className="min-h-screen bg-gray-100">
          <Sidebar onCollapse={handleSidebarCollapse} />
          <main className={`transition-all duration-300 ${
            isSidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'
          }`}>
            <div className="px-4 py-4">
              {/* 把 Breadcrumb 和用戶資訊放在同一行 */}
              <div className="flex items-center mb-4">
                <div className="flex-1">
                  <Breadcrumb />
                </div>
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'logout',
                        icon: <LogoutOutlined />,
                        label: '登出',
                        onClick: handleLogout,
                      },
                    ],
                  }}
                  placement="bottomRight"
                >
                  <div className="flex items-center cursor-pointer bg-white px-3 py-1 rounded shadow-sm">
                    <Avatar icon={<UserOutlined />} />
                    <div className="ml-2 text-left">
                      <div className="text-sm font-medium text-gray-800">{userName}</div>
                      <div className="text-xs text-gray-500">{userRole}</div>
                    </div>
                  </div>
                </Dropdown>
              </div>
              {children}
            </div>
          </main>
        </div>
      </ConfigProvider>
    </App>
  );
} 