'use client';

import React, { useState, useEffect } from 'react';
import { message, ConfigProvider, Input, Popconfirm, Tag, Select, Space, Radio, Checkbox, Button } from 'antd';
import type { RadioChangeEvent } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import { SearchOutlined, EditOutlined, DeleteOutlined, LockOutlined, UnlockOutlined, PlusOutlined } from '@ant-design/icons';
import zhTW from 'antd/lib/locale/zh_TW';
import Modal from '../../../components/common/Modal';
import DataTable from '../../../components/DataTable';
import { userInfoApi, UserInfoListItem, CreateUserInfoDto, UpdateUserInfoDto } from '../../../services/api/userInfoApi';
import { roleGroupApi } from '../../../services/api/roleGroupApi';
import { siteApi } from '../../../services/api/siteApi';
import { BaseQueryParams, DropdownItem, SortOrderInfo } from '../../../interfaces/dto/common.dto';
import { formatDateForInput } from '../../../utils/dateUtils';

export default function AccountsPage() {
  // 狀態管理
  const [users, setUsers] = useState<UserInfoListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingUserId, setEditingUserId] = useState<string | null>(null);
  const [messageApi, contextHolder] = message.useMessage();
  const [searchText, setSearchText] = useState('');

  // --- 新增：排序狀態 ---
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(null);
  // --- 結束新增 ---

  // 新增和編輯用戶的狀態
  const [newUser, setNewUser] = useState<CreateUserInfoDto>({
    UserInfoId: '',
    Name: '',
    Email: '',
    MobileNumber: '',
    Identity: '',
    DepartmentId: '',
    JobTitleId: '',
    CompanyId: '',
    ServiceUnit: '',
    Status: true,
    Gender: '',
    BirthDate: '',
    TelephoneNumber: '',
    RegisteredAddress: '',
    MailingAddress: '',
    EmergencyContactName: '',
    EmergencyContactPhone: '',
    EmergencyContactRelation: '',
    HireDate: '',
    IsInside: false,
    IsM365: false,
    IsEmailNotificationEnabled: false,
    RoleGroupIds: []
  });

  const [editUser, setEditUser] = useState<UpdateUserInfoDto>({
    UserInfoId: '',
    Name: '',
    Email: '',
    MobileNumber: '',
    Identity: '',
    DepartmentId: '',
    JobTitleId: '',
    CompanyId: '',
    ServiceUnit: '',
    Status: true,
    Gender: '',
    BirthDate: '',
    TelephoneNumber: '',
    RegisteredAddress: '',
    MailingAddress: '',
    EmergencyContactName: '',
    EmergencyContactPhone: '',
    EmergencyContactRelation: '',
    HireDate: '',
    IsInside: false,
    IsM365: false,
    IsEmailNotificationEnabled: false,
    RoleGroupIds: [],
    Password: ''
  });

  // 添加公司和案場選項數據
  const [companies, setCompanies] = useState<DropdownItem[]>([]);
  const [sites, setSites] = useState<DropdownItem[]>([]);
  const [departments, setDepartments] = useState<DropdownItem[]>([]);
  const [jobTitles, setJobTitles] = useState<DropdownItem[]>([]);
  const [roleGroups, setRoleGroups] = useState<DropdownItem[]>([]);
  const [locationTypeValue, setLocationTypeValue] = useState<'inside' | 'outside'>('inside');

  // 表格列定義
  const columns = [
    {
      title: '使用者帳號',
      dataIndex: 'UserInfoId',
      key: 'UserInfoId',
      sorter: true,
      allowSearch: true
    },
    {
      title: '姓名',
      dataIndex: 'Name',
      key: 'Name',
      sorter: true,
      allowSearch: true
    },
    {
      title: '部門',
      dataIndex: 'DepartmentName',
      key: 'DepartmentName',
      sorter: true,
      allowSearch: true
    },
    {
      title: '職稱',
      dataIndex: 'JobTitleName',
      key: 'JobTitleName',
      sorter: true,
      allowSearch: true
    },
    {
      title: '狀態',
      dataIndex: 'Status',
      key: 'Status',
      render: (status: boolean) => (
        <Tag color={status ? 'success' : 'error'}>
          {status ? '啟用' : '停用'}
        </Tag>
      ),
      sorter: false,
      allowSearch: false
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: UserInfoListItem) => (
        <Space size="middle">
          <Button
            type="link"
            onClick={() => handleEditClick(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            danger={record.Status}
            onClick={() => handleToggleStatus(record)}
          >
            {record.Status ? '停用' : '啟用'}
          </Button>
          <Popconfirm
            title="確定要刪除此使用者嗎？"
            description="刪除後無法恢復，請確認。"
            onConfirm={() => handleDeleteUser(record.UserInfoId)}
            okText="確定"
            cancelText="取消"
          >
            <Button type="link" danger>
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
      sorter: false,
      allowSearch: false
    },
  ];

  // 獲取使用者列表
  const fetchUsers = async (page = 1, size = pageSize, searchField?: string, searchValue?: string, currentSortField = sortField, currentSortOrder = sortOrder) => {
    setLoading(true);
    try {
      const params: BaseQueryParams = {
        UsingPaging: true,
        NumberOfPperPage: size,
        PageIndex: page,
      };
      if (searchField && searchValue) {
        params.SearchTermInfos = [{
          SearchField: searchField,
          SearchValue: searchValue
        }];
      }
      // 添加排序參數
      if (currentSortField && currentSortOrder) {
        params.SortOrderInfos = [{
           SortField: currentSortField,
           SortOrder: currentSortOrder === 'ascend' ? 'asc' : 'desc' // 轉換為 API 需要的 asc/desc
         }];
      }

      const response = await userInfoApi.getUserInfoList(params);

      if (!response.isSuccess || !response.body) { // 檢查 body 是否存在
        messageApi.error(response.message || '獲取使用者列表失敗');
        setUsers([]); // 清空數據
        setTotal(0);
        return; // 提前返回
      }

      setUsers(response.body.Detail || []); // 如果 Detail 為 nullish，則設為空陣列
      setTotal(response.body.RecordCount || 0);
      setCurrentPage(response.body.PageIndex || 1);
      setPageSize(response.body.NumberOfPperPage || 10);
       // 同步狀態
      setSortField(currentSortField);
      setSortOrder(currentSortOrder);
    } catch (error) {
      console.error('獲取使用者列表失敗:', error);
      messageApi.error('獲取使用者列表失敗');
      setUsers([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 獲取公司列表
  const fetchCompanies = async () => {
    try {
      const response = await userInfoApi.getCompanyDropdownList();

      if (response.isSuccess && response.body) {
        setCompanies(response.body);
      }
    } catch (error) {
      console.error('獲取公司列表失敗:', error);
    }
  };

  // 獲取部門列表
  const fetchDepartments = async (companyId?: string) => {
    try {
      const response = await userInfoApi.getDepartmentDropdownList(companyId);

      if (response.isSuccess && response.body) {
        setDepartments(response.body);
      }
    } catch (error) {
      console.error('獲取部門列表失敗:', error);
    }
  };

  // 獲取職稱列表
  const fetchJobTitles = async (departmentId?: string) => {
    try {
      const response = await userInfoApi.getJobTitleDropdownList(departmentId);

      if (response.isSuccess && response.body) {
        setJobTitles(response.body);
      }
    } catch (error) {
      console.error('獲取職稱列表失敗:', error);
    }
  };

  // 獲取角色清單 (修改: 接受 siteCode)
  const fetchRoleGroups = async (siteCode?: string) => {
    try {
      const response = await userInfoApi.getRoleGroupDropdownList(siteCode);

      if (response.isSuccess && response.body) {
        setRoleGroups(response.body);
      } else {
        setRoleGroups([]); // 清空列表
        messageApi.error(response.message || '獲取角色清單失敗');
      }
    } catch (error) {
      setRoleGroups([]); // 出錯時也清空列表
      messageApi.error('獲取角色清單失敗');
    }
  };

  // 獲取案場列表
  const fetchSites = async () => {
    try {
      const response = await siteApi.getSiteDropdownList();

      if (response.isSuccess && response.body) {
        setSites(response.body);
      }
    } catch (error) {
      console.error('獲取案場列表失敗:', error);
    }
  };

  // 處理排序變化的函數
  const handleSort = (sorter: any) => {
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    const newSortField = currentSorter?.field as string || null;
    const newSortOrder = currentSorter?.order as ('ascend' | 'descend' | null) || null;
    
    if (newSortField !== sortField || newSortOrder !== sortOrder) {
      setSortField(newSortField);
      setSortOrder(newSortOrder);
      // 獲取數據並重置頁碼到 1
      fetchUsers(1, pageSize, undefined, undefined, newSortField, newSortOrder);
    }
  };

  // 初始載入
  useEffect(() => {
    // 初始加載時也考慮排序狀態
    fetchUsers(currentPage, pageSize, undefined, undefined, sortField, sortOrder); 
    fetchCompanies();
    fetchSites();
  // 移除 fetchUsers 的依賴，避免重複觸發
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 處理頁面變化
  const handlePageChange = (page: number, size?: number) => {
    const newPageSize = size || pageSize; // Use provided size or current state
    // 只有在頁碼或每頁筆數實際改變時才重新獲取數據
    if (page !== currentPage || newPageSize !== pageSize) {
      fetchUsers(page, newPageSize, undefined, undefined, sortField, sortOrder);
    }
  };

  // 處理輸入變化 - 包含 Ant Design Checkbox (修正)
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement> | CheckboxChangeEvent,
    isEdit = false
  ) => {
    const setterFunction = isEdit ? setEditUser : setNewUser;
    const target = e.target as HTMLElement & { name?: string; value?: string; checked?: boolean; type?: string }; // 更明確的類型輔助

    if (!target.name) {
      console.warn('Input change event target missing name:', e);
      return; // 必須有 name 才能更新 state
    }

    const { name } = target;

    // 優先判斷是否為 Checkbox (包括 AntD 和原生)
    // AntD CheckboxChangeEvent 的 target 有 checked 屬性
    // 原生 Checkbox 的 target 有 type === 'checkbox' 和 checked 屬性
    if (target.checked !== undefined && (target.type === 'checkbox' || e.constructor.name === 'CheckboxChangeEvent')) {
      const checked = target.checked;
      setterFunction(prev => ({
        ...prev,
        [name]: checked
      }));
    } else if (target.value !== undefined) {
      // 其他標準輸入 (text, email, tel, date, select, textarea)
      const value = target.value;
      setterFunction(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // handleEditInputChange 保持不變，它調用 handleInputChange
  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement> | CheckboxChangeEvent) => {
    handleInputChange(e, true);
  };

  // 處理編輯點擊 (修改: 根據使用者資訊獲取初始角色組)
  const handleEditClick = async (user: UserInfoListItem) => {
    setEditingUserId(user.UserInfoId);
    const initialRoleGroupIds = user.RoleGroups.map(role => role.RoleGroupId);

    setEditUser({
      ...user,
      BirthDate: formatDateForInput(user.BirthDate), // 使用工具函數
      HireDate: formatDateForInput(user.HireDate),   // 使用工具函數
      RoleGroupIds: initialRoleGroupIds
    });
    setIsEditModalOpen(true);
    setLocationTypeValue(user.IsInside ? 'inside' : 'outside'); // 同步人員類型狀態

    // 載入公司/部門/職稱下拉選單
    if (user.CompanyId) {
      await fetchDepartments(user.CompanyId);
      if (user.DepartmentId) {
        await fetchJobTitles(user.DepartmentId);
      }
    }

    // 根據初始人員類型和案場獲取角色組
    if (!user.IsInside && user.ServiceUnit) {
      fetchRoleGroups(user.ServiceUnit);
    } else {
      fetchRoleGroups(); // 內場或無案場外場，獲取通用角色組
    }
  };

  // 處理刪除使用者
  const handleDeleteUser = async (userId: string) => {
    try {
      const response = await userInfoApi.deleteUserInfo(userId);

      if (!response.isSuccess) {
        messageApi.error(response.message || '刪除使用者失敗');
        return;
      }

      messageApi.success('刪除使用者成功');
      fetchUsers(currentPage, pageSize);
    } catch (error) {
      console.error('刪除使用者失敗:', error);
      messageApi.error('刪除使用者失敗');
    }
  };

  // 處理狀態切換
  const handleToggleStatus = async (user: UserInfoListItem) => {
    try {
      const response = await userInfoApi.updateUserStatus({
        UserInfoId: user.UserInfoId,
        Status: !user.Status
      });

      if (!response.isSuccess) {
        messageApi.error(response.message || '更新使用者狀態失敗');
        return;
      }

      messageApi.success('更新使用者狀態成功');
      fetchUsers(currentPage, pageSize);
    } catch (error) {
      console.error('更新使用者狀態失敗:', error);
      messageApi.error('更新使用者狀態失敗');
    }
  };

  // 處理位置類型變更 (新增) (修改: 獲取角色組並清空已選)
  const handleLocationTypeChange = (value: 'inside' | 'outside') => {
    setLocationTypeValue(value);
    const isInside = value === 'inside';
    setNewUser(prev => ({
      ...prev,
      IsInside: isInside,
      ServiceUnit: isInside ? '' : prev.ServiceUnit,
      RoleGroupIds: [] // 清空已選角色組
    }));
    if (isInside) {
      fetchRoleGroups(); // 內場，獲取通用角色組
    } else {
      setRoleGroups([]); // 切換到外場時先清空列表，等待選擇案場
    }
  };

  // 處理位置類型變更 (編輯) (修改: 獲取角色組並清空已選)
  const handleEditLocationTypeChange = (value: 'inside' | 'outside') => {
    setLocationTypeValue(value);
    const isInside = value === 'inside';
    setEditUser(prev => ({
      ...prev,
      IsInside: isInside,
      ServiceUnit: isInside ? '' : prev.ServiceUnit,
      RoleGroupIds: [] // 清空已選角色組
    }));
    if (isInside) {
      fetchRoleGroups(); // 內場，獲取通用角色組
    } else {
      setRoleGroups([]); // 切換到外場時先清空列表，等待選擇案場
    }
  };

  // 處理角色組變更
  const handleRoleGroupsChange = (values: string[]) => {
    setNewUser(prev => ({
      ...prev,
      RoleGroupIds: values
    }));
  };

  // 處理編輯角色組變更
  const handleEditRoleGroupsChange = (values: string[]) => {
    setEditUser(prev => ({
      ...prev,
      RoleGroupIds: values
    }));
  };

  // 處理公司選擇變更
  const handleCompanyChange = (value: string) => {
    // 更新新增用戶的公司ID
    setNewUser(prev => ({
      ...prev,
      CompanyId: value,
      // 清空部門和職稱，因為它們需要重新選擇
      DepartmentId: '',
      JobTitleId: ''
    }));

    // 獲取該公司的部門列表
    fetchDepartments(value);
    // 清空職稱列表
    setJobTitles([]);
  };

  // 處理部門選擇變更
  const handleDepartmentChange = (value: string) => {
    // 更新新增用戶的部門ID
    setNewUser(prev => ({
      ...prev,
      DepartmentId: value,
      // 清空職稱，因為它需要重新選擇
      JobTitleId: ''
    }));

    // 獲取該部門的職稱列表
    fetchJobTitles(value);
  };

  // 編輯用戶時的公司選擇變更
  const handleEditCompanyChange = (value: string) => {
    // 更新編輯用戶的公司ID
    setEditUser(prev => ({
      ...prev,
      CompanyId: value,
      // 清空部門和職稱，因為它們需要重新選擇
      DepartmentId: '',
      JobTitleId: ''
    }));

    // 獲取該公司的部門列表
    fetchDepartments(value);
    // 清空職稱列表
    setJobTitles([]);
  };

  // 編輯用戶時的部門選擇變更
  const handleEditDepartmentChange = (value: string) => {
    // 更新編輯用戶的部門ID
    setEditUser(prev => ({
      ...prev,
      DepartmentId: value,
      // 清空職稱，因為它需要重新選擇
      JobTitleId: ''
    }));

    // 獲取該部門的職稱列表
    fetchJobTitles(value);
  };

  // 提交新增使用者
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // 確保提交時 IsM365 和 IsEmailNotificationEnabled 為 false
      const dataToSubmit = {
        ...newUser,
        IsM365: false,
        IsEmailNotificationEnabled: false
      };
      const response = await userInfoApi.createUserInfo(dataToSubmit);

      if (!response.isSuccess) {
        messageApi.error(response.message || '建立使用者失敗');
        return;
      }

      messageApi.success('建立使用者成功');
      setIsModalOpen(false);
      setNewUser({
        UserInfoId: '',
        Name: '',
        Email: '',
        MobileNumber: '',
        Identity: '',
        DepartmentId: '',
        JobTitleId: '',
        CompanyId: '',
        ServiceUnit: '',
        Status: true,
        Gender: '',
        BirthDate: '',
        TelephoneNumber: '',
        RegisteredAddress: '',
        MailingAddress: '',
        EmergencyContactName: '',
        EmergencyContactPhone: '',
        EmergencyContactRelation: '',
        HireDate: '',
        IsInside: false,
        IsM365: false,
        IsEmailNotificationEnabled: false,
        RoleGroupIds: []
      });
      fetchUsers(1, pageSize);
    } catch (error) {
      messageApi.error('建立使用者失敗');
      console.error('Failed to create user:', error);
    }
  };

  // 提交編輯使用者
  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingUserId) return;

    try {
      // 確保提交時 IsM365 和 IsEmailNotificationEnabled 為 false
      const dataToSubmit = {
        ...editUser,
        IsM365: false,
        IsEmailNotificationEnabled: false,
        Password: editUser.Password || undefined // 如果密碼為空字串，不提交 Password 欄位
      };
      // 移除 Password 欄位如果它是 undefined
      if (dataToSubmit.Password === undefined) {
        delete dataToSubmit.Password;
      }

      const response = await userInfoApi.updateUserInfo(dataToSubmit);

      if (!response.isSuccess) {
        messageApi.error(response.message || '更新使用者失敗');
        return;
      }

      messageApi.success('更新使用者成功');
      setIsEditModalOpen(false);
      setEditingUserId(null);
      fetchUsers(currentPage, pageSize);
    } catch (error) {
      messageApi.error('更新使用者失敗');
      console.error('Failed to update user:', error);
    }
  };

  // 處理搜索
  const handleFilter = (searchInfos: { SearchField: string; SearchValue: string }[]) => {
    const searchInfo = searchInfos.length > 0 ? searchInfos[0] : null;
    // 觸發篩選時，重置頁碼到 1，並保持當前排序
    fetchUsers(1, pageSize, searchInfo?.SearchField, searchInfo?.SearchValue, sortField, sortOrder);
  };

  return (
    <ConfigProvider locale={zhTW}>
      {contextHolder}
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 flex justify-between items-center">
          <div className="mb-4">
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsModalOpen(true)}>
              新增帳號
            </Button>
          </div>
        </div>

        {/* 數據表格 */}
        <DataTable
          columns={columns}
          dataSource={users}
          loading={loading}
          total={total}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onFilter={handleFilter}
          onSort={handleSort}
          rowKey="UserInfoId"
        />

        {/* 新增使用者彈窗 */}
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title="新增使用者"
        >
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {/* 第一行：服務單位、部門、職稱 */}
              <div className="col-span-2 grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    服務單位
                  </label>
                  <Select
                    className="w-full"
                    placeholder="請選擇服務單位"
                    value={newUser.CompanyId}
                    onChange={handleCompanyChange}
                    options={companies.map(company => ({
                      value: company.Value,
                      label: company.Name
                    }))}
                    size="large"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    部門
                  </label>
                  <Select
                    className="w-full"
                    placeholder="請選擇部門"
                    value={newUser.DepartmentId}
                    onChange={handleDepartmentChange}
                    options={departments.map(dept => ({
                      value: dept.Value,
                      label: dept.Name
                    }))}
                    disabled={!newUser.CompanyId}
                    size="large"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    職稱
                  </label>
                  <Select
                    className="w-full"
                    placeholder="請選擇職稱"
                    value={newUser.JobTitleId}
                    onChange={(value) => setNewUser(prev => ({ ...prev, JobTitleId: value }))}
                    options={jobTitles.map(title => ({
                      value: title.Value,
                      label: title.Name
                    }))}
                    disabled={!newUser.DepartmentId}
                    size="large"
                  />
                </div>
              </div>
              {/* 其他表單項 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  使用者ID
                </label>
                <input
                  type="text"
                  name="UserInfoId"
                  value={newUser.UserInfoId}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  姓名
                </label>
                <input
                  type="text"
                  name="Name"
                  value={newUser.Name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  電子郵件
                </label>
                <input
                  type="email"
                  name="Email"
                  value={newUser.Email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  手機號碼
                </label>
                <input
                  type="tel"
                  name="MobileNumber"
                  value={newUser.MobileNumber}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  身分證字號
                </label>
                <input
                  type="text"
                  name="Identity"
                  value={newUser.Identity}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  人員類型
                </label>
                <Select
                  className="w-full"
                  placeholder="請選擇人員類型"
                  value={locationTypeValue}
                  onChange={handleLocationTypeChange}
                  options={[
                    { value: 'inside', label: '內場人員' },
                    { value: 'outside', label: '外場人員' }
                  ]}
                  size="large"
                />
              </div>
              {/* 根據人員類型條件顯示案場下拉選單 */}
              {locationTypeValue === 'outside' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    案場
                  </label>
                  <Select
                    className="w-full"
                    placeholder="請選擇案場"
                    value={newUser.ServiceUnit}
                    allowClear
                    onChange={(value) => {
                      const siteCode = value || '';
                      setNewUser(prev => ({
                        ...prev,
                        ServiceUnit: siteCode,
                        RoleGroupIds: []
                      }));
                      fetchRoleGroups(value);
                    }}
                    options={sites.map(site => ({
                      value: site.Value,
                      label: site.Name
                    }))}
                    size="large"
                  />
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  性別
                </label>
                <select
                  name="Gender"
                  value={newUser.Gender}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 h-10"
                >
                  <option value="">請選擇</option>
                  <option value="M">男</option>
                  <option value="F">女</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  出生日期
                </label>
                <input
                  type="date"
                  name="BirthDate"
                  value={newUser.BirthDate}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  電話號碼
                </label>
                <input
                  type="tel"
                  name="TelephoneNumber"
                  value={newUser.TelephoneNumber}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  戶籍地址
                </label>
                <input
                  type="text"
                  name="RegisteredAddress"
                  value={newUser.RegisteredAddress}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  通訊地址
                </label>
                <input
                  type="text"
                  name="MailingAddress"
                  value={newUser.MailingAddress}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  緊急聯絡人姓名
                </label>
                <input
                  type="text"
                  name="EmergencyContactName"
                  value={newUser.EmergencyContactName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  緊急聯絡人電話
                </label>
                <input
                  type="tel"
                  name="EmergencyContactPhone"
                  value={newUser.EmergencyContactPhone}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  與緊急聯絡人關係
                </label>
                <input
                  type="text"
                  name="EmergencyContactRelation"
                  value={newUser.EmergencyContactRelation}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  到職日期
                </label>
                <input
                  type="date"
                  name="HireDate"
                  value={newUser.HireDate}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  角色組
                </label>
                <Select
                  className="w-full"
                  placeholder="請選擇角色組"
                  mode="multiple"
                  value={newUser.RoleGroupIds}
                  onChange={handleRoleGroupsChange}
                  options={roleGroups.map(role => ({
                    value: role.Value,
                    label: role.Name
                  }))}
                  size="large"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center">
                <Checkbox
                  name="Status"
                  checked={newUser.Status}
                  onChange={handleInputChange}
                >
                  <span className="ml-2 text-sm text-gray-700">啟用帳號</span>
                </Checkbox>
              </label>
              {/* <label className="flex items-center">
                <Checkbox
                  name="IsAdmin"
                  checked={newUser.IsAdmin}
                  onChange={handleInputChange}
                >
                  <span className="ml-2 text-sm text-gray-700">管理員權限</span>
                </Checkbox>
              </label> */}
              {/* <label className="flex items-center">
                <Checkbox
                  name="IsM365"
                  checked={newUser.IsM365}
                  onChange={handleInputChange}
                >
                  <span className="ml-2 text-sm text-gray-700">M365帳號</span>
                </Checkbox>
              </label>
              <label className="flex items-center">
                <Checkbox
                  name="IsEmailNotificationEnabled"
                  checked={newUser.IsEmailNotificationEnabled}
                  onChange={handleInputChange}
                >
                  <span className="ml-2 text-sm text-gray-700">啟用郵件通知</span>
                </Checkbox>
              </label> */}
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600"
              >
                確認
              </button>
            </div>
          </form>
        </Modal>

        {/* 編輯使用者彈窗 */}
        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title="編輯使用者"
        >
          <form onSubmit={handleEditSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {/* 第一行：服務單位、部門、職稱 */}
              <div className="col-span-2 grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    服務單位
                  </label>
                  <Select
                    className="w-full"
                    placeholder="請選擇服務單位"
                    value={editUser.CompanyId}
                    onChange={handleEditCompanyChange}
                    options={companies.map(company => ({
                      value: company.Value,
                      label: company.Name
                    }))}
                    size="large"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    部門
                  </label>
                  <Select
                    className="w-full"
                    placeholder="請選擇部門"
                    value={editUser.DepartmentId}
                    onChange={handleEditDepartmentChange}
                    options={departments.map(dept => ({
                      value: dept.Value,
                      label: dept.Name
                    }))}
                    disabled={!editUser.CompanyId}
                    size="large"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    職稱
                  </label>
                  <Select
                    className="w-full"
                    placeholder="請選擇職稱"
                    value={editUser.JobTitleId}
                    onChange={(value) => setEditUser(prev => ({ ...prev, JobTitleId: value }))}
                    options={jobTitles.map(title => ({
                      value: title.Value,
                      label: title.Name
                    }))}
                    disabled={!editUser.DepartmentId}
                    size="large"
                  />
                </div>
              </div>
              {/* 其他表單項 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  使用者ID
                </label>
                <input
                  type="text"
                  name="UserInfoId"
                  value={editUser.UserInfoId}
                  onChange={handleEditInputChange}
                  required
                  disabled
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  姓名
                </label>
                <input
                  type="text"
                  name="Name"
                  value={editUser.Name}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  電子郵件
                </label>
                <input
                  type="email"
                  name="Email"
                  value={editUser.Email}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  手機號碼
                </label>
                <input
                  type="tel"
                  name="MobileNumber"
                  value={editUser.MobileNumber}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  身分證字號
                </label>
                <input
                  type="text"
                  name="Identity"
                  value={editUser.Identity}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  人員類型
                </label>
                <Select
                  className="w-full"
                  placeholder="請選擇人員類型"
                  value={editUser.IsInside ? 'inside' : 'outside'}
                  onChange={handleEditLocationTypeChange}
                  options={[
                    { value: 'inside', label: '內場人員' },
                    { value: 'outside', label: '外場人員' }
                  ]}
                  size="large"
                />
              </div>
              {/* 根據人員類型條件顯示案場下拉選單 */}
              {!editUser.IsInside && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    案場
                  </label>
                  <Select
                    className="w-full"
                    placeholder="請選擇案場"
                    value={editUser.ServiceUnit}
                    allowClear
                    onChange={(value) => {
                      const siteCode = value || '';
                      setEditUser(prev => ({
                        ...prev,
                        ServiceUnit: siteCode,
                        RoleGroupIds: []
                      }));
                      fetchRoleGroups(value);
                    }}
                    options={sites.map(site => ({
                      value: site.Value,
                      label: site.Name
                    }))}
                    size="large"
                  />
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  性別
                </label>
                <select
                  name="Gender"
                  value={editUser.Gender}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 h-10"
                >
                  <option value="">請選擇</option>
                  <option value="M">男</option>
                  <option value="F">女</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  出生日期
                </label>
                <input
                  type="date"
                  name="BirthDate"
                  value={editUser.BirthDate}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  電話號碼
                </label>
                <input
                  type="tel"
                  name="TelephoneNumber"
                  value={editUser.TelephoneNumber}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  戶籍地址
                </label>
                <input
                  type="text"
                  name="RegisteredAddress"
                  value={editUser.RegisteredAddress}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  通訊地址
                </label>
                <input
                  type="text"
                  name="MailingAddress"
                  value={editUser.MailingAddress}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  緊急聯絡人姓名
                </label>
                <input
                  type="text"
                  name="EmergencyContactName"
                  value={editUser.EmergencyContactName}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  緊急聯絡人電話
                </label>
                <input
                  type="tel"
                  name="EmergencyContactPhone"
                  value={editUser.EmergencyContactPhone}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  與緊急聯絡人關係
                </label>
                <input
                  type="text"
                  name="EmergencyContactRelation"
                  value={editUser.EmergencyContactRelation}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  到職日期
                </label>
                <input
                  type="date"
                  name="HireDate"
                  value={editUser.HireDate}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  角色組
                </label>
                <Select
                  className="w-full"
                  placeholder="請選擇角色組"
                  mode="multiple"
                  value={editUser.RoleGroupIds}
                  onChange={handleEditRoleGroupsChange}
                  options={roleGroups.map(role => ({
                    value: role.Value,
                    label: role.Name
                  }))}
                  size="large"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center">
                <Checkbox
                  name="Status"
                  checked={editUser.Status}
                  onChange={handleEditInputChange}
                >
                  <span className="ml-2 text-sm text-gray-700">啟用帳號</span>
                </Checkbox>
              </label>
              {/* <label className="flex items-center">
                <Checkbox
                  name="IsAdmin"
                  checked={editUser.IsAdmin}
                  onChange={handleEditInputChange}
                >
                  <span className="ml-2 text-sm text-gray-700">管理員權限</span>
                </Checkbox>
              </label> */}
              {/* <label className="flex items-center">
                <Checkbox
                  name="IsM365"
                  checked={editUser.IsM365}
                  onChange={handleEditInputChange}
                >
                  <span className="ml-2 text-sm text-gray-700">M365帳號</span>
                </Checkbox>
              </label>
              <label className="flex items-center">
                <Checkbox
                  name="IsEmailNotificationEnabled"
                  checked={editUser.IsEmailNotificationEnabled}
                  onChange={handleEditInputChange}
                >
                  <span className="ml-2 text-sm text-gray-700">啟用郵件通知</span>
                </Checkbox>
              </label> */}
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={() => setIsEditModalOpen(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600"
              >
                確認
              </button>
            </div>
          </form>
        </Modal>
      </div>
    </ConfigProvider>
  );
} 