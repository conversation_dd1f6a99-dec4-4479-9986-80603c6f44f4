'use client';

import React, { useState, useEffect } from 'react';
import { message, ConfigProvider, Input, Popconfirm, Tag, Select, Tree, Button } from 'antd';
import type { TreeProps } from 'antd/es/tree';
import { SearchOutlined, EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import zhTW from 'antd/lib/locale/zh_TW';
import Modal from '../../../components/common/Modal';
import DataTable from '../../../components/DataTable';
import TableSelectModal from '../../../components/common/TableSelectModal';
import { roleGroupApi, RoleGroupDetailItem, CreateRoleGroupDto, UpdateRoleGroupPermissionDto, PermissionTreeNode } from '../../../services/api/roleGroupApi';
import { siteApi } from '../../../services/api/siteApi';
import { userInfoApi } from '../../../services/api/userInfoApi';
import { BaseQueryParams, DropdownItem } from '../../../interfaces/dto/common.dto';

// 權限列表
const availablePermissions = [
  '客戶管理',
  '銷售管理',
  '報表查看',
  '預算管理',
  '行政管理',
  '權限管理',
  '系統設定'
];

// 本地狀態接口
interface RoleFormState {
  Name: string;
  Permissions: string[];
  SiteCode: string;
  UserIds: string[];
  PermissionTree?: PermissionTreeNode[];
}

export default function RolePermissionsPage() {
  // 狀態管理
  const [roles, setRoles] = useState<RoleGroupDetailItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingRoleId, setEditingRoleId] = useState<string | null>(null);
  const [messageApi, contextHolder] = message.useMessage();
  const [searchText, setSearchText] = useState('');
  
  // 新增案場下拉選單狀態
  const [sites, setSites] = useState<DropdownItem[]>([]);
  
  // 新增人員下拉選單狀態
  const [users, setUsers] = useState<DropdownItem[]>([]);
  
  // 新增角色和編輯角色的狀態
  const [newRole, setNewRole] = useState<RoleFormState>({
    Name: '',
    Permissions: [],
    SiteCode: '',
    UserIds: []
  });

  const [editRole, setEditRole] = useState<RoleFormState>({
    Name: '',
    Permissions: [],
    SiteCode: '',
    UserIds: []
  });

  // --- 新增：使用者選擇 Modal 狀態 ---
  const [isUserSelectModalOpen, setIsUserSelectModalOpen] = useState(false);
  const [userSelectTargetRole, setUserSelectTargetRole] = useState<'new' | 'edit' | null>(null);
  // --- 結束新增 ---

  // API 实例
  const api = roleGroupApi;

  // 表格列定义
  const columns = [
    {
      title: '角色名稱',
      dataIndex: 'Name',
      key: 'Name',
      sorter: true,
      allowSearch: true
    },
    {
      title: '案場',
      dataIndex: 'SiteName',
      key: 'SiteName',
      sorter: true,
      allowSearch: true
    },
    {
      title: '使用人數',
      dataIndex: 'UserIds',
      key: 'UserCount',
      render: (userIds: string[]) => `${userIds?.length || 0} 人`,
      sorter: false,
      allowSearch: false
    },
    {
      title: '操作',
      key: 'action',
      sorter: false,
      allowSearch: false,
      render: (_: any, record: RoleGroupDetailItem) => (
        <div className="flex space-x-2">
          <Button
            type="link"
            onClick={() => handleEditClick(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定要刪除此角色嗎？"
            description="刪除後無法恢復，請確認。"
            onConfirm={() => handleDeleteRole(record.RoleGroupId)}
            okText="確定"
            cancelText="取消"
            disabled={record.UserIds?.length > 0}
          >
            <Button
              type="link"
              danger
              disabled={record.UserIds?.length > 0}
              title={record.UserIds?.length > 0 ? "有用戶使用此角色，無法刪除" : ""}
            >
              刪除
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ];

  // 獲取角色列表
  const fetchRoles = async (page = currentPage, size = pageSize, searchField?: string, searchValue?: string) => {
    setLoading(true);
    try {
      console.log('正在請求數據，參數:', { page, size, searchField, searchValue });
      
      const params: BaseQueryParams = {
        UsingPaging: true,
        NumberOfPperPage: size,
        PageIndex: page,
      };
      
      // 添加搜索條件
      if (searchField && searchValue) {
        params.SearchTermInfos = [{
          SearchField: searchField,
          SearchValue: searchValue
        }];
      }
      
      console.log('發送請求參數:', JSON.stringify(params));
      
      const response = await api.getRoleGroupList(params);
      
      console.log('角色列表返回數據:', JSON.stringify(response, null, 2));
      
      if (!response.isSuccess || !response.body?.Detail?.length) {
        messageApi.error(response.message || '獲取角色列表失敗');
        return;
      }
      
      setRoles(response.body.Detail);
      setTotal(response.body.RecordCount);
      setCurrentPage(response.body.PageIndex);
      setPageSize(response.body.NumberOfPperPage);
      
      console.log('設置數據後的狀態:', {
        roles: response.body.Detail.length,
        total: response.body.RecordCount,
        currentPage: response.body.PageIndex,
        pageSize: response.body.NumberOfPperPage
      });
    } catch (error) {
      console.error('獲取角色列表失敗:', error);
      messageApi.error('獲取角色列表失敗');
    } finally {
      setLoading(false);
    }
  };

  // 獲取案場下拉選單
  const fetchSites = async () => {
    try {
      const response = await siteApi.getSiteDropdownList();
      if (response.isSuccess) {
        setSites(response.body);
      } else {
        messageApi.error('獲取案場列表失敗');
      }
    } catch (error) {
      console.error('獲取案場列表失敗:', error);
      messageApi.error('獲取案場列表失敗');
    }
  };

  // 獲取人員下拉選單
  const fetchUsers = async () => {
    try {
      const response = await userInfoApi.getUserInfoDropdownList();
      if (response.isSuccess) {
        setUsers(response.body);
      } else {
        messageApi.error('獲取人員列表失敗');
      }
    } catch (error) {
      console.error('獲取人員列表失敗:', error);
      messageApi.error('獲取人員列表失敗');
    }
  };

  // 初始載入
  useEffect(() => {
    fetchSites();
    fetchUsers();
    fetchRoles();
    
    // 獲取權限樹
    api.getMenuTree().then(response => {
      if (response.isSuccess && response.body) {
        setNewRole(prev => ({
          ...prev,
          PermissionTree: response.body
        }));
      }
    }).catch(error => {
      console.error('Failed to fetch permission tree:', error);
    });
  }, []);

  // 處理頁面變化
  const handlePageChange = (page: number, pageSize?: number) => {
    if (pageSize) {
      fetchRoles(page, pageSize);
    } else {
      fetchRoles(page);
    }
  };

  // 處理輸入變化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, isEdit = false) => {
    const { name, value } = e.target;
    const setterFunction = isEdit ? setEditRole : setNewRole;
    
    setterFunction(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    handleInputChange(e, true);
  };

  // 處理權限切換
  const handlePermissionToggle = (checkedKeys: any, isEdit = false) => {
    const setterFunction = isEdit ? setEditRole : setNewRole;
    const keys = Array.isArray(checkedKeys) ? checkedKeys : checkedKeys.checked;
    setterFunction(prev => ({
      ...prev,
      Permissions: keys.map(String)
    }));
  };

  // 將 PermissionTree 轉換為 Ant Design Tree 需要的格式
  const convertToTreeData = (permissionTree: PermissionTreeNode[]) => {
    return permissionTree.map(node => ({
      title: node.Name,
      key: node.Id,
      children: node.Children.length > 0 ? convertToTreeData(node.Children) : undefined
    }));
  };

  // 在表單中替換原有的權限選擇部分
  const renderPermissionTree = (isEdit: boolean) => {
    const role = isEdit ? editRole : newRole;
    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          權限
        </label>
        <Tree
          checkable
          className="bg-white border border-gray-300 rounded-md p-4"
          checkedKeys={role.Permissions}
          onCheck={(checkedKeys) => handlePermissionToggle(
            checkedKeys,
            isEdit
          )}
          treeData={convertToTreeData(role.PermissionTree || [])}
        />
      </div>
    );
  };

  // 處理案場選擇變化
  const handleSiteChange = (value: string, isEdit = false) => {
    const setterFunction = isEdit ? setEditRole : setNewRole;
    setterFunction(prev => ({
      ...prev,
      SiteCode: value
    }));
  };

  // --- 新增：輔助函數和 Modal 開啟函數 ---
  const findUserNames = (userIds: string[]): string => {
    if (!userIds || userIds.length === 0) return '';
    return userIds
      .map(id => users.find(u => u.Value === id)?.Name || id)
      .filter(name => name)
      .join(', ');
  };

  const openUserSelectModal = (target: 'new' | 'edit') => {
    setUserSelectTargetRole(target);
    setIsUserSelectModalOpen(true);
  };
  // --- 結束新增 ---

  // 處理編輯點擊
  const handleEditClick = async (role: RoleGroupDetailItem) => {
    setEditingRoleId(role.RoleGroupId);
    
    try {
      const response = await api.getRoleGroupById(role.RoleGroupId);
      
      if (!response.isSuccess) {
        messageApi.error(response.message || '獲取角色詳情失敗');
        return;
      }
      
      const roleDetail = response.body;
      
      // 收集所有 Selected=true 的節點 id（含子節點）
      const selectedIds: string[] = [];
      const traverse = (nodes: PermissionTreeNode[] = []) => {
        nodes.forEach(node => {
          if (node.Selected) selectedIds.push(node.Id);
          if (node.Children && node.Children.length > 0) traverse(node.Children);
        });
      };
      traverse(roleDetail.PermissionTree || []);
      setEditRole({
        Name: roleDetail.Name,
        Permissions: selectedIds,
        SiteCode: roleDetail.SiteCode,
        UserIds: roleDetail.UserIds || [],
        PermissionTree: roleDetail.PermissionTree,
      });
      
      setIsEditModalOpen(true);
    } catch (error) {
      messageApi.error('獲取角色詳情失敗');
      console.error('Failed to fetch role details:', error);
    }
  };

  // 處理刪除角色
  const handleDeleteRole = async (roleId: string) => {
    try {
      const response = await api.deleteRoleGroup(roleId);
      
      if (!response.isSuccess) {
        messageApi.error(response.message || '角色刪除失敗');
        return;
      }
      
      messageApi.success('角色刪除成功');
      fetchRoles(currentPage, pageSize);
    } catch (error) {
      messageApi.error('角色刪除失敗');
      console.error('Failed to delete role:', error);
    }
  };

  // 處理排序
  const handleSort = (sorter: any) => {
    if (sorter && sorter.field && sorter.order) {
      const params: BaseQueryParams = {
        UsingPaging: true,
        NumberOfPperPage: pageSize,
        PageIndex: currentPage,
        SortOrderInfos: [{
          SortField: sorter.field === 'name' ? 'Name' : 
                     sorter.field === 'userCount' ? 'UserCount' : sorter.field,
          SortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'
        }]
      };
      
      if (searchText) {
        params.SearchTermInfos = [{
          SearchField: 'Name',
          SearchValue: searchText
        }];
      }
      
      api.getRoleGroupList(params)
        .then(response => {
          if (response.isSuccess && response.body) {
            setRoles(response.body.Detail);
            setTotal(response.body.RecordCount);
          } else {
            messageApi.error(response.message || '獲取角色列表失敗');
          }
        })
        .catch(error => {
          messageApi.error('獲取角色列表失敗');
          console.error('Failed to fetch roles:', error);
        });
    }
  };

  // 處理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
    if (value) {
      fetchRoles(1, pageSize, 'Name', value);
    } else {
      fetchRoles(1, pageSize);
    }
  };

  // 處理搜索
  const handleFilter = (searchInfos: { SearchField: string; SearchValue: string }[]) => {
    if (searchInfos.length > 0) {
      const searchInfo = searchInfos[0];
      fetchRoles(1, pageSize, searchInfo.SearchField, searchInfo.SearchValue);
    } else {
      fetchRoles(1, pageSize);
    }
  };

  // 提交新增角色
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const data: CreateRoleGroupDto = {
        Name: newRole.Name,
        Permissions: newRole.Permissions.join(','),
        SiteCode: newRole.SiteCode,
        UserIds: newRole.UserIds
      };
      
      const response = await api.createRoleGroup(data);
      
      if (!response.isSuccess) {
        messageApi.error(response.message || '角色建立失敗');
        return;
      }
      
      messageApi.success('角色建立成功');
      setIsModalOpen(false);
      setNewRole({
        Name: '',
        Permissions: [],
        SiteCode: '',
        UserIds: [],
        PermissionTree: []
      });
      fetchRoles(1, pageSize);
    } catch (error) {
      messageApi.error('角色建立失敗');
      console.error('Failed to create role:', error);
    }
  };

  // 提交編輯角色
  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (editingRoleId === null) return;
    
    try {
      const data: UpdateRoleGroupPermissionDto = {
        Id: editingRoleId,
        Name: editRole.Name,
        Permissions: editRole.Permissions.join(','),
        UserIds: editRole.UserIds
      };
      
      const response = await api.updateRoleGroupPermission(data);
      
      if (!response.isSuccess) {
        messageApi.error(response.message || '角色更新失敗');
        return;
      }
      
      messageApi.success('角色更新成功');
      setIsEditModalOpen(false);
      setEditingRoleId(null);
      fetchRoles(currentPage, pageSize);
    } catch (error) {
      messageApi.error('角色更新失敗');
      console.error('Failed to update role:', error);
    }
  };

  return (
    <ConfigProvider locale={zhTW}>
      {contextHolder}
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 flex justify-between items-center">
          <div className="mb-4">
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsModalOpen(true)}>
              新增角色
            </Button>
          </div>
          
          <div className="w-64">
            <Input
              placeholder="搜索角色名稱"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => {
                setSearchText(e.target.value);
                if (!e.target.value) {
                  fetchRoles(1, pageSize);
                }
              }}
              onPressEnter={(e) => handleSearch(e.currentTarget.value)}
              allowClear
            />
          </div>
        </div>
        
        {/* 數據表格 */}
        <DataTable
          columns={columns}
          dataSource={roles}
          loading={loading}
          total={total}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onSort={handleSort}
          onFilter={handleFilter}
          rowKey="RoleGroupId"
        />

        {/* 新增角色彈窗 */}
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title="新增角色"
        >
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                案場
              </label>
              <Select
                className="w-full"
                placeholder="請選擇案場"
                value={newRole.SiteCode}
                onChange={(value) => handleSiteChange(value)}
                options={sites.map(site => ({
                  label: site.Name,
                  value: site.Value
                }))}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                角色名稱
              </label>
              <input
                type="text"
                name="Name"
                value={newRole.Name}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                人員
              </label>
              <button
                type="button"
                onClick={() => openUserSelectModal('new')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-left hover:bg-gray-50 flex justify-between items-center h-10 min-h-[2.5rem]"
              >
                {newRole.UserIds && newRole.UserIds.length > 0 ? (
                  <span className="text-gray-900 text-xs line-clamp-2">{findUserNames(newRole.UserIds)}</span>
                ) : (
                  <span className="text-gray-500">選擇人員</span>
                )}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" /></svg>
              </button>
            </div>
            <div>
              {renderPermissionTree(false)}
            </div>
            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600"
              >
                確認
              </button>
            </div>
          </form>
        </Modal>

        {/* 編輯角色彈窗 */}
        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title="編輯角色"
        >
          <form onSubmit={handleEditSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                案場
              </label>
              <Select
                className="w-full"
                placeholder="請選擇案場"
                value={editRole.SiteCode}
                onChange={(value) => handleSiteChange(value, true)}
                options={sites.map(site => ({
                  label: site.Name,
                  value: site.Value
                }))}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                角色名稱
              </label>
              <input
                type="text"
                name="Name"
                value={editRole.Name}
                onChange={handleEditInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                人員
              </label>
              <button
                type="button"
                onClick={() => openUserSelectModal('edit')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-left hover:bg-gray-50 flex justify-between items-center h-10 min-h-[2.5rem]"
              >
                {editRole.UserIds && editRole.UserIds.length > 0 ? (
                  <span className="text-gray-900 text-xs line-clamp-2">{findUserNames(editRole.UserIds)}</span>
                ) : (
                  <span className="text-gray-500">選擇人員</span>
                )}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" /></svg>
              </button>
            </div>
            <div>
              {renderPermissionTree(true)}
            </div>
            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={() => setIsEditModalOpen(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600"
              >
                確認
              </button>
            </div>
          </form>
        </Modal>

        {/* --- 新增：人員選擇 Modal --- */}
        <TableSelectModal
          isOpen={isUserSelectModalOpen}
          onClose={() => setIsUserSelectModalOpen(false)}
          onSelect={(selectedItems) => {
            // 確保 selectedItems 是陣列 (因為 isMultiSelect=true)
            const selectedIds = Array.isArray(selectedItems) ? selectedItems.map(item => item.Value) : [];
            if (userSelectTargetRole === 'new') {
              setNewRole(prev => ({ ...prev, UserIds: selectedIds }));
            } else if (userSelectTargetRole === 'edit') {
              setEditRole(prev => ({ ...prev, UserIds: selectedIds }));
            }
            setIsUserSelectModalOpen(false); // 關閉 Modal
          }}
          title="選擇人員"
          data={users} // 使用已獲取的人員列表
          columns={[
            { key: "Value", title: "帳號" },
            { key: "Name", title: "姓名" },
          ]}
          rowKey="Value"
          isMultiSelect={true} // 角色的人員選擇是多選
          initialSelectedValues={
            userSelectTargetRole === 'new'
              ? newRole.UserIds
              : userSelectTargetRole === 'edit'
              ? editRole.UserIds
              : []
          }
          searchKeys={["Value", "Name"]}
        />
        {/* --- 結束新增 --- */}

      </div>
    </ConfigProvider>
  );
} 