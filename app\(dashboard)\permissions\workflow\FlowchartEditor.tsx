import { SVG, Element as SVGElement, Svg } from '@svgdotjs/svg.js';
import React, { useEffect, useRef } from 'react';

// 從 workflow/page.tsx 複製或引入相關類型
interface ReviewTaskStep {
  id: number; // 臨時ID，僅用於UI
  Name: string;
  TimeLimit: number;
  ApproverIds: string[];
  SystemNotification: boolean;
  EmailNotification: boolean;
  SmsNotification: boolean;
}

interface FlowchartEditorProps {
  steps: ReviewTaskStep[];
}

const STEP_WIDTH = 150;
const STEP_HEIGHT = 100;
const VERTICAL_GAP = 20; // This might not be used in the new layout
const HORIZONTAL_GAP = 60; // Gap between steps horizontally
const ROW_GAP = 50;        // Gap between rows vertically
const PADDING = 20;

const FlowchartEditor: React.FC<FlowchartEditorProps> = ({ steps }) => {
  const svgContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    let drawInstance: Svg | null = null;

    if (svgContainerRef.current) {
      svgContainerRef.current.innerHTML = '';
      drawInstance = SVG().addTo(svgContainerRef.current).size('100%', 400);

      if (drawInstance && steps && steps.length > 0) {
        const stepsPerRow = 2;
        let maxX = 0;
        let maxY = 0;
        const positions: { x: number, y: number }[] = [];

        // 1. Calculate positions first
        steps.forEach((step, index) => {
          const rowIndex = Math.floor(index / stepsPerRow);
          const colIndex = index % stepsPerRow;
          const isLeftToRightRow = rowIndex % 2 === 0;

          let x: number;
          if (isLeftToRightRow) {
            x = PADDING + colIndex * (STEP_WIDTH + HORIZONTAL_GAP);
          } else {
            x = PADDING + (stepsPerRow - 1 - colIndex) * (STEP_WIDTH + HORIZONTAL_GAP);
          }
          const y = PADDING + rowIndex * (STEP_HEIGHT + ROW_GAP);
          positions.push({ x, y });

          if (x + STEP_WIDTH > maxX) maxX = x + STEP_WIDTH;
          if (y + STEP_HEIGHT > maxY) maxY = y + STEP_HEIGHT;
        });

        // 2. Draw steps and text at calculated positions
        steps.forEach((step, index) => {
          const { x, y } = positions[index];

          // Draw rectangle
          const rect = drawInstance!.rect(STEP_WIDTH, STEP_HEIGHT)
            .attr({
              x: x,
              y: y,
              rx: 5, ry: 5,
              fill: '#eef2ff', stroke: '#a5b4fc', 'stroke-width': 1
            });

          // Draw text (using existing centering logic)
          const hasApprovers = step.ApproverIds.length > 0;
          const numberOfLines = 1 + 1 + (hasApprovers ? 1 : 0);
          const fontSizeTitle = 13;
          const fontSizeInfo = 11;
          const lineSpacing = 18;
          const totalTextHeight = lineSpacing * (numberOfLines - 1) + fontSizeTitle;
          const firstLineDy = - (totalTextHeight / 2) + (fontSizeTitle / 2) + (numberOfLines > 1 ? 2 : 0);
          const text = drawInstance!.text(add => {
             add.tspan(step.Name || `步驟 ${index + 1}`).fill('#374151').font({ size: fontSizeTitle, weight: 'bold' });
             add.tspan(`時限: ${step.TimeLimit}h`).newLine().dx(0).dy(lineSpacing).fill('#6b7280').font({ size: fontSizeInfo });
             if (hasApprovers) {
               add.tspan(`審核人: ${step.ApproverIds.length} 位`).newLine().dx(0).dy(lineSpacing).fill('#6b7280').font({ size: fontSizeInfo });
             }
          });
          text.move(x + STEP_WIDTH / 2, y + STEP_HEIGHT / 2 + firstLineDy)
              .font({ anchor: 'middle' })
              .attr({ 'dominant-baseline': 'middle' });
        });

        // 3. Draw connecting lines
        steps.forEach((step, index) => {
          if (index < steps.length - 1) {
            const currentPos = positions[index];
            const nextPos = positions[index + 1];
            const currentRowIndex = Math.floor(index / stepsPerRow);
            const nextRowIndex = Math.floor((index + 1) / stepsPerRow);
            const isLeftToRightRow = currentRowIndex % 2 === 0;
            const arrowSize = 8;

            if (currentRowIndex === nextRowIndex) {
              // Intra-row connection (Horizontal)
              let startX, startY, endX, endY;
              startY = currentPos.y + STEP_HEIGHT / 2;
              endY = nextPos.y + STEP_HEIGHT / 2; // Same Y

              if (isLeftToRightRow) {
                startX = currentPos.x + STEP_WIDTH;
                endX = nextPos.x;
                drawInstance!.line(startX, startY, endX, endY).stroke({ width: 2, color: '#9ca3af' });
                drawInstance!.polygon(`${endX - arrowSize},${endY - arrowSize / 2} ${endX},${endY} ${endX - arrowSize},${endY + arrowSize / 2}`)
                  .fill('#9ca3af').stroke({ width: 1, color: '#9ca3af' });
              } else {
                startX = currentPos.x;
                endX = nextPos.x + STEP_WIDTH;
                drawInstance!.line(startX, startY, endX, endY).stroke({ width: 2, color: '#9ca3af' });
                drawInstance!.polygon(`${endX + arrowSize},${endY - arrowSize / 2} ${endX},${endY} ${endX + arrowSize},${endY + arrowSize / 2}`)
                   .fill('#9ca3af').stroke({ width: 1, color: '#9ca3af' });
              }
            } else {
              // Inter-row connection (Vertical turn)
              const start = { x: currentPos.x + STEP_WIDTH / 2, y: currentPos.y + STEP_HEIGHT };
              const end = { x: nextPos.x + STEP_WIDTH / 2, y: nextPos.y };
              const midY = start.y + ROW_GAP / 2;
              // Flatten the points array for polyline
              const pointsArray: number[] = [
                  start.x, start.y,
                  start.x, midY,
                  end.x, midY,
                  end.x, end.y
              ];
              drawInstance!.polyline(pointsArray).fill('none').stroke({ width: 2, color: '#9ca3af' });
              drawInstance!.polygon(`${end.x - arrowSize / 2},${end.y - arrowSize} ${end.x},${end.y} ${end.x + arrowSize / 2},${end.y - arrowSize}`)
                .fill('#9ca3af').stroke({ width: 1, color: '#9ca3af' });
            }
          }
        });

        // Adjust final canvas size
        const requiredWidth = maxX + PADDING;
        const requiredHeight = maxY + PADDING;
        // Ensure minimum size, e.g., min width for 2 steps
        const minWidth = PADDING * 2 + stepsPerRow * STEP_WIDTH + (stepsPerRow - 1) * HORIZONTAL_GAP;
        drawInstance.size(Math.max(requiredWidth, minWidth), requiredHeight > 400 ? requiredHeight : 400);

      } else if (drawInstance) {
        drawInstance.size('100%', 400); // Reset size if no steps
      }
    }

    // Cleanup function
    return () => {
       if (drawInstance) {
         try { drawInstance.remove(); } catch (e) {
           if (svgContainerRef.current) { svgContainerRef.current.innerHTML = ''; }
           console.error("Error removing SVG instance:", e);
         }
       }
    };
  }, [steps]);

  return <div ref={svgContainerRef} style={{ width: '100%', border: '1px solid #ccc', borderRadius: '4px', overflow: 'auto' }}></div>;
};

export default FlowchartEditor; 