'use client';

import React, { useState, useEffect } from 'react';
import { Button, Modal, Input, Select, message, Tag, Space, ConfigProvider } from 'antd';
import { Plus, X } from 'lucide-react';
import { reviewTaskApi } from '@/app/services/api/reviewTaskApi';
import { 
  ReviewTaskStep, 
  ReviewTaskListItem,
  CreateReviewTaskDto,
  ReviewTaskDetailOutput,
  ReviewTaskQueryParams,
  ReviewDepartment,
  ReviewTaskStatus,
  reviewTaskStatusMap
} from '@/app/interfaces/dto/reviewTask.dto';
import { SortOrderInfo } from '@/app/interfaces/dto/common.dto';
import { SearchOutlined } from '@ant-design/icons';
import zhTW from 'antd/locale/zh_TW';
import DataTable, { SearchInfo } from '@/app/components/DataTable';
import type { ColumnsType, ColumnType } from 'antd/es/table';
import type { SorterResult } from 'antd/es/table/interface';
import FlowchartEditor from './FlowchartEditor';

// 擴展列類型定義
interface ExtendedColumnType<T> extends ColumnType<T> {
  allowSearch?: boolean;
}

interface WorkflowFormData {
  Name: string;
  Description: string;
  Steps: (ReviewTaskStep & { id: number })[];
  Status: ReviewTaskStatus;
}

const notifyMethods = ['系統通知', 'Email', '簡訊'];

const createEmptyStep = (): ReviewTaskStep & { id: number } => ({
  id: 0,  // 臨時ID，僅用於UI
  Name: '',
  TimeLimit: 24,
  ApproverIds: [],
  SystemNotification: true,
  EmailNotification: false,
  SmsNotification: false
});

const createEmptyWorkflow = (): WorkflowFormData => ({
  Name: '',
  Description: '',
  Steps: [],
  Status: 'pending' as ReviewTaskStatus
});

export default function WorkflowPage() {
  const [messageApi, contextHolder] = message.useMessage();
  // 修改用戶數據狀態
  const [departments, setDepartments] = useState<ReviewDepartment[]>([]);
  const [workflows, setWorkflows] = useState<ReviewTaskListItem[]>([]);
  const [newWorkflow, setNewWorkflow] = useState<WorkflowFormData>(createEmptyWorkflow());
  const [editWorkflow, setEditWorkflow] = useState<WorkflowFormData>(createEmptyWorkflow());
  const [editingWorkflowId, setEditingWorkflowId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [sortOrder, setSortOrder] = useState<SortOrderInfo[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [showNewModal, setShowNewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>, isEdit = false) => {
    const { name, value } = e.target;
    const setterFunction = isEdit ? setEditWorkflow : setNewWorkflow;
    
    setterFunction(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    handleInputChange(e, true);
  };

  const handleAddStep = (isEdit = false) => {
    const setterFunction = isEdit ? setEditWorkflow : setNewWorkflow;
    const currentSteps = isEdit ? editWorkflow.Steps : newWorkflow.Steps;
    
    setterFunction(prev => ({
      ...prev,
      Steps: [
        ...prev.Steps,
        {
          ...createEmptyStep(),
          id: currentSteps.length + 1
        }
      ]
    }));
  };

  const handleAddStepClick = (e: React.MouseEvent, isEdit = false) => {
    e.preventDefault();
    handleAddStep(isEdit);
  };

  const handleRemoveStep = (stepId: number, isEdit = false) => {
    const setterFunction = isEdit ? setEditWorkflow : setNewWorkflow;
    
    setterFunction(prev => ({
      ...prev,
      Steps: prev.Steps.filter(step => step.id !== stepId)
    }));
  };

  const handleStepChange = (stepId: number, field: keyof ReviewTaskStep, value: any, isEdit = false) => {
    const setterFunction = isEdit ? setEditWorkflow : setNewWorkflow;
    
    setterFunction(prev => ({
      ...prev,
      Steps: prev.Steps.map(step =>
        step.id === stepId
          ? { ...step, [field]: value }
          : step
      )
    }));
  };

  const handleApproverToggle = (stepId: number, userId: string, isEdit = false) => {
    const setterFunction = isEdit ? setEditWorkflow : setNewWorkflow;
    
    setterFunction(prev => ({
      ...prev,
      Steps: prev.Steps.map(step => {
        if (step.id === stepId) {
          const approvers = step.ApproverIds.includes(userId)
            ? step.ApproverIds.filter(id => id !== userId)
            : [...step.ApproverIds, userId];
          return { ...step, ApproverIds: approvers };
        }
        return step;
      })
    }));
  };

  const handleNotificationToggle = (stepId: number, notificationType: 'SystemNotification' | 'EmailNotification' | 'SmsNotification', isEdit = false) => {
    const setterFunction = isEdit ? setEditWorkflow : setNewWorkflow;
    
    setterFunction(prev => ({
      ...prev,
      Steps: prev.Steps.map(step => {
        if (step.id === stepId) {
          return {
            ...step,
            [notificationType]: !step[notificationType]
          };
        }
        return step;
      })
    }));
  };

  // 獲取審核人員列表
  const fetchReviewUsers = async () => {
    try {
      const response = await reviewTaskApi.getReviewUsers();
      if (response.isSuccess) {
        setDepartments(response.body);
      } else {
        setError('獲取審核人員列表失敗');
      }
    } catch (error) {
      setError('獲取審核人員列表失敗');
      console.error('Error fetching review users:', error);
    }
  };

  useEffect(() => {
    fetchReviewUsers();
  }, []);

  // 獲取用戶名稱
  const getUserNames = (userIds: string[]) => {
    return userIds.map(userId => {
      const user = departments.flatMap(dept => dept.Users).find(u => u.UserInfoId === userId);
      return user ? user.UserName : '';
    }).filter(Boolean).join(', ');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      const createDto: CreateReviewTaskDto = {
        Name: newWorkflow.Name,
        Description: newWorkflow.Description,
        Steps: newWorkflow.Steps.map(({ id, ...step }) => ({
          Name: step.Name,
          TimeLimit: step.TimeLimit,
          ApproverIds: step.ApproverIds,
          SystemNotification: step.SystemNotification,
          EmailNotification: step.EmailNotification,
          SmsNotification: step.SmsNotification
        }))
      };
      await reviewTaskApi.createReviewTask(createDto);
      messageApi.success('工作流程創建成功');
      setShowNewModal(false);
      setNewWorkflow(createEmptyWorkflow());
      fetchWorkflows();
    } catch (error) {
      console.error('Error creating workflow:', error);
      messageApi.error('工作流程創建失敗');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingWorkflowId) return;

    try {
      setSubmitting(true);
      const updateDto: CreateReviewTaskDto = {
        Name: editWorkflow.Name,
        Description: editWorkflow.Description,
        Steps: editWorkflow.Steps.map(({ id, ...step }) => ({
          Name: step.Name,
          TimeLimit: step.TimeLimit,
          ApproverIds: step.ApproverIds,
          SystemNotification: step.SystemNotification,
          EmailNotification: step.EmailNotification,
          SmsNotification: step.SmsNotification
        }))
      };
      await reviewTaskApi.updateReviewTask(editingWorkflowId, updateDto);
      messageApi.success('工作流程更新成功');
      setShowEditModal(false);
      fetchWorkflows();
    } catch (error) {
      console.error('Error updating workflow:', error);
      messageApi.error('工作流程更新失敗');
    } finally {
      setSubmitting(false);
    }
  };

  const handleFetchWorkflowDetail = async (taskId: number) => {
    try {
      setLoading(true);
      const response = await reviewTaskApi.getReviewTaskDetail(taskId);
      if (response.body) {
        const detail = response.body;
        const formData: WorkflowFormData = {
          Name: detail.Name,
          Description: detail.Description,
          Steps: (detail.Steps || []).map((step, index) => ({
            ...step,
            id: index + 1,
            SystemNotification: step.SystemNotification || false,
            EmailNotification: step.EmailNotification || false,
            SmsNotification: step.SmsNotification || false
          })),
          Status: detail.Status
        };
        setEditWorkflow(formData);
        setEditingWorkflowId(taskId);
        setShowEditModal(true);
      }
    } catch (error) {
      console.error('Error fetching workflow detail:', error);
      messageApi.error('獲取工作流程詳情失敗');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteWorkflow = async (workflowId: number) => {
    if (!confirm('確定要刪除此工作流程嗎？')) return;

    try {
      setLoading(true);
      await reviewTaskApi.deleteReviewTask(workflowId);
      fetchWorkflows();
    } catch (error) {
      setError('刪除工作流程失敗');
      console.error('Error deleting workflow:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (workflowId: number) => {
    try {
      const workflow = workflows.find(w => w.taskId === workflowId);
      if (!workflow) return;

      const detail = await reviewTaskApi.getReviewTaskDetail(workflowId);
      if (!detail.isSuccess || !detail.body) {
        messageApi.error('獲取工作流程詳情失敗');
        return;
      }

      const updateDto: CreateReviewTaskDto = {
        Name: detail.body.Name,
        Description: detail.body.Description,
        Status: workflow.status === 'enable' ? 'disable' : 'enable',
        Deadline: detail.body.Deadline ? new Date(detail.body.Deadline) : undefined,
        Steps: detail.body.Steps.map(step => ({
          Name: step.Name,
          ApproverIds: step.ApproverIds,
          TimeLimit: step.TimeLimit,
          SystemNotification: step.SystemNotification,
          EmailNotification: step.EmailNotification,
          SmsNotification: step.SmsNotification
        }))
      };

      await reviewTaskApi.updateReviewTask(workflowId, updateDto);
      messageApi.success('工作流程狀態更新成功');
      fetchWorkflows();
    } catch (error) {
      console.error('Error toggling workflow status:', error);
      messageApi.error('更新工作流程狀態失敗');
    }
  };

  const fetchWorkflows = async (queryParams?: Partial<ReviewTaskQueryParams>) => {
    try {
      setLoading(true);
      const defaultParams: ReviewTaskQueryParams = {
        UsingPaging: true,
        PageIndex: page,
        NumberOfPperPage: pageSize,
        SortOrderInfos: sortOrder.length > 0 ? sortOrder : undefined,
        SearchTermInfos: []
      };

      const response = await reviewTaskApi.getReviewTasks({
        ...defaultParams,
        ...queryParams
      });

      if (response.body) {
        const mappedTasks: ReviewTaskListItem[] = response.body.Detail.map(task => ({
          taskId: task.TaskId,
          name: task.Name,
          description: task.Description,
          status: task.Status,
          totalStep: task.TotalStep,
          currentStep: 0,
          createdTime: task.CreatedTime,
          updatedTime: task.UpdatedTime,
          createdUserInfoId: task.CreatedUserInfoId,
          updatedUserInfoId: task.UpdatedUserInfoId
        }));
        setWorkflows(mappedTasks);
        setTotal(response.body.RecordCount);
      }
    } catch (error) {
      console.error('Error fetching workflows:', error);
      messageApi.error('獲取工作流程列表失敗');
    } finally {
      setLoading(false);
    }
  };

  // 處理表格變更
  const handleTableChange = (page: number, pageSize: number) => {
    setPage(page);
    setPageSize(pageSize);
  };

  // 處理表格排序
  const handleTableSort = (sorter: SorterResult<ReviewTaskListItem> | SorterResult<ReviewTaskListItem>[]) => {
    if (Array.isArray(sorter)) {
      return;
    }
    if (sorter.columnKey || sorter.field) {
      const newSortOrder: SortOrderInfo[] = [{
        SortField: (sorter.columnKey || sorter.field) as string,
        SortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'
      }];
      setSortOrder(newSortOrder);
    } else {
      setSortOrder([]);
    }
  };

  // 處理表格過濾
  const handleTableFilter = (searchInfos: SearchInfo[]) => {
    setPage(1); // 重置到第一頁
    fetchWorkflows({
      PageIndex: 1,
      SearchTermInfos: searchInfos
    });
  };

  useEffect(() => {
    fetchWorkflows({
      PageIndex: page,
      NumberOfPperPage: pageSize,
      SortOrderInfos: sortOrder.length > 0 ? sortOrder : undefined,
      SearchTermInfos: []
    });
  }, [page, pageSize, sortOrder]); // 移除 filterName 和 filterStatus，因為現在使用表頭過濾

  useEffect(() => {
    fetchReviewUsers();
  }, []); // 只在組件加載時執行一次

  const columns: ExtendedColumnType<ReviewTaskListItem>[] = [
    {
      title: '流程名稱',
      dataIndex: 'name',
      key: 'Name',
      sorter: true,
      allowSearch: true
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'Description',
      sorter: true,
      allowSearch: true
    },
    {
      title: '步驟數',
      dataIndex: 'totalStep',
      key: 'TotalStep',
      allowSearch: false,
      render: (totalStep: number) => `${totalStep} 步驟`,
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'Status',
      sorter: true,
      allowSearch: true,
      render: (status: ReviewTaskStatus) => (
        <Tag color={status === 'enable' ? 'success' : status === 'disable' ? 'default' : 'error'}>
          {reviewTaskStatusMap[status]}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      allowSearch: false,
      render: (_, record) => (
        <Space size="middle">
          <button 
            className="text-blue-600 hover:text-blue-900"
            onClick={() => handleFetchWorkflowDetail(record.taskId)}
          >
            編輯
          </button>
          <button 
            className="text-yellow-600 hover:text-yellow-900"
            onClick={() => handleToggleStatus(record.taskId)}
          >
            {record.status === 'enable' ? '停用' : '啟用'}
          </button>
          <button 
            className="text-red-600 hover:text-red-900"
            onClick={() => handleDeleteWorkflow(record.taskId)}
          >
            刪除
          </button>
        </Space>
      ),
    },
  ];

  return (
    <ConfigProvider
      locale={zhTW}
      theme={{
        components: {
          Button: {
            // 設置按鈕的默認樣式
            colorPrimary: '#1890ff',
          },
          Modal: {
            // 設置對話框的默認樣式
            borderRadiusLG: 4,
          }
        }
      }}
    >
      {contextHolder}
      <div className="bg-white rounded-lg shadow">
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <button
              onClick={() => setShowNewModal(true)}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            >
              新增流程
            </button>
          </div>
        </div>
        <DataTable
          columns={columns}
          dataSource={workflows}
          loading={loading}
          total={total}
          currentPage={page}
          pageSize={pageSize}
          onPageChange={handleTableChange}
          onSort={handleTableSort}
          onFilter={handleTableFilter}
          showSizeChanger={true}
          pageSizeOptions={['10', '25', '50', '100']}
          rowKey="taskId"
          className="shadow-sm"
        />

        {/* 新增流程彈窗 */}
        <Modal
          title="新增工作流程"
          open={showNewModal}
          onCancel={() => setShowNewModal(false)}
          footer={null}
        >
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  流程名稱
                </label>
                <input
                  type="text"
                  name="Name"
                  value={newWorkflow.Name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  描述
                </label>
                <textarea
                  name="Description"
                  value={newWorkflow.Description}
                  onChange={handleInputChange}
                  required
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  審核步驟
                </label>
                <button
                  type="button"
                  onClick={(e) => handleAddStepClick(e, false)}
                  className="flex items-center text-blue-600 hover:text-blue-700"
                >
                  <Plus size={16} className="mr-1" />
                  添加步驟
                </button>
              </div>
              
              <div className="space-y-3">
                {newWorkflow.Steps.map((step, index) => (
                  <div key={step.id} className="p-3 border rounded-lg relative">
                    <button
                      type="button"
                      onClick={() => handleRemoveStep(step.id, false)}
                      className="absolute top-2 right-2 text-gray-400 hover:text-gray-500"
                    >
                      <X size={16} />
                    </button>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          步驟名稱
                        </label>
                        <input
                          type="text"
                          value={step.Name}
                          onChange={(e) => handleStepChange(step.id, 'Name', e.target.value, false)}
                          required
                          className="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          審核人員
                        </label>
                        <div className="max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                          {departments.map((dept) => (
                            <div key={dept.Dept.DeptName} className="mb-1">
                              <div className="font-medium text-gray-700 text-xs mb-1">{dept.Dept.DeptName}</div>
                              <div className="pl-2 space-y-1">
                                {dept.Users.map(user => (
                                  <label key={user.UserInfoId} className="flex items-center space-x-1">
                                    <input
                                      type="checkbox"
                                      checked={step.ApproverIds.includes(user.UserInfoId)}
                                      onChange={(e) => handleApproverToggle(step.id, user.UserInfoId, false)}
                                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-3 w-3"
                                    />
                                    <span className="text-xs text-gray-700">{user.UserName} ({user.GradeName})</span>
                                  </label>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          時限（小時）
                        </label>
                        <input
                          type="number"
                          value={step.TimeLimit}
                          onChange={(e) => handleStepChange(step.id, 'TimeLimit', parseInt(e.target.value), false)}
                          required
                          min="1"
                          className="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          通知方式
                        </label>
                        <div className="grid grid-cols-3 gap-2">
                          <label className="inline-flex items-center">
                            <input
                              type="checkbox"
                              checked={step.SystemNotification}
                              onChange={() => handleNotificationToggle(step.id, 'SystemNotification', false)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                            />
                            <span className="ml-2 text-sm text-gray-700">系統通知</span>
                          </label>
                          <label className="inline-flex items-center">
                            <input
                              type="checkbox"
                              checked={step.EmailNotification}
                              onChange={() => handleNotificationToggle(step.id, 'EmailNotification', false)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                            />
                            <span className="ml-2 text-sm text-gray-700">Email</span>
                          </label>
                          <label className="inline-flex items-center">
                            <input
                              type="checkbox"
                              checked={step.SmsNotification}
                              onChange={() => handleNotificationToggle(step.id, 'SmsNotification', false)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                            />
                            <span className="ml-2 text-sm text-gray-700">簡訊</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 新增流程圖預覽 */}
              {newWorkflow.Steps.length > 0 && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    流程預覽
                  </label>
                  <FlowchartEditor steps={newWorkflow.Steps} />
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-4 pt-2">
              <button
                type="button"
                onClick={() => setShowNewModal(false)}
                className="px-3 py-1.5 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                取消
              </button>
              <button
                type="submit"
                className="px-3 py-1.5 text-white bg-blue-500 rounded-md hover:bg-blue-600"
              >
                確認
              </button>
            </div>
          </form>
        </Modal>

        {/* 編輯流程彈窗 */}
        <Modal
          title="編輯工作流程"
          open={showEditModal}
          onCancel={() => setShowEditModal(false)}
          footer={null}
        >
          <form onSubmit={handleEditSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  流程名稱
                </label>
                <input
                  type="text"
                  name="Name"
                  value={editWorkflow.Name}
                  onChange={handleEditInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  描述
                </label>
                <textarea
                  name="Description"
                  value={editWorkflow.Description}
                  onChange={handleEditInputChange}
                  required
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  審核步驟
                </label>
                <button
                  type="button"
                  onClick={(e) => handleAddStepClick(e, true)}
                  className="flex items-center text-blue-600 hover:text-blue-700"
                >
                  <Plus size={16} className="mr-1" />
                  添加步驟
                </button>
              </div>
              
              <div className="space-y-3">
                {editWorkflow.Steps.map((step, index) => (
                  <div key={step.id} className="p-3 border rounded-lg relative">
                    <button
                      type="button"
                      onClick={() => handleRemoveStep(step.id, true)}
                      className="absolute top-2 right-2 text-gray-400 hover:text-gray-500"
                    >
                      <X size={16} />
                    </button>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          步驟名稱
                        </label>
                        <input
                          type="text"
                          value={step.Name}
                          onChange={(e) => handleStepChange(step.id, 'Name', e.target.value, true)}
                          required
                          className="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          審核人員
                        </label>
                        <div className="max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                          {departments.map((dept) => (
                            <div key={dept.Dept.DeptName} className="mb-1">
                              <div className="font-medium text-gray-700 text-xs mb-1">{dept.Dept.DeptName}</div>
                              <div className="pl-2 space-y-1">
                                {dept.Users.map(user => (
                                  <label key={user.UserInfoId} className="flex items-center space-x-1">
                                    <input
                                      type="checkbox"
                                      checked={step.ApproverIds.includes(user.UserInfoId)}
                                      onChange={(e) => handleApproverToggle(step.id, user.UserInfoId, true)}
                                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-3 w-3"
                                    />
                                    <span className="text-xs text-gray-700">{user.UserName} ({user.GradeName})</span>
                                  </label>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          時限（小時）
                        </label>
                        <input
                          type="number"
                          value={step.TimeLimit}
                          onChange={(e) => handleStepChange(step.id, 'TimeLimit', parseInt(e.target.value), true)}
                          required
                          min="1"
                          className="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          通知方式
                        </label>
                        <div className="grid grid-cols-3 gap-2">
                          <label className="inline-flex items-center">
                            <input
                              type="checkbox"
                              checked={step.SystemNotification}
                              onChange={() => handleNotificationToggle(step.id, 'SystemNotification', true)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                            />
                            <span className="ml-2 text-sm text-gray-700">系統通知</span>
                          </label>
                          <label className="inline-flex items-center">
                            <input
                              type="checkbox"
                              checked={step.EmailNotification}
                              onChange={() => handleNotificationToggle(step.id, 'EmailNotification', true)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                            />
                            <span className="ml-2 text-sm text-gray-700">Email</span>
                          </label>
                          <label className="inline-flex items-center">
                            <input
                              type="checkbox"
                              checked={step.SmsNotification}
                              onChange={() => handleNotificationToggle(step.id, 'SmsNotification', true)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                            />
                            <span className="ml-2 text-sm text-gray-700">簡訊</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 新增流程圖預覽 */}
              {editWorkflow.Steps.length > 0 && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    流程預覽
                  </label>
                  <FlowchartEditor steps={editWorkflow.Steps} />
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-4 pt-2">
              <button
                type="button"
                onClick={() => setShowEditModal(false)}
                className="px-3 py-1.5 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                取消
              </button>
              <button
                type="submit"
                className="px-3 py-1.5 text-white bg-blue-500 rounded-md hover:bg-blue-600"
              >
                確認
              </button>
            </div>
          </form>
        </Modal>
      </div>
    </ConfigProvider>
  );
}