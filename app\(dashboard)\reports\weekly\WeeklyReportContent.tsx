'use client';

import React from 'react';
import { Table } from 'antd';
import type { TableProps } from 'antd';
import EChart from '../../../components/common/EChart'; // 確認路徑是否正確
import type {
  SummaryDataType,
  MediaDataType,
  NewsDataType,
  OperationStatsDataType,
  // GenderAnalysisDataType,
  AgeAnalysisDataType,
  IndustryAnalysisDataType,
  RegionAnalysisBaseType,
  RegionAnalysisDealType,
  MediaAnalysisDataType,
  AreaPreferenceDataType,
  AreaPreferenceDealDataType,
  PurchasePurposeDataType,
  PurchasePurposeDealDataType,
  LayoutPreferenceDataType,
  LayoutPreferenceDealDataType,
  FloorPreferenceDataType,
  FloorPreferenceDealDataType,
  BudgetPreferenceDataType,
  BudgetPreferenceDealDataType,
  BuildingFloorStatus,
  ParkingLevelStatus,
  ParkingSummaryStatus,
} from './weekly-report.service'; // 假設類型定義與主頁面相同

// ReportData 的完整類型定義，應與 WeeklyReportPage 中的 reportData 狀態類型一致
export interface ReportDataShape {
  summary: SummaryDataType[];
  summaryColumns: TableProps<SummaryDataType>['columns'];
  mainChartOption: any;
  visitorPromotionList: string[];
  dealPromotionList: string[];
  mediaPromotionData: MediaDataType[];
  mediaPromotionColumns: TableProps<MediaDataType>['columns'];
  marketNewsData: NewsDataType[];
  stockChartOption: any;
  weeklyOperationStatsData: OperationStatsDataType[];
  operationChartOption: any;
  // genderAnalysisData: {
  //   calls: GenderAnalysisDataType[];
  //   visits: GenderAnalysisDataType[];
  //   revisits: GenderAnalysisDataType[];
  //   deals: GenderAnalysisDataType[];
  // };
  ageAnalysisData: {
    visits: AgeAnalysisDataType[];
    revisits: AgeAnalysisDataType[];
    deals: AgeAnalysisDataType[];
  };
  industryAnalysisData: {
    visits: IndustryAnalysisDataType[];
    revisits: IndustryAnalysisDataType[];
    deals: IndustryAnalysisDataType[];
  };
  taipeiRegionCallsData: RegionAnalysisBaseType[];
  taipeiRegionVisitsData: RegionAnalysisBaseType[];
  taipeiRegionRevisitsData: RegionAnalysisBaseType[];
  taipeiRegionDealsData: RegionAnalysisDealType[];
  newTaipeiRegionCallsData: RegionAnalysisBaseType[];
  newTaipeiRegionVisitsData: RegionAnalysisBaseType[];
  newTaipeiRegionRevisitsData: RegionAnalysisBaseType[];
  newTaipeiRegionDealsData: RegionAnalysisDealType[];
  otherRegionCallsData: RegionAnalysisBaseType[];
  otherRegionVisitsData: RegionAnalysisBaseType[];
  otherRegionRevisitsData: RegionAnalysisBaseType[];
  otherRegionDealsData: RegionAnalysisDealType[];
  mediaAnalysisCallsData: MediaAnalysisDataType[];
  areaPreferenceCallsData: AreaPreferenceDataType[];
  areaPreferenceVisitsData: AreaPreferenceDataType[];
  areaPreferenceRevisitsData: AreaPreferenceDataType[];
  areaPreferenceDealsData: AreaPreferenceDealDataType[];
  purchasePurposeVisitsData: PurchasePurposeDataType[];
  purchasePurposeRevisitsData: PurchasePurposeDataType[];
  purchasePurposeDealsData: PurchasePurposeDealDataType[];
  layoutPreferenceVisitsData: LayoutPreferenceDataType[];
  layoutPreferenceRevisitsData: LayoutPreferenceDataType[];
  layoutPreferenceDealsData: LayoutPreferenceDealDataType[];
  floorPreferenceVisitsData: FloorPreferenceDataType[];
  floorPreferenceRevisitsData: FloorPreferenceDataType[];
  floorPreferenceDealsData: FloorPreferenceDealDataType[];
  budgetPreferenceVisitsData: BudgetPreferenceDataType[];
  budgetPreferenceRevisitsData: BudgetPreferenceDataType[];
  budgetPreferenceDealsData: BudgetPreferenceDealDataType[];
  buildingSalesStatusData: BuildingFloorStatus[];
  parkingSalesStatusData: ParkingLevelStatus[];
  parkingSummaryData: ParkingSummaryStatus[];
}

interface WeeklyReportContentProps {
  reportData: ReportDataShape | null;
  showEchartDownload?: boolean; // New prop to control EChart download button visibility
}

// Helper functions (moved from WeeklyReportPage)
const getUnitCellStyle = (status: BuildingFloorStatus[keyof BuildingFloorStatus]) => {
  switch (status) {
    case '售':
      return 'bg-red-200 text-red-800';
    case '定':
      return 'bg-yellow-200 text-yellow-800';
    default:
      return 'bg-white';
  }
};

const getParkingCellStyle = (status: string | null) => {
  if (!status) return 'bg-white';
  if (status.startsWith('售')) return 'bg-red-200 text-red-800';
  if (status.startsWith('定')) return 'bg-yellow-200 text-yellow-800';
  if (status === '地主保留') return 'bg-gray-300 text-gray-700';
  return 'bg-white';
};

function getWeekDates(weekIndex: number): string {
  const baseDate = new Date('2024-07-01');
  const startOffset = weekIndex * 7;
  const endOffset = startOffset + 6;
  const startDate = new Date(baseDate.getTime() + startOffset * 24 * 60 * 60 * 1000);
  const endDate = new Date(baseDate.getTime() + endOffset * 24 * 60 * 60 * 1000);
  const formatDate = (date: Date) => `${date.getMonth() + 1}/${date.getDate()}`;
  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
}


const WeeklyReportContent: React.FC<WeeklyReportContentProps> = ({ reportData, showEchartDownload = false }) => {
  if (!reportData) {
    // This case should ideally be handled by the parent (e.g., show a message or spinner)
    // Or, if this component is always expected to receive valid reportData, this check might be redundant
    // For now, let's assume parent handles the "no data" or "loading" state.
    return null;
  }

  const {
    summary,
    summaryColumns,
    mainChartOption,
    visitorPromotionList,
    dealPromotionList,
    mediaPromotionData,
    mediaPromotionColumns,
    marketNewsData,
    stockChartOption,
    weeklyOperationStatsData,
    operationChartOption,
    // genderAnalysisData,
    ageAnalysisData,
    industryAnalysisData,
    taipeiRegionCallsData,
    taipeiRegionVisitsData,
    taipeiRegionRevisitsData,
    taipeiRegionDealsData,
    newTaipeiRegionCallsData,
    newTaipeiRegionVisitsData,
    newTaipeiRegionRevisitsData,
    newTaipeiRegionDealsData,
    otherRegionCallsData,
    otherRegionVisitsData,
    otherRegionRevisitsData,
    otherRegionDealsData,
    mediaAnalysisCallsData,
    areaPreferenceCallsData,
    areaPreferenceVisitsData,
    areaPreferenceRevisitsData,
    areaPreferenceDealsData,
    purchasePurposeVisitsData,
    purchasePurposeRevisitsData,
    purchasePurposeDealsData,
    layoutPreferenceVisitsData,
    layoutPreferenceRevisitsData,
    layoutPreferenceDealsData,
    floorPreferenceVisitsData,
    floorPreferenceRevisitsData,
    floorPreferenceDealsData,
    budgetPreferenceVisitsData,
    budgetPreferenceRevisitsData,
    budgetPreferenceDealsData,
    buildingSalesStatusData,
    parkingSalesStatusData,
    parkingSummaryData,
  } = reportData;

  // Column definitions (moved from WeeklyReportPage)
  const baseAnalysisColumns: TableProps<any>['columns'] = [
    { title: '項目', dataIndex: 'item', key: 'item', fixed: 'left', width: 120 },
    { title: '前期累計', dataIndex: 'previousTotal', key: 'previousTotal', width: 90, align: 'right' },
    { title: '前3週', dataIndex: 'prev3Weeks', key: 'prev3Weeks', width: 70, align: 'right' },
    { title: '前2週', dataIndex: 'prev2Weeks', key: 'prev2Weeks', width: 70, align: 'right' },
    { title: '前1週', dataIndex: 'prev1Week', key: 'prev1Week', width: 70, align: 'right' },
    { title: '本週', dataIndex: 'thisWeek', key: 'thisWeek', width: 70, align: 'right' },
    { title: '累計', dataIndex: 'cumulativeTotal', key: 'cumulativeTotal', width: 90, align: 'right' },
    { title: '比例', dataIndex: 'percentage', key: 'percentage', fixed: 'right', width: 80, align: 'right' },
  ];

  const ageAnalysisColumns: TableProps<AgeAnalysisDataType>['columns'] = [
    { title: '年齡區間', dataIndex: 'ageRange', key: 'ageRange', fixed: 'left', width: 120 },
    ...baseAnalysisColumns.slice(1)
  ];

  const regionBaseAnalysisColumns: TableProps<RegionAnalysisBaseType>['columns'] = [
    { title: '項目', dataIndex: 'region', key: 'region', fixed: 'left', width: 100 },
    ...baseAnalysisColumns.slice(1)
  ];

  const regionDealAnalysisColumns: TableProps<RegionAnalysisDealType>['columns'] = [
    { title: '項目', dataIndex: 'region', key: 'region', fixed: 'left', width: 100 },
    { title: '前期累計', dataIndex: 'previousTotal', key: 'previousTotal', width: 90, align: 'right' },
    { title: '前3週', dataIndex: 'prev3Weeks', key: 'prev3Weeks', width: 70, align: 'right' },
    { title: '前2週', dataIndex: 'prev2Weeks', key: 'prev2Weeks', width: 70, align: 'right' },
    { title: '前1週', dataIndex: 'prev1Week', key: 'prev1Week', width: 70, align: 'right' },
    { title: '本週', dataIndex: 'thisWeek', key: 'thisWeek', width: 70, align: 'right' },
    { title: '累計', dataIndex: 'cumulativeTotal', key: 'cumulativeTotal', width: 90, align: 'right' },
    { title: '比例', dataIndex: 'percentage', key: 'percentage', width: 80, align: 'right' },
    { title: '來人成交比', dataIndex: 'visitDealRatio', key: 'visitDealRatio', width: 100, align: 'right' },
    { title: '週成交狀況', dataIndex: 'weeklyDealStatus', key: 'weeklyDealStatus', fixed: 'right', width: 100, align: 'right' },
  ];

  const mediaAnalysisCallColumns: TableProps<MediaAnalysisDataType>['columns'] = [
    { title: '媒體項目', dataIndex: 'mediaItem', key: 'mediaItem', fixed: 'left', width: 120 },
    ...baseAnalysisColumns.slice(1)
  ];

  const areaPreferenceColumns: TableProps<AreaPreferenceDataType>['columns'] = [
    { title: '坪數區間', dataIndex: 'areaRange', key: 'areaRange', fixed: 'left', width: 120 },
    ...baseAnalysisColumns.slice(1)
  ];
  const areaPreferenceDealColumns: TableProps<AreaPreferenceDealDataType>['columns'] = [
    { title: '坪數區間', dataIndex: 'areaRange', key: 'areaRange', fixed: 'left', width: 120 },
    { title: '前期累計', dataIndex: 'previousTotal', key: 'previousTotal', width: 90, align: 'right' },
    { title: '前3週', dataIndex: 'prev3Weeks', key: 'prev3Weeks', width: 70, align: 'right' },
    { title: '前2週', dataIndex: 'prev2Weeks', key: 'prev2Weeks', width: 70, align: 'right' },
    { title: '前1週', dataIndex: 'prev1Week', key: 'prev1Week', width: 70, align: 'right' },
    { title: '本週', dataIndex: 'thisWeek', key: 'thisWeek', width: 70, align: 'right' },
    { title: '累計', dataIndex: 'cumulativeTotal', key: 'cumulativeTotal', width: 90, align: 'right' },
    { title: '比例', dataIndex: 'percentage', key: 'percentage', width: 80, align: 'right' },
    { title: '來人成交比', dataIndex: 'visitDealRatio', key: 'visitDealRatio', width: 100, align: 'right' },
    { title: '回訪成交比', dataIndex: 'revisitDealRatio', key: 'revisitDealRatio', fixed: 'right', width: 100, align: 'right' },
  ];

  const purchasePurposeColumns: TableProps<PurchasePurposeDataType>['columns'] = [
    { title: '購屋用途', dataIndex: 'purpose', key: 'purpose', fixed: 'left', width: 120 },
    ...baseAnalysisColumns.slice(1)
  ];
  const purchasePurposeDealColumns: TableProps<PurchasePurposeDealDataType>['columns'] = [
    { title: '購屋用途', dataIndex: 'purpose', key: 'purpose', fixed: 'left', width: 120 },
    { title: '前期累計', dataIndex: 'previousTotal', key: 'previousTotal', width: 90, align: 'right' },
    { title: '前3週', dataIndex: 'prev3Weeks', key: 'prev3Weeks', width: 70, align: 'right' },
    { title: '前2週', dataIndex: 'prev2Weeks', key: 'prev2Weeks', width: 70, align: 'right' },
    { title: '前1週', dataIndex: 'prev1Week', key: 'prev1Week', width: 70, align: 'right' },
    { title: '本週', dataIndex: 'thisWeek', key: 'thisWeek', width: 70, align: 'right' },
    { title: '累計', dataIndex: 'cumulativeTotal', key: 'cumulativeTotal', width: 90, align: 'right' },
    { title: '比例', dataIndex: 'percentage', key: 'percentage', width: 80, align: 'right' },
    { title: '來人成交比', dataIndex: 'visitDealRatio', key: 'visitDealRatio', width: 100, align: 'right' },
    { title: '回訪成交比', dataIndex: 'revisitDealRatio', key: 'revisitDealRatio', fixed: 'right', width: 100, align: 'right' },
  ];

  const layoutPreferenceColumns: TableProps<LayoutPreferenceDataType>['columns'] = [
    { title: '需求格局', dataIndex: 'layout', key: 'layout', fixed: 'left', width: 120 },
    ...baseAnalysisColumns.slice(1)
  ];
  const layoutPreferenceDealColumns: TableProps<LayoutPreferenceDealDataType>['columns'] = [
    { title: '需求格局', dataIndex: 'layout', key: 'layout', fixed: 'left', width: 120 },
    { title: '前期累計', dataIndex: 'previousTotal', key: 'previousTotal', width: 90, align: 'right' },
    { title: '前3週', dataIndex: 'prev3Weeks', key: 'prev3Weeks', width: 70, align: 'right' },
    { title: '前2週', dataIndex: 'prev2Weeks', key: 'prev2Weeks', width: 70, align: 'right' },
    { title: '前1週', dataIndex: 'prev1Week', key: 'prev1Week', width: 70, align: 'right' },
    { title: '本週', dataIndex: 'thisWeek', key: 'thisWeek', width: 70, align: 'right' },
    { title: '累計', dataIndex: 'cumulativeTotal', key: 'cumulativeTotal', width: 90, align: 'right' },
    { title: '比例', dataIndex: 'percentage', key: 'percentage', width: 80, align: 'right' },
    { title: '來人成交比', dataIndex: 'visitDealRatio', key: 'visitDealRatio', width: 100, align: 'right' },
    { title: '回訪成交比', dataIndex: 'revisitDealRatio', key: 'revisitDealRatio', fixed: 'right', width: 100, align: 'right' },
  ];

  const floorPreferenceColumns: TableProps<FloorPreferenceDataType>['columns'] = [
    { title: '樓層喜好', dataIndex: 'floorRange', key: 'floorRange', fixed: 'left', width: 120 },
    ...baseAnalysisColumns.slice(1)
  ];
  const floorPreferenceDealColumns: TableProps<FloorPreferenceDealDataType>['columns'] = [
    { title: '樓層喜好', dataIndex: 'floorRange', key: 'floorRange', fixed: 'left', width: 120 },
    { title: '前期累計', dataIndex: 'previousTotal', key: 'previousTotal', width: 90, align: 'right' },
    { title: '前3週', dataIndex: 'prev3Weeks', key: 'prev3Weeks', width: 70, align: 'right' },
    { title: '前2週', dataIndex: 'prev2Weeks', key: 'prev2Weeks', width: 70, align: 'right' },
    { title: '前1週', dataIndex: 'prev1Week', key: 'prev1Week', width: 70, align: 'right' },
    { title: '本週', dataIndex: 'thisWeek', key: 'thisWeek', width: 70, align: 'right' },
    { title: '累計', dataIndex: 'cumulativeTotal', key: 'cumulativeTotal', width: 90, align: 'right' },
    { title: '比例', dataIndex: 'percentage', key: 'percentage', width: 80, align: 'right' },
    { title: '來人成交比', dataIndex: 'visitDealRatio', key: 'visitDealRatio', width: 100, align: 'right' },
    { title: '回訪成交比', dataIndex: 'revisitDealRatio', key: 'revisitDealRatio', fixed: 'right', width: 100, align: 'right' },
  ];

  const budgetPreferenceColumns: TableProps<BudgetPreferenceDataType>['columns'] = [
    { title: '預算區間', dataIndex: 'budgetRange', key: 'budgetRange', fixed: 'left', width: 120 },
    ...baseAnalysisColumns.slice(1)
  ];
  const budgetPreferenceDealColumns: TableProps<BudgetPreferenceDealDataType>['columns'] = [
    { title: '預算區間', dataIndex: 'budgetRange', key: 'budgetRange', fixed: 'left', width: 120 },
    { title: '前期累計', dataIndex: 'previousTotal', key: 'previousTotal', width: 90, align: 'right' },
    { title: '前3週', dataIndex: 'prev3Weeks', key: 'prev3Weeks', width: 70, align: 'right' },
    { title: '前2週', dataIndex: 'prev2Weeks', key: 'prev2Weeks', width: 70, align: 'right' },
    { title: '前1週', dataIndex: 'prev1Week', key: 'prev1Week', width: 70, align: 'right' },
    { title: '本週', dataIndex: 'thisWeek', key: 'thisWeek', width: 70, align: 'right' },
    { title: '累計', dataIndex: 'cumulativeTotal', key: 'cumulativeTotal', width: 90, align: 'right' },
    { title: '比例', dataIndex: 'percentage', key: 'percentage', width: 80, align: 'right' },
    { title: '來人成交比', dataIndex: 'visitDealRatio', key: 'visitDealRatio', width: 100, align: 'right' },
    { title: '回訪成交比', dataIndex: 'revisitDealRatio', key: 'revisitDealRatio', fixed: 'right', width: 100, align: 'right' },
  ];

  const parkingSummaryColumns: TableProps<ParkingSummaryStatus>['columns'] = [
    { title: '樓層', dataIndex: 'level', key: 'level', width: 60 },
    { title: '車位類型', dataIndex: 'type', key: 'type', width: 80 },
    { title: '可售', dataIndex: 'available', key: 'available', width: 60, align: 'right' },
    { title: '已售', dataIndex: 'sold', key: 'sold', width: 60, align: 'right' },
    { title: '去化百分比', dataIndex: 'percentage', key: 'percentage', width: 100, align: 'right' },
  ];

  return (
    <div className="space-y-6 weekly-report-content-wrapper"> {/* Added a wrapper class */}


      <div className="bg-white p-4 rounded shadow mt-6">
        <h2 className="text-xl font-semibold mb-4">重大產經新聞</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full border border-gray-200 text-center">
            <thead className="bg-gray-100">
              <tr>
                <th className="border p-2">週次</th>
                {marketNewsData.map((news, index) => (
                  <th key={index} className="border p-2">
                    {news.week}<br /><span className="text-sm">{`(${getWeekDates(index)})`}</span>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border p-2">房市新聞</td>
                {marketNewsData.map((news, index) => <td key={index} className="border p-2">{news.housing}</td>)}
              </tr>
              <tr>
                <td className="border p-2">國內產經新聞</td>
                {marketNewsData.map((news, index) => <td key={index} className="border p-2">{news.domestic}</td>)}
              </tr>
              <tr>
                <td className="border p-2">國際新聞</td>
                {marketNewsData.map((news, index) => <td key={index} className="border p-2">{news.international}</td>)}
              </tr>
              <tr>
                <td className="border p-2">股市走勢圖</td>
                {marketNewsData.map((news, index) => <td key={index} className="border p-2 text-blue-600">{news.stockIndex}</td>)}
              </tr>
            </tbody>
          </table>
        </div>
        <div className="mt-4">
          <EChart option={stockChartOption} style={{ height: 200 }} showDownload={showEchartDownload} />
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-4">
        <section className="bg-white p-4 rounded shadow">
          <h2 className="text-xl font-semibold mb-2">本週成交促進</h2>
          <ul className="list-decimal ml-5 space-y-1">
            {dealPromotionList.map((item, idx) => <li key={idx}>{item}</li>)}
          </ul>
        </section>
        <section className="bg-white p-4 rounded shadow">
          <h2 className="text-xl font-semibold mb-2">本週來人促進</h2>
          <ul className="list-decimal ml-5 space-y-1">
            {visitorPromotionList.map((item, idx) => <li key={idx}>{item}</li>)}
          </ul>
        </section>
      </div>

      <div className="bg-white p-4 rounded shadow">
        <h2 className="text-xl font-semibold mb-4">本週媒體投放統計</h2>
        <Table
          columns={mediaPromotionColumns}
          dataSource={mediaPromotionData.map(item => ({ ...item, key: item.key }))}
          pagination={false}
          bordered
          scroll={{ x: 800 }}
        />
      </div>

      {/* This div now contains all the report sections that were previously in WeeklyReportPage */}
      <Table
        columns={summaryColumns}
        dataSource={summary.map(item => ({ ...item, key: item.key }))}
        pagination={false}
        bordered
        scroll={{ x: 1200 }}
      />

      <div className="bg-white p-4 rounded shadow">
        <EChart option={mainChartOption} style={{ height: 300 }} showDownload={showEchartDownload} />
      </div>

      <div className="bg-white p-4 rounded shadow mt-6">
        <h2 className="text-xl font-semibold mb-4">週經營成果表</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full border-collapse border border-gray-200 text-center">
            <thead className="bg-gray-100">
              <tr>
                <th rowSpan={2} className="border p-2">來人來電成交統計</th>
                <th rowSpan={2} className="border p-2">前期累計</th>
                <th rowSpan={2} className="border p-2">本週小計</th>
                <th rowSpan={2} className="border p-2">累計</th>
              </tr>
              <tr>
                {['7/22', '7/23', '7/24', '7/25', '7/26', '7/27', '7/28'].map(date => (
                  <th key={date} className="border p-2">{date}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {weeklyOperationStatsData.map((stat) => (
                <tr key={stat.key}>
                  <td className="border p-2">{stat.item}</td>
                  <td className="border p-2">{stat.previousTotal}</td>
                  {stat.daily.map((v, i) => <td key={i} className="border p-2">{v}</td>)}
                  <td className="border p-2">{stat.weeklyTotal}</td>
                  <td className="border p-2">{stat.cumulativeTotal}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mt-4">
          <h2 className="text-xl font-semibold mb-2">週經營成果圖</h2>
          <EChart option={operationChartOption} style={{ height: 300 }} showDownload={showEchartDownload} />
        </div>
      </div>

      <div className="bg-white p-4 rounded shadow mt-6">
        <h2 className="text-xl font-semibold mb-4">A. 貴賓背景類分析</h2>
        <div className="space-y-8">
          <section>
            <h3 className="font-medium text-lg mb-3">一、年齡</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">來人</h4>
                <Table
                  columns={ageAnalysisColumns}
                  dataSource={ageAnalysisData.visits.map((d) => ({ ...d, key: d.ageRange }))}
                  pagination={false}
                  bordered
                  scroll={{ x: 700 }}
                />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">回訪(次)</h4>
                <Table
                  columns={ageAnalysisColumns}
                  dataSource={ageAnalysisData.revisits.map((d) => ({ ...d, key: d.ageRange }))}
                  pagination={false}
                  bordered
                  scroll={{ x: 700 }}
                />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">成交</h4>
                <Table
                  columns={ageAnalysisColumns}
                  dataSource={ageAnalysisData.deals.map((d) => ({ ...d, key: d.ageRange }))}
                  pagination={false}
                  bordered
                  scroll={{ x: 700 }}
                />
              </div>
            </div>
          </section>

          <section>
            <h3 className="font-medium text-lg mb-3">二、產業</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">來人</h4>
                <Table
                  columns={baseAnalysisColumns}
                  dataSource={industryAnalysisData.visits.map((d) => ({ ...d, key: d.item }))}
                  pagination={false}
                  bordered
                  scroll={{ x: 700 }}
                />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">回訪(次)</h4>
                <Table
                  columns={baseAnalysisColumns}
                  dataSource={industryAnalysisData.revisits.map((d) => ({ ...d, key: d.item }))}
                  pagination={false}
                  bordered
                  scroll={{ x: 700 }}
                />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">成交</h4>
                <Table
                  columns={baseAnalysisColumns}
                  dataSource={industryAnalysisData.deals.map((d) => ({ ...d, key: d.item }))}
                  pagination={false}
                  bordered
                  scroll={{ x: 700 }}
                />
              </div>
            </div>
          </section>

          <section>
            <h3 className="font-medium text-lg mb-3">三、區域</h3>
            <div className="space-y-6">
              <section className="bg-gray-100 p-4 rounded">
                <h4 className="text-md font-semibold mb-3">1. 台北市</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">來電</h5>
                    <Table columns={regionBaseAnalysisColumns} dataSource={taipeiRegionCallsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 700 }} />
                  </div>
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">來人</h5>
                    <Table columns={regionBaseAnalysisColumns} dataSource={taipeiRegionVisitsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 700 }} />
                  </div>
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">回訪(次)</h5>
                    <Table columns={regionBaseAnalysisColumns} dataSource={taipeiRegionRevisitsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 700 }} />
                  </div>
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">成交</h5>
                    <Table columns={regionDealAnalysisColumns} dataSource={taipeiRegionDealsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 850 }} />
                  </div>
                </div>
              </section>
              <section className="bg-gray-100 p-4 rounded">
                <h4 className="text-md font-semibold mb-3">2. 新北市</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">來電</h5>
                    <Table columns={regionBaseAnalysisColumns} dataSource={newTaipeiRegionCallsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 700 }} />
                  </div>
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">來人</h5>
                    <Table columns={regionBaseAnalysisColumns} dataSource={newTaipeiRegionVisitsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 700 }} />
                  </div>
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">回訪(次)</h5>
                    <Table columns={regionBaseAnalysisColumns} dataSource={newTaipeiRegionRevisitsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 700 }} />
                  </div>
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">成交</h5>
                    <Table columns={regionDealAnalysisColumns} dataSource={newTaipeiRegionDealsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 850 }} />
                  </div>
                </div>
              </section>
              <section className="bg-gray-100 p-4 rounded">
                <h4 className="text-md font-semibold mb-3">3. 其他</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">來電</h5>
                    <Table columns={regionBaseAnalysisColumns} dataSource={otherRegionCallsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 700 }} />
                  </div>
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">來人</h5>
                    <Table columns={regionBaseAnalysisColumns} dataSource={otherRegionVisitsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 700 }} />
                  </div>
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">回訪(次)</h5>
                    <Table columns={regionBaseAnalysisColumns} dataSource={otherRegionRevisitsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 700 }} />
                  </div>
                  <div className="overflow-x-auto bg-white p-2 rounded shadow-sm">
                    <h5 className="text-sm font-medium mb-1 text-center">成交</h5>
                    <Table columns={regionDealAnalysisColumns} dataSource={otherRegionDealsData.map((d) => ({ ...d, key: d.region }))} pagination={false} bordered scroll={{ x: 850 }} />
                  </div>
                </div>
              </section>
            </div>
          </section>
        </div>
      </div>

      <div className="bg-white p-4 rounded shadow mt-6">
        <h2 className="text-xl font-semibold mb-4">B. 媒體類分析</h2>
        <div className="overflow-x-auto bg-gray-50 p-3 rounded">
          <h3 className="font-medium text-lg mb-3">一、媒體 (可複選) - 來電</h3>
          <Table
            columns={mediaAnalysisCallColumns}
            dataSource={mediaAnalysisCallsData.map((d) => ({ ...d, key: d.mediaItem }))}
            pagination={false}
            bordered
            scroll={{ x: 700 }}
          />
        </div>
      </div>

      <div className="bg-white p-4 rounded shadow mt-6">
        <h2 className="text-xl font-semibold mb-4">C. 貴賓偏好類分析</h2>
        <div className="space-y-8">
          <section>
            <h3 className="font-medium text-lg mb-3">一、需求坪數</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">來電</h4>
                <Table columns={areaPreferenceColumns} dataSource={areaPreferenceCallsData.map((d) => ({ ...d, key: d.areaRange }))} pagination={false} bordered scroll={{ x: 700 }} />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">來人</h4>
                <Table columns={areaPreferenceColumns} dataSource={areaPreferenceVisitsData.map((d) => ({ ...d, key: d.areaRange }))} pagination={false} bordered scroll={{ x: 700 }} />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">回訪(次)</h4>
                <Table columns={areaPreferenceColumns} dataSource={areaPreferenceRevisitsData.map((d) => ({ ...d, key: d.areaRange }))} pagination={false} bordered scroll={{ x: 700 }} />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">成交</h4>
                <Table columns={areaPreferenceDealColumns} dataSource={areaPreferenceDealsData.map((d) => ({ ...d, key: d.areaRange }))} pagination={false} bordered scroll={{ x: 850 }} />
              </div>
            </div>
          </section>

          <section>
            <h3 className="font-medium text-lg mb-3">二、購屋用途(複選)</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">來人</h4>
                <Table columns={purchasePurposeColumns} dataSource={purchasePurposeVisitsData.map((d) => ({ ...d, key: d.purpose }))} pagination={false} bordered scroll={{ x: 700 }} />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">回訪(次)</h4>
                <Table columns={purchasePurposeColumns} dataSource={purchasePurposeRevisitsData.map((d) => ({ ...d, key: d.purpose }))} pagination={false} bordered scroll={{ x: 700 }} />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">成交</h4>
                <Table columns={purchasePurposeDealColumns} dataSource={purchasePurposeDealsData.map((d) => ({ ...d, key: d.purpose }))} pagination={false} bordered scroll={{ x: 850 }} />
              </div>
            </div>
          </section>

          <section>
            <h3 className="font-medium text-lg mb-3">三、需求格局</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">來人</h4>
                <Table columns={layoutPreferenceColumns} dataSource={layoutPreferenceVisitsData.map((d) => ({ ...d, key: d.layout }))} pagination={false} bordered scroll={{ x: 700 }} />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">回訪(次)</h4>
                <Table columns={layoutPreferenceColumns} dataSource={layoutPreferenceRevisitsData.map((d) => ({ ...d, key: d.layout }))} pagination={false} bordered scroll={{ x: 700 }} />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">成交</h4>
                <Table columns={layoutPreferenceDealColumns} dataSource={layoutPreferenceDealsData.map((d) => ({ ...d, key: d.layout }))} pagination={false} bordered scroll={{ x: 850 }} />
              </div>
            </div>
          </section>

          <section>
            <h3 className="font-medium text-lg mb-3">四、樓層喜好</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">來人</h4>
                <Table columns={floorPreferenceColumns} dataSource={floorPreferenceVisitsData.map((d) => ({ ...d, key: d.floorRange }))} pagination={false} bordered scroll={{ x: 700 }} />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">回訪(次)</h4>
                <Table columns={floorPreferenceColumns} dataSource={floorPreferenceRevisitsData.map((d) => ({ ...d, key: d.floorRange }))} pagination={false} bordered scroll={{ x: 700 }} />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">成交</h4>
                <Table columns={floorPreferenceDealColumns} dataSource={floorPreferenceDealsData.map((d) => ({ ...d, key: d.floorRange }))} pagination={false} bordered scroll={{ x: 850 }} />
              </div>
            </div>
          </section>

          <section>
            <h3 className="font-medium text-lg mb-3">五、購屋預算</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">來人</h4>
                <Table columns={budgetPreferenceColumns} dataSource={budgetPreferenceVisitsData.map((d) => ({ ...d, key: d.budgetRange }))} pagination={false} bordered scroll={{ x: 700 }} />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">回訪(次)</h4>
                <Table columns={budgetPreferenceColumns} dataSource={budgetPreferenceRevisitsData.map((d) => ({ ...d, key: d.budgetRange }))} pagination={false} bordered scroll={{ x: 700 }} />
              </div>
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">成交</h4>
                <Table columns={budgetPreferenceDealColumns} dataSource={budgetPreferenceDealsData.map((d) => ({ ...d, key: d.budgetRange }))} pagination={false} bordered scroll={{ x: 850 }} />
              </div>
            </div>
          </section>
        </div>
      </div>

      <div className="bg-white p-4 rounded shadow mt-6">
        <h2 className="text-xl font-semibold mb-4">D. 佳元當代美術館 去化分析</h2>
        <div className="space-y-8">
          <section>
            <h3 className="font-medium text-lg mb-3">戶別去化狀態</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse border border-gray-300 text-center text-sm">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border border-gray-300 p-1 w-[5%]">樓層</th>
                    <th className="border border-gray-300 p-1 w-[12%]">A</th>
                    <th className="border border-gray-300 p-1 w-[12%]">B</th>
                    <th className="border border-gray-300 p-1 w-[12%]">C</th>
                    <th className="border border-gray-300 p-1 w-[12%]">D</th>
                    <th className="border border-gray-300 p-1 w-[12%]">E</th>
                    <th className="border border-gray-300 p-1 w-[12%]">F</th>
                    <th className="border border-gray-300 p-1 w-[5%]">樓層</th>
                    <th className="border border-gray-300 p-1 w-[5%]">可售</th>
                    <th className="border border-gray-300 p-1 w-[5%]">已售</th>
                    <th className="border border-gray-300 p-1 w-[8%]">去化百分比</th>
                  </tr>
                </thead>
                <tbody>
                  {buildingSalesStatusData.map((row, index) => (
                    <tr key={index}>
                      <td className="border border-gray-300 p-1 font-medium">{row.floor}</td>
                      <td className={`border border-gray-300 p-1 ${getUnitCellStyle(row.unitA)}`}>{typeof row.unitA === 'number' || typeof row.unitA === 'string' ? row.unitA : ''}</td>
                      <td className={`border border-gray-300 p-1 ${getUnitCellStyle(row.unitB)}`}>{typeof row.unitB === 'number' || typeof row.unitB === 'string' ? row.unitB : ''}</td>
                      <td className={`border border-gray-300 p-1 ${getUnitCellStyle(row.unitC)}`}>{typeof row.unitC === 'number' || typeof row.unitC === 'string' ? row.unitC : ''}</td>
                      <td className={`border border-gray-300 p-1 ${getUnitCellStyle(row.unitD)}`}>{typeof row.unitD === 'number' || typeof row.unitD === 'string' ? row.unitD : ''}</td>
                      <td className={`border border-gray-300 p-1 ${getUnitCellStyle(row.unitE)}`}>{typeof row.unitE === 'number' || typeof row.unitE === 'string' ? row.unitE : ''}</td>
                      <td className={`border border-gray-300 p-1 ${getUnitCellStyle(row.unitF)}`}>{typeof row.unitF === 'number' || typeof row.unitF === 'string' ? row.unitF : ''}</td>
                      <td className="border border-gray-300 p-1 font-medium">{row.floorSummary}</td>
                      <td className="border border-gray-300 p-1 text-right">{row.available}</td>
                      <td className="border border-gray-300 p-1 text-right">{row.sold}</td>
                      <td className="border border-gray-300 p-1 text-right">{row.percentage}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </section>

          <section>
            <h3 className="font-medium text-lg mb-3">車位去化狀態</h3>
            <div className="grid grid-cols gap-6">
              <div className="overflow-x-auto bg-gray-50 p-3 rounded">
                <h4 className="text-base font-semibold mb-2">車位統計</h4>
                <Table
                  columns={parkingSummaryColumns}
                  dataSource={parkingSummaryData.map(d => ({ ...d, key: `${d.level}-${d.type}` }))}
                  pagination={false}
                  bordered
                />
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default WeeklyReportContent; 