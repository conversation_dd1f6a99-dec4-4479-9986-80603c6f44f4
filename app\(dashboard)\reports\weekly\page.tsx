'use client';

import React, { useState, useEffect } from 'react';
import { Spin, Button, TableProps } from 'antd';
import Link from 'next/link';
import { PrinterOutlined } from '@ant-design/icons';
import {
  getWeeklyReportData,
} from './weekly-report.service';
import WeeklyReportContent, { ReportDataShape } from './WeeklyReportContent';

export default function WeeklyReportPage() {
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState<ReportDataShape | null>(null);
  const [currentHost, setCurrentHost] = useState('');

  useEffect(() => {
    setCurrentHost(window.location.origin);
    const fetchData = async () => {
      try {
        const data = await getWeeklyReportData();
        setReportData(data);
      } catch (error) {
        console.error("Failed to fetch weekly report data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handlePrint = () => {
    if (reportData) {
      localStorage.setItem('weeklyReportDataForPrint', JSON.stringify(reportData));
      const printUrl = `${currentHost}/a-life/print_versions/reports/weekly`;
      window.open(printUrl, '_blank');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" tip="正在載入報表...">
          <div style={{ padding: 50, background: 'rgba(0, 0, 0, 0.05)', borderRadius: 4 }} />
        </Spin>
      </div>
    );
  }

  if (!reportData) {
    return <div className="p-4 text-center text-red-500">無法載入報表資料。</div>;
  }

  return (
    <>
      <style jsx global>{`
        @media print {
          body {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          .ant-layout-sider,
          .ant-layout-header,
          .non-printable {
            display: none !important;
          }
          .ant-layout-content {
            margin: 0 !important;
          }
        }
      `}</style>
      <div className="p-4 space-y-6 bg-gray-50 report-content-wrapper weekly-report-container">
        <header className="flex justify-between items-end non-printable">
          <div>
            <h1 className="text-2xl font-bold">第17週 週報表</h1>
            <span className="text-sm text-gray-600">日期: 2024/07/22 - 2024/07/28</span>
          </div>
          <Button icon={<PrinterOutlined />} type="primary" onClick={handlePrint}>
            列印
          </Button>
        </header>

        <WeeklyReportContent reportData={reportData} showEchartDownload={true} />
      </div>
    </>
  );
} 