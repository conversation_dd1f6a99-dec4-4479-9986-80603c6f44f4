import type { TableProps } from 'antd';

// 摘要資料類型
interface SummaryDataType {
  key: string;
  type: string;
  available: number;
  availableAmount: string;
  soldCount: number;
  soldBaseAmount: string;
  soldTotal: string;
  soldUnitPrice: string;
  soldRate: string;
  unsoldCount: number;
  unsoldAmount: string;
  unsoldRate: string;
}

// 媒體資料類型
interface MediaDataType {
  key: string;
  channel: string;
  days: string;
  detail: string;
  calls: number;
  visitors: number;
}

// 房市新聞資料類型
interface NewsDataType {
  week: string;
  housing: string;
  domestic: string;
  international: string;
  stockIndex: number | string;
}

// 經營成果統計資料類型
interface OperationStatsDataType {
  key: string;
  item: string;
  previousTotal: number;
  daily: number[];
  weeklyTotal: number;
  cumulativeTotal: number;
}

// 貴賓背景分析資料類型 (通用)
interface GuestAnalysisBaseType {
  item: string;
  previousTotal: number;
  prev3Weeks: number | string;
  prev2Weeks: number | string;
  prev1Week: number | string;
  thisWeek: number | string;
  cumulativeTotal: number;
  percentage: string;
}

// 貴賓背景分析 - 性別
// interface GenderAnalysisDataType extends GuestAnalysisBaseType { }
// 貴賓背景分析 - 年齡
interface AgeAnalysisDataType extends GuestAnalysisBaseType {
  ageRange: string;
}
// 貴賓背景分析 - 產業
interface IndustryAnalysisDataType extends GuestAnalysisBaseType { }
// 貴賓背景分析 - 區域 (基礎)
interface RegionAnalysisBaseType {
  key: string; // Use district name or 'other' as key
  region: string;
  previousTotal: number | string; // Allow string for '-'
  prev3Weeks: number | string;
  prev2Weeks: number | string;
  prev1Week: number | string;
  thisWeek: number | string;
  cumulativeTotal: number | string;
  percentage: string;
}
// 貴賓背景分析 - 區域成交 (擴展)
interface RegionAnalysisDealType extends RegionAnalysisBaseType {
  visitDealRatio: string; // 來人成交比
  weeklyDealStatus: string; // 週成交狀況
}
// 媒體分析資料類型
interface MediaAnalysisDataType extends GuestAnalysisBaseType {
  mediaItem: string;
}

// C. 貴賓偏好類分析 - 通用 Deal Type
interface GuestAnalysisDealType extends GuestAnalysisBaseType {
  visitDealRatio: string; // 來人成交比
  revisitDealRatio?: string; // 回訪成交比 (有些 Deal 表格有此欄位)
}

// C.1 需求坪數
interface AreaPreferenceDataType extends GuestAnalysisBaseType {
  areaRange: string; // e.g., '60坪以上'
}
interface AreaPreferenceDealDataType extends GuestAnalysisDealType {
  areaRange: string;
}

// C.2 購屋用途
interface PurchasePurposeDataType extends GuestAnalysisBaseType {
  purpose: string; // e.g., '小換大'
}
interface PurchasePurposeDealDataType extends GuestAnalysisDealType {
  purpose: string;
}

// C.3 需求格局
interface LayoutPreferenceDataType extends GuestAnalysisBaseType {
  layout: string; // e.g., '4房'
}
interface LayoutPreferenceDealDataType extends GuestAnalysisDealType {
  layout: string;
}

// C.4 樓層喜好
interface FloorPreferenceDataType extends GuestAnalysisBaseType {
  floorRange: string; // e.g., '11~15樓中樓層'
}
interface FloorPreferenceDealDataType extends GuestAnalysisDealType {
  floorRange: string;
}

// C.5 購屋預算
interface BudgetPreferenceDataType extends GuestAnalysisBaseType {
  budgetRange: string; // e.g., '8001萬以上'
}
interface BudgetPreferenceDealDataType extends GuestAnalysisDealType {
  budgetRange: string;
}


// 定義摘要資料
const summaryData: SummaryDataType[] = [
  { key: 'house', type: '戶', available: 69, availableAmount: '307,200', soldCount: 24, soldBaseAmount: '89,715', soldTotal: '94,735', soldUnitPrice: '134.74萬/坪', soldRate: '34.78%', unsoldCount: 45, unsoldAmount: '217,485', unsoldRate: '65.22%' },
  { key: 'car', type: '車', available: 55, availableAmount: '19,250', soldCount: 15, soldBaseAmount: '5,020', soldTotal: '？', soldUnitPrice: '374.67萬/個', soldRate: '34.78%', unsoldCount: 40, unsoldAmount: '14,230', unsoldRate: '65.22%' }
];

// 定義摘要表格欄位
const summaryColumns: TableProps<SummaryDataType>['columns'] = [
  { title: '可售', dataIndex: 'available', key: 'available', render: (v, r) => `${r.available} ${r.type}` },
  { title: '可售金額', dataIndex: 'availableAmount', key: 'availableAmount' },
  { title: '已售', dataIndex: 'soldCount', key: 'soldCount', render: (v, r) => `${r.soldCount} ${r.type}` },
  { title: '已售底價金額', dataIndex: 'soldBaseAmount', key: 'soldBaseAmount' },
  { title: '已簽合計', dataIndex: 'soldTotal', key: 'soldTotal' },
  { title: '已簽合計成交單價', dataIndex: 'soldUnitPrice', key: 'soldUnitPrice' },
  { title: '銷售率', dataIndex: 'soldRate', key: 'soldRate' },
  { title: '未售', dataIndex: 'unsoldCount', key: 'unsoldCount', render: (v, r) => `${r.unsoldCount} ${r.type}` },
  { title: '未售金額', dataIndex: 'unsoldAmount', key: 'unsoldAmount' },
  { title: '未售率', dataIndex: 'unsoldRate', key: 'unsoldRate' }
];

// 定義折線圖選項
const mainChartOption = {
  xAxis: { type: 'category', data: ['11月', '12月', '1月', '2月', '3月', '4月', '5月', '6月', '7月'] },
  yAxis: { type: 'value', name: '萬' },
  series: [{ data: [132.48, 131.65, 135.01, 135.99], type: 'line', smooth: true }],
  tooltip: { trigger: 'axis' }
};

// 定義來人促進列表
const visitorPromotionList: string[] = [
  'POP: 中山區廣告基地位置周...共計10點位',
  '網路媒體: Google關鍵字、591等',
  'FB行銷: 年齡面...等促進策略',
  'Google聯播網: 以品...',
  '樂居、BN-MAX等',
  '591平台: PC、行動裝置廣告',
  'RD: 廣播電台等播放',
  '7/11~8/25「微氣候展」'
];

// 定義成交促進列表
const dealPromotionList: string[] = [
  '主攻中高樓層、三房型產品',
  '現場營造熱銷氛圍策略',
  '關閉促案及簽約條件',
  '二房溫嚴篩住方案、三房渠道住付方式',
  '票固本案信元、增加5面手拿板',
  '魔化線行流程優化',
  '藝術品循環...'
];

// 定義媒體推廣資料
const mediaPromotionData: MediaDataType[] = [
  { key: '1', channel: '社群平台', days: '7天', detail: '訂製198萬起,民生松江 三捷流域', calls: 2, visitors: 5 },
  { key: '2', channel: '網路平台', days: '7天', detail: 'Google關鍵字,591,樂居', calls: 2, visitors: 4 },
  { key: '3', channel: '戶外看板', days: '9點', detail: '訂製198萬起,民生松江 三捷流域', calls: 3, visitors: 2 },
  { key: '4', channel: '接待中心/精神堡壘', days: '1座', detail: '佳元當代美術館', calls: 2, visitors: 5 },
  { key: '5', channel: '基地圖範', days: '0座', detail: '', calls: 0, visitors: 0 },
  { key: '6', channel: '介紹', days: '0天', detail: '', calls: 1, visitors: 2 },
  { key: '7', channel: 'PR', days: '-', detail: '美術館楊柏林大師展覽主題「微氣候」', calls: 0, visitors: 0 }
];

// 定義媒體推廣表格欄位
const mediaPromotionColumns: TableProps<MediaDataType>['columns'] = [
  { title: '媒體項目', dataIndex: 'channel', key: 'channel' },
  { title: '量(單位)', dataIndex: 'days', key: 'days' },
  { title: '廣告訴求及運用明細', dataIndex: 'detail', key: 'detail' },
  { title: '來電數', dataIndex: 'calls', key: 'calls' },
  { title: '來人數', dataIndex: 'visitors', key: 'visitors' }
];

// 定義房市重大產經新聞資料
const marketNewsData: NewsDataType[] = [
  { week: '第14週', housing: '新北市XX一次未售員月...', domestic: '台積電資本支出上升...', international: '大陸6月出口增速...', stockIndex: 23556.59 },
  { week: '第15週', housing: '北市XX房價穩定...', domestic: '台商回流投資熱...', international: '三中全會召開...', stockIndex: 23916.93 },
  { week: '第16週', housing: '北市房市成交價...', domestic: '美國市場復甦...', international: '美股同步上漲...', stockIndex: 22849.26 },
  { week: '第17週', housing: '買氣到員工要...', domestic: '台積電漲幅140...', international: '美股大反彈...', stockIndex: 22119.21 },
];

// 定義股市走勢圖選項
const stockChartOption = {
  xAxis: { type: 'category', data: ['第14週', '第15週', '第16週', '第17週'] },
  yAxis: { type: 'value', name: '指數' },
  series: [{ data: [23556.59, 23916.93, 22849.26, 22119.21], type: 'line', smooth: true }],
  tooltip: { trigger: 'axis' }
};

// 定義週經營成果表資料
const weeklyOperationStatsData: OperationStatsDataType[] = [
  { key: 'visits', item: '來人', previousTotal: 563, daily: [4, 2, 0, 0, 1, 2, 7], weeklyTotal: 16, cumulativeTotal: 579 },
  { key: 'calls', item: '來電', previousTotal: 284, daily: [0, 0, 0, 0, 1, 0, 6], weeklyTotal: 7, cumulativeTotal: 291 },
  { key: 'leads', item: '留單', previousTotal: 0, daily: [0, 0, 0, 0, 0, 0, 0], weeklyTotal: 0, cumulativeTotal: 0 },
  { key: 'deals', item: '成交(售)', previousTotal: 0, daily: [0, 0, 0, 0, 0, 0, 0], weeklyTotal: 0, cumulativeTotal: 0 },
];

// 定義週經營成果圖選項
const operationChartOption = {
  xAxis: { type: 'category', data: ['第14週', '第15週', '第16週', '第17週'] },
  yAxis: { type: 'value' },
  series: [
    { name: '來人', type: 'line', data: [12, 19, 23, 16], smooth: true },
    { name: '來電', type: 'line', data: [8, 8, 8, 7] },
    { name: '留單', type: 'line', data: [0, 0, 0, 0] },
    { name: '成交(售)', type: 'line', data: [0, 0, 0, 0] }
  ],
  tooltip: { trigger: 'axis' },
  legend: { data: ['來人', '來電', '留單', '成交(售)'] }
};

// 定義貴賓背景分析 - 性別資料
// const genderAnalysisData = {
//   calls: [
//     { item: '男', previousTotal: 111, prev3Weeks: 3, prev2Weeks: 5, prev1Week: 4, thisWeek: 2, cumulativeTotal: 125, percentage: '46%' },
//     { item: '女', previousTotal: 133, prev3Weeks: 3, prev2Weeks: 2, prev1Week: 4, thisWeek: 5, cumulativeTotal: 147, percentage: '54%' },
//     { item: '合計', previousTotal: 244, prev3Weeks: 6, prev2Weeks: 7, prev1Week: 8, thisWeek: 7, cumulativeTotal: 272, percentage: '100%' },
//   ] as GenderAnalysisDataType[],
//   visits: [
//     { item: '男', previousTotal: 260, prev3Weeks: 8, prev2Weeks: 15, prev1Week: 15, thisWeek: 6, cumulativeTotal: 304, percentage: '53%' },
//     { item: '女', previousTotal: 239, prev3Weeks: 4, prev2Weeks: 4, prev1Week: 15, thisWeek: 10, cumulativeTotal: 272, percentage: '47%' },
//     { item: '合計', previousTotal: 499, prev3Weeks: 12, prev2Weeks: 19, prev1Week: 30, thisWeek: 16, cumulativeTotal: 576, percentage: '100%' },
//   ] as GenderAnalysisDataType[],
//   revisits: [
//     { item: '男', previousTotal: 72, prev3Weeks: 4, prev2Weeks: 2, prev1Week: 3, thisWeek: 1, cumulativeTotal: 81, percentage: '61%' },
//     { item: '女', previousTotal: 44, prev3Weeks: 3, prev2Weeks: 2, prev1Week: 0, thisWeek: 1, cumulativeTotal: 51, percentage: '39%' },
//     { item: '合計', previousTotal: 116, prev3Weeks: 7, prev2Weeks: 4, prev1Week: 3, thisWeek: 2, cumulativeTotal: 132, percentage: '100%' },
//   ] as GenderAnalysisDataType[],
//   deals: [
//     { item: '男', previousTotal: 7, prev3Weeks: 2, prev2Weeks: 1, prev1Week: 0, thisWeek: 0, cumulativeTotal: 10, percentage: '42%' },
//     { item: '女', previousTotal: 9, prev3Weeks: 1, prev2Weeks: 1, prev1Week: 3, thisWeek: 0, cumulativeTotal: 14, percentage: '58%' },
//     { item: '合計', previousTotal: 16, prev3Weeks: 3, prev2Weeks: 2, prev1Week: 3, thisWeek: 0, cumulativeTotal: 24, percentage: '100%' },
//   ] as GenderAnalysisDataType[],
// };

// 定義貴賓背景分析 - 年齡資料
const ageAnalysisData = {
  visits: [
    { ageRange: '60歲以上', item: '60歲以上', previousTotal: 23, prev3Weeks: 2, prev2Weeks: '-', prev1Week: '-', thisWeek: 1, cumulativeTotal: 25, percentage: '19%' },
    { ageRange: '56~60歲', item: '56~60歲', previousTotal: 9, prev3Weeks: 1, prev2Weeks: '-', prev1Week: 1, thisWeek: '-', cumulativeTotal: 11, percentage: '8%' },
    { ageRange: '合計', item: '合計', previousTotal: 32, prev3Weeks: 3, prev2Weeks: '-', prev1Week: 1, thisWeek: 1, cumulativeTotal: 36, percentage: '100%' }, // Assuming item is ageRange for consistency in display
  ] as AgeAnalysisDataType[],
  revisits: [
    { ageRange: '60歲以上', item: '60歲以上', previousTotal: 23, prev3Weeks: 2, prev2Weeks: '-', prev1Week: '-', thisWeek: 1, cumulativeTotal: 25, percentage: '19%' },
    { ageRange: '56~60歲', item: '56~60歲', previousTotal: 9, prev3Weeks: 1, prev2Weeks: '-', prev1Week: 1, thisWeek: '-', cumulativeTotal: 11, percentage: '8%' },
    { ageRange: '合計', item: '合計', previousTotal: 32, prev3Weeks: 3, prev2Weeks: '-', prev1Week: 1, thisWeek: 1, cumulativeTotal: 36, percentage: '100%' },
  ] as AgeAnalysisDataType[],
  deals: [
    { ageRange: '60歲以上', item: '60歲以上', previousTotal: 7, prev3Weeks: 2, prev2Weeks: 1, prev1Week: 0, thisWeek: 0, cumulativeTotal: 10, percentage: '42%' },
    { ageRange: '56~60歲', item: '56~60歲', previousTotal: 9, prev3Weeks: 1, prev2Weeks: 1, prev1Week: 3, thisWeek: 0, cumulativeTotal: 14, percentage: '58%' },
    { ageRange: '合計', item: '合計', previousTotal: 16, prev3Weeks: 3, prev2Weeks: 2, prev1Week: 3, thisWeek: 0, cumulativeTotal: 24, percentage: '100%' },
  ] as AgeAnalysisDataType[],
};

// 定義貴賓背景分析 - 產業資料
const industryAnalysisData = {
  visits: [
    { item: '金融保險業', previousTotal: 74, prev3Weeks: 2, prev2Weeks: 3, prev1Week: 5, thisWeek: 1, cumulativeTotal: 85, percentage: '15%' },
    { item: '科技業', previousTotal: 79, prev3Weeks: 1, prev2Weeks: 3, prev1Week: 4, thisWeek: 1, cumulativeTotal: 90, percentage: '16%' },
    { item: '合計', previousTotal: 153, prev3Weeks: 3, prev2Weeks: 6, prev1Week: 9, thisWeek: 2, cumulativeTotal: 161, percentage: '100%' }, // Assuming '100%' applies to the subset shown
  ] as IndustryAnalysisDataType[],
  revisits: [
    { item: '金融保險業', previousTotal: 31, prev3Weeks: 1, prev2Weeks: '-', prev1Week: '-', thisWeek: 1, cumulativeTotal: 33, percentage: '25%' },
    { item: '科技業', previousTotal: 19, prev3Weeks: 2, prev2Weeks: '-', prev1Week: '-', thisWeek: 1, cumulativeTotal: 22, percentage: '17%' },
    { item: '合計', previousTotal: 50, prev3Weeks: 3, prev2Weeks: '-', prev1Week: '-', thisWeek: 2, cumulativeTotal: 52, percentage: '100%' },
  ] as IndustryAnalysisDataType[],
  deals: [
    { item: '金融保險業', previousTotal: 4, prev3Weeks: '-', prev2Weeks: '-', prev1Week: 1, thisWeek: '-', cumulativeTotal: 5, percentage: '21%' },
    { item: '科技業', previousTotal: 2, prev3Weeks: '-', prev2Weeks: '-', prev1Week: 1, thisWeek: '-', cumulativeTotal: 3, percentage: '8%' }, // Percentage seems low, maybe 8% refers to total deals?
    { item: '合計', previousTotal: 6, prev3Weeks: '-', prev2Weeks: '-', prev1Week: 2, thisWeek: '-', cumulativeTotal: 8, percentage: '100%' },
  ] as IndustryAnalysisDataType[],
};

// 定義貴賓背景分析 - 區域資料
const taipeiRegionCallsData: RegionAnalysisBaseType[] = [
  { key: '中山', region: '中山', previousTotal: 108, prev3Weeks: 2, prev2Weeks: 4, prev1Week: 1, thisWeek: 3, cumulativeTotal: 118, percentage: '57%' },
  { key: '松山', region: '松山', previousTotal: 21, prev3Weeks: 1, prev2Weeks: '-', prev1Week: '-', thisWeek: 2, cumulativeTotal: 24, percentage: '12%' }, // Assuming TW=2 based on Accum
  { key: '信義', region: '信義', previousTotal: 2, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 2, percentage: '1%' },
  { key: '大安', region: '大安', previousTotal: 9, prev3Weeks: 1, prev2Weeks: 2, prev1Week: '-', thisWeek: 0, cumulativeTotal: 12, percentage: '6%' }, // TW = 0 based on Accum
  { key: '中正', region: '中正', previousTotal: 6, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 6, percentage: '3%' },
  { key: '萬華', region: '萬華', previousTotal: 3, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 3, percentage: '1%' },
  { key: '內湖', region: '內湖', previousTotal: 8, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 1, cumulativeTotal: 9, percentage: '4%' },
  { key: '文山', region: '文山', previousTotal: 3, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 3, percentage: '1%' },
  { key: '大同', region: '大同', previousTotal: 4, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 4, percentage: '2%' }, // Renamed from duplicate 萬華
  { key: '士林', region: '士林', previousTotal: 12, prev3Weeks: 1, prev2Weeks: 1, prev1Week: '-', thisWeek: 0, cumulativeTotal: 14, percentage: '7%' }, // TW = 0 based on Accum
  { key: '北投', region: '北投', previousTotal: 9, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 9, percentage: '4%' },
  { key: '光復', region: '光復', previousTotal: 2, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 2, percentage: '1%' }, // Assuming 光復 is a distinct area in Taipei context
  { key: '合計', region: '合計', previousTotal: 185, prev3Weeks: 5, prev2Weeks: 6, prev1Week: 5, thisWeek: 5, cumulativeTotal: 206, percentage: '100%' },
];
const taipeiRegionVisitsData: RegionAnalysisBaseType[] = [
  { key: '中山', region: '中山', previousTotal: 247, prev3Weeks: 5, prev2Weeks: 11, prev1Week: 7, thisWeek: 11, cumulativeTotal: 281, percentage: '56%' },
  { key: '松山', region: '松山', previousTotal: 43, prev3Weeks: 2, prev2Weeks: 1, prev1Week: '-', thisWeek: 0, cumulativeTotal: 46, percentage: '9%' }, // TW=0 based on Accum
  { key: '信義', region: '信義', previousTotal: 13, prev3Weeks: '-', prev2Weeks: 2, prev1Week: '-', thisWeek: 0, cumulativeTotal: 15, percentage: '3%' }, // TW=0 based on Accum
  { key: '大安', region: '大安', previousTotal: 39, prev3Weeks: 7, prev2Weeks: 1, prev1Week: 3, thisWeek: 1, cumulativeTotal: 51, percentage: '10%' },
  { key: '中正', region: '中正', previousTotal: 15, prev3Weeks: '-', prev2Weeks: 2, prev1Week: '-', thisWeek: 0, cumulativeTotal: 17, percentage: '3%' }, // TW=0 based on Accum
  { key: '萬華', region: '萬華', previousTotal: 6, prev3Weeks: '-', prev2Weeks: 1, prev1Week: '-', thisWeek: 2, cumulativeTotal: 8, percentage: '2%' }, // TW=1 based on Accum? Image shows TW=2
  { key: '內湖', region: '內湖', previousTotal: 20, prev3Weeks: '-', prev2Weeks: 1, prev1Week: '-', thisWeek: 1, cumulativeTotal: 22, percentage: '4%' }, // TW=1 based on Accum? Image shows TW=1
  { key: '文山', region: '文山', previousTotal: 16, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 16, percentage: '3%' },
  { key: '大同', region: '大同', previousTotal: 5, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 5, percentage: '1%' },
  { key: '士林', region: '士林', previousTotal: 24, prev3Weeks: 2, prev2Weeks: 2, prev1Week: '-', thisWeek: 0, cumulativeTotal: 28, percentage: '6%' }, // TW=0 based on Accum
  { key: '北投', region: '北投', previousTotal: 15, prev3Weeks: 1, prev2Weeks: '-', prev1Week: 1, thisWeek: 0, cumulativeTotal: 17, percentage: '3%' }, // TW=0 based on Accum
  { key: '光復', region: '光復', previousTotal: 5, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 1, cumulativeTotal: 6, percentage: '1%' },
  { key: '合計', region: '合計', previousTotal: 435, prev3Weeks: 11, prev2Weeks: 18, prev1Week: 24, thisWeek: 14, cumulativeTotal: 502, percentage: '100%' }, // Note: Image weekly total is 14, calculated 16. Using 14 from image.
];
const taipeiRegionRevisitsData: RegionAnalysisBaseType[] = [
  { key: '中山', region: '中山', previousTotal: 62, prev3Weeks: 2, prev2Weeks: 2, prev1Week: 1, thisWeek: 2, cumulativeTotal: 69, percentage: '63%' },
  { key: '松山', region: '松山', previousTotal: 6, prev3Weeks: '-', prev2Weeks: 1, prev1Week: 1, thisWeek: 0, cumulativeTotal: 8, percentage: '8%' }, // TW=0 based on Accum
  { key: '信義', region: '信義', previousTotal: 4, prev3Weeks: '-', prev2Weeks: 1, prev1Week: '-', thisWeek: 0, cumulativeTotal: 5, percentage: '5%' }, // TW=0 based on Accum
  { key: '大安', region: '大安', previousTotal: 5, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 5, percentage: '5%' },
  { key: '中正', region: '中正', previousTotal: 5, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 5, percentage: '5%' },
  { key: '萬華', region: '萬華', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '1%' },
  { key: '內湖', region: '內湖', previousTotal: 2, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 2, percentage: '2%' },
  { key: '文山', region: '文山', previousTotal: 2, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 2, percentage: '2%' },
  { key: '大同', region: '大同', previousTotal: 4, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 4, percentage: '4%' },
  { key: '士林', region: '士林', previousTotal: 6, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 6, percentage: '5%' },
  { key: '北投', region: '北投', previousTotal: 4, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 4, percentage: '4%' },
  { key: '光復', region: '光復', previousTotal: 1, prev3Weeks: '-', prev2Weeks: 1, prev1Week: 1, thisWeek: 0, cumulativeTotal: 3, percentage: '2%' }, // TW=0 based on Accum
  { key: '合計', region: '合計', previousTotal: 98, prev3Weeks: 4, prev2Weeks: 4, prev1Week: 3, thisWeek: 2, cumulativeTotal: 110, percentage: '100%' },
];
const taipeiRegionDealsData: RegionAnalysisDealType[] = [
  { key: '中山', region: '中山', previousTotal: 7, prev3Weeks: '-', prev2Weeks: 2, prev1Week: 1, thisWeek: 1, cumulativeTotal: 11, percentage: '55%', visitDealRatio: '25.5 : 1', weeklyDealStatus: '4.1 : 1' }, // visitDealRatio updated from image if possible (281/11 approx 25.5)
  { key: '松山', region: '松山', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '5%', visitDealRatio: '46.0 : 1', weeklyDealStatus: '5.1 : 1' },
  { key: '信義', region: '信義', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '5%', visitDealRatio: '15.0 : 1', weeklyDealStatus: '3.8 : 1' },
  { key: '大安', region: '大安', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '5%', visitDealRatio: '51.0 : 1', weeklyDealStatus: '10.2 : 1' },
  { key: '中正', region: '中正', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '5%', visitDealRatio: '17.0 : 1', weeklyDealStatus: '3.4 : 1' },
  { key: '萬華', region: '萬華', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '5%', visitDealRatio: '8.0 : 1', weeklyDealStatus: '8.0 : 1' },
  { key: '內湖', region: '內湖', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: 1, thisWeek: 1, cumulativeTotal: 3, percentage: '15%', visitDealRatio: '7.7 : 1', weeklyDealStatus: '23.0 : 1' }, // Data from image
  { key: '文山', region: '文山', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '5%', visitDealRatio: '16.0 : 1', weeklyDealStatus: '2.5 : 1' },
  { key: '大同', region: '大同', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '1.3 : 1' },
  { key: '士林', region: '士林', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '4.7 : 1' },
  { key: '北投', region: '北投', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '8.5 : 1' },
  { key: '光復', region: '光復', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '3.0 : 1' },
  { key: '合計', region: '合計', previousTotal: 14, prev3Weeks: 0, prev2Weeks: 2, prev1Week: 1, thisWeek: 3, cumulativeTotal: 20, percentage: '100%', visitDealRatio: '25.1 : 1', weeklyDealStatus: '4.6 : 1' },
];

// 2. 新北市
const newTaipeiRegionCallsData: RegionAnalysisBaseType[] = [
  { key: '汐止', region: '汐止', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '新店', region: '新店', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '5%' },
  { key: '永和', region: '永和', previousTotal: 3, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 3, percentage: '14%' },
  { key: '中和', region: '中和', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '5%' },
  { key: '板橋', region: '板橋', previousTotal: 4, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 1, cumulativeTotal: 5, percentage: '23%' },
  { key: '三重', region: '三重', previousTotal: 7, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 1, cumulativeTotal: 8, percentage: '36%' },
  { key: '蘆洲', region: '蘆洲', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '土城', region: '土城', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '新莊', region: '新莊', previousTotal: 2, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 2, percentage: '9%' },
  { key: '光復區', region: '光復區', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '5%' },
  { key: '光明區', region: '光明區', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '合計', region: '合計', previousTotal: 20, prev3Weeks: 1, prev2Weeks: 0, prev1Week: 0, thisWeek: 1, cumulativeTotal: 22, percentage: '100%' }, // Calculated TW = 2, Image TW = 1. Using image.
];
const newTaipeiRegionVisitsData: RegionAnalysisBaseType[] = [
  { key: '汐止', region: '汐止', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '新店', region: '新店', previousTotal: 5, prev3Weeks: '-', prev2Weeks: 1, prev1Week: '-', thisWeek: 0, cumulativeTotal: 6, percentage: '12%' }, // TW=0 based on Accum
  { key: '永和', region: '永和', previousTotal: 9, prev3Weeks: '-', prev2Weeks: 1, prev1Week: '-', thisWeek: 0, cumulativeTotal: 10, percentage: '20%' }, // TW=0 based on Accum
  { key: '中和', region: '中和', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '2%' },
  { key: '板橋', region: '板橋', previousTotal: 11, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 11, percentage: '22%' },
  { key: '三重', region: '三重', previousTotal: 15, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 15, percentage: '29%' },
  { key: '蘆洲', region: '蘆洲', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '土城', region: '土城', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '新莊', region: '新莊', previousTotal: 2, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 2, percentage: '4%' },
  { key: '光復區', region: '光復區', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '2%' },
  { key: '光明區', region: '光明區', previousTotal: 2, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 2, percentage: '4%' },
  { key: '合計', region: '合計', previousTotal: 46, prev3Weeks: 1, prev2Weeks: 1, prev1Week: 0, thisWeek: 1, cumulativeTotal: 51, percentage: '100%' }, // Image TW=1, matches calculation.
];
const newTaipeiRegionRevisitsData: RegionAnalysisBaseType[] = [
  { key: '汐止', region: '汐止', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '新店', region: '新店', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: 1, thisWeek: 0, cumulativeTotal: 2, percentage: '10%' }, // TW=0 based on Accum
  { key: '永和', region: '永和', previousTotal: 4, prev3Weeks: '-', prev2Weeks: 1, prev1Week: '-', thisWeek: 1, cumulativeTotal: 6, percentage: '29%' }, // TW=1 based on Accum
  { key: '中和', region: '中和', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '板橋', region: '板橋', previousTotal: 7, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 7, percentage: '33%' },
  { key: '三重', region: '三重', previousTotal: 5, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 5, percentage: '24%' },
  { key: '蘆洲', region: '蘆洲', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '土城', region: '土城', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '新莊', region: '新莊', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '5%' },
  { key: '光復區', region: '光復區', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '光明區', region: '光明區', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { key: '合計', region: '合計', previousTotal: 18, prev3Weeks: 2, prev2Weeks: 1, prev1Week: 0, thisWeek: 1, cumulativeTotal: 21, percentage: '100%' }, // Image TW=1, matches calculation.
];
const newTaipeiRegionDealsData: RegionAnalysisDealType[] = [
  { key: '汐止', region: '汐止', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '#DIV/0!' },
  // Recalculating New Taipei Deals based on Total=2
  { key: '新店', region: '新店', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '50%', visitDealRatio: '6.0 : 1', weeklyDealStatus: '3.0 : 1' },
  { key: '永和', region: '永和', previousTotal: 1, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 1, percentage: '50%', visitDealRatio: '10.0 : 1', weeklyDealStatus: '1.7 : 1' },
  { key: '中和', region: '中和', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '#DIV/0!' },
  { key: '板橋', region: '板橋', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '1.6 : 1' },
  { key: '三重', region: '三重', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '3.0 : 1' },
  { key: '蘆洲', region: '蘆洲', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '#DIV/0!' },
  { key: '土城', region: '土城', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '#DIV/0!' },
  { key: '新莊', region: '新莊', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '3.0 : 1' },
  { key: '光復區', region: '光復區', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '#DIV/0!' },
  { key: '光明區', region: '光明區', previousTotal: 0, prev3Weeks: '-', prev2Weeks: '-', prev1Week: '-', thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', weeklyDealStatus: '#DIV/0!' },
  { key: '合計', region: '合計', previousTotal: 2, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 2, percentage: '100%', visitDealRatio: '51.0 : 1', weeklyDealStatus: '2.4 : 1' }, // Using image total = 2
];


// 3. 其他 (外縣市匯總)
const otherRegionCallsData: RegionAnalysisBaseType[] = [
  { key: '其他', region: '其他', previousTotal: 8, prev3Weeks: 3, prev2Weeks: 0, prev1Week: 2, thisWeek: 0, cumulativeTotal: 13, percentage: '100%' },
];
const otherRegionVisitsData: RegionAnalysisBaseType[] = [
  { key: '其他', region: '其他', previousTotal: 16, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 2, thisWeek: 1, cumulativeTotal: 19, percentage: '100%' },
];
const otherRegionRevisitsData: RegionAnalysisBaseType[] = [
  { key: '其他', region: '其他', previousTotal: 0, prev3Weeks: 1, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 1, percentage: '100%' },
];
const otherRegionDealsData: RegionAnalysisDealType[] = [
  { key: '其他', region: '其他', previousTotal: 2, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 1, cumulativeTotal: 3, percentage: '100%', visitDealRatio: '6.3 : 1', weeklyDealStatus: '19.0 : 1' }, // Image TW=1, matches calculation.
];


// 定義媒體分析資料
const mediaAnalysisCallsData: MediaAnalysisDataType[] = [
  { mediaItem: '社群平台', item: '社群平台', previousTotal: 61, prev3Weeks: 1, prev2Weeks: 2, prev1Week: 2, thisWeek: 2, cumulativeTotal: 68, percentage: '23%' },
  { mediaItem: '網路平台', item: '網路平台', previousTotal: 68, prev3Weeks: 4, prev2Weeks: 0, prev1Week: 2, thisWeek: 2, cumulativeTotal: 76, percentage: '26%' }, // Assuming '-' is 0
  { mediaItem: '戶外看板', item: '戶外看板', previousTotal: 33, prev3Weeks: 1, prev2Weeks: 3, prev1Week: 1, thisWeek: 3, cumulativeTotal: 41, percentage: '14%' },
  { mediaItem: '手舉牌', item: '手舉牌', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },      // Assuming '-' is 0
  { mediaItem: '接待中心', item: '接待中心', previousTotal: 34, prev3Weeks: 0, prev2Weeks: 2, prev1Week: 3, thisWeek: 2, cumulativeTotal: 41, percentage: '14%' }, // Assuming '-' is 0
  { mediaItem: '基地圍籬', item: '基地圍籬', previousTotal: 61, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 61, percentage: '21%' }, // Assuming '-' is 0
  { mediaItem: '郵寄信箱/邀請函', item: '郵寄信箱/邀請函', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' }, // Assuming '-' is 0
  { mediaItem: '展覽活動', item: '展覽活動', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },   // Assuming '-' is 0
  { mediaItem: '介紹', item: '介紹', previousTotal: 7, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 1, cumulativeTotal: 8, percentage: '3%' },         // Assuming '-' is 0
  { mediaItem: '業務開發', item: '業務開發', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },   // Assuming '-' is 0
  { mediaItem: '紙媒', item: '紙媒', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },         // Assuming '-' is 0
  { mediaItem: 'RD', item: 'RD', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },            // Assuming '-' is 0
];


// --- C. 貴賓偏好類分析 資料定義 ---

// C.1 需求坪數
const areaPreferenceCallsData: AreaPreferenceDataType[] = [
  { areaRange: '60坪以上', item: '60坪以上', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { areaRange: '56坪~60坪', item: '56坪~60坪', previousTotal: 3, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 3, percentage: '1%' },
  { areaRange: '51坪~55坪', item: '51坪~55坪', previousTotal: 5, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 5, percentage: '2%' },
  { areaRange: '46坪~50坪', item: '46坪~50坪', previousTotal: 2, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 2, percentage: '1%' },
  { areaRange: '41坪~45坪', item: '41坪~45坪', previousTotal: 2, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 3, percentage: '1%' },
  { areaRange: '36坪~40坪', item: '36坪~40坪', previousTotal: 22, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 23, percentage: '9%' },
  { areaRange: '31坪~35坪', item: '31坪~35坪', previousTotal: 43, prev3Weeks: 1, prev2Weeks: 4, prev1Week: 1, thisWeek: 3, cumulativeTotal: 52, percentage: '21%' },
  { areaRange: '26坪~30坪', item: '26坪~30坪', previousTotal: 44, prev3Weeks: 1, prev2Weeks: 0, prev1Week: 2, thisWeek: 0, cumulativeTotal: 47, percentage: '19%' },
  { areaRange: '21坪~25坪', item: '21坪~25坪', previousTotal: 99, prev3Weeks: 1, prev2Weeks: 2, prev1Week: 4, thisWeek: 4, cumulativeTotal: 110, percentage: '45%' },
  { areaRange: '20坪以下', item: '20坪以下', previousTotal: 1, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 1, percentage: '0%' },
];
const areaPreferenceVisitsData: AreaPreferenceDataType[] = [
  { areaRange: '60坪以上', item: '60坪以上', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 1, cumulativeTotal: 1, percentage: '0%' },
  { areaRange: '56坪~60坪', item: '56坪~60坪', previousTotal: 10, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 10, percentage: '2%' }, // Accum should be 11? Using 10 from image
  { areaRange: '51坪~55坪', item: '51坪~55坪', previousTotal: 13, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 13, percentage: '3%' },
  { areaRange: '46坪~50坪', item: '46坪~50坪', previousTotal: 10, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 10, percentage: '2%' },
  { areaRange: '41坪~45坪', item: '41坪~45坪', previousTotal: 9, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 1, cumulativeTotal: 9, percentage: '2%' }, // Accum should be 11? Using 9 from image
  { areaRange: '36坪~40坪', item: '36坪~40坪', previousTotal: 44, prev3Weeks: 0, prev2Weeks: 1, prev1Week: 3, thisWeek: 0, cumulativeTotal: 45, percentage: '9%' }, // Accum should be 48? Using 45 from image
  { areaRange: '31坪~35坪', item: '31坪~35坪', previousTotal: 120, prev3Weeks: 3, prev2Weeks: 6, prev1Week: 6, thisWeek: 4, cumulativeTotal: 125, percentage: '24%' },// Accum should be 139? Using 125 from image
  { areaRange: '26坪~30坪', item: '26坪~30坪', previousTotal: 115, prev3Weeks: 2, prev2Weeks: 4, prev1Week: 5, thisWeek: 3, cumulativeTotal: 117, percentage: '23%' },// Accum should be 129? Using 117 from image
  { areaRange: '21坪~25坪', item: '21坪~25坪', previousTotal: 175, prev3Weeks: 6, prev2Weeks: 8, prev1Week: 14, thisWeek: 7, cumulativeTotal: 181, percentage: '35%' },// Accum should be 210? Using 181 from image
  { areaRange: '20坪以下', item: '20坪以下', previousTotal: 3, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 1, cumulativeTotal: 3, percentage: '1%' },  // Accum should be 4? Using 3 from image
];
const areaPreferenceRevisitsData: AreaPreferenceDataType[] = [
  { areaRange: '60坪以上', item: '60坪以上', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { areaRange: '56坪~60坪', item: '56坪~60坪', previousTotal: 4, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 4, percentage: '3%' },
  { areaRange: '51坪~55坪', item: '51坪~55坪', previousTotal: 2, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 2, percentage: '2%' },
  { areaRange: '46坪~50坪', item: '46坪~50坪', previousTotal: 3, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 3, percentage: '2%' },
  { areaRange: '41坪~45坪', item: '41坪~45坪', previousTotal: 1, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 1, percentage: '1%' },
  { areaRange: '36坪~40坪', item: '36坪~40坪', previousTotal: 16, prev3Weeks: 0, prev2Weeks: 1, prev1Week: 1, thisWeek: 1, cumulativeTotal: 18, percentage: '14%' }, // Accum should be 19? Using 18 from image
  { areaRange: '31坪~35坪', item: '31坪~35坪', previousTotal: 32, prev3Weeks: 2, prev2Weeks: 2, prev1Week: 1, thisWeek: 0, cumulativeTotal: 37, percentage: '28%' },// Accum should be 37? Matches image
  { areaRange: '26坪~30坪', item: '26坪~30坪', previousTotal: 36, prev3Weeks: 1, prev2Weeks: 1, prev1Week: 1, thisWeek: 1, cumulativeTotal: 39, percentage: '30%' },// Accum should be 40? Using 39 from image
  { areaRange: '21坪~25坪', item: '21坪~25坪', previousTotal: 23, prev3Weeks: 2, prev2Weeks: 1, prev1Week: 1, thisWeek: 1, cumulativeTotal: 28, percentage: '21%' },// Accum should be 28? Matches image
  { areaRange: '20坪以下', item: '20坪以下', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
];
const areaPreferenceDealsData: AreaPreferenceDealDataType[] = [
  { areaRange: '60坪以上', item: '60坪以上', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '3.5 : 1' },
  { areaRange: '56坪~60坪', item: '56坪~60坪', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '2.5 : 1' },
  { areaRange: '51坪~55坪', item: '51坪~55坪', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '6.5 : 1' },
  { areaRange: '46坪~50坪', item: '46坪~50坪', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '3.3 : 1' },
  { areaRange: '41坪~45坪', item: '41坪~45坪', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '9.0 : 1' },
  { areaRange: '36坪~40坪', item: '36坪~40坪', previousTotal: 2, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 3, percentage: '13%', visitDealRatio: '15.0 : 1', revisitDealRatio: '2.5 : 1' }, // Accum should be 3? Matches image
  { areaRange: '31坪~35坪', item: '31坪~35坪', previousTotal: 5, prev3Weeks: 0, prev2Weeks: 1, prev1Week: 1, thisWeek: 1, cumulativeTotal: 8, percentage: '33%', visitDealRatio: '15.4 : 1', revisitDealRatio: '3.5 : 1' }, // Accum should be 8? Matches image
  { areaRange: '26坪~30坪', item: '26坪~30坪', previousTotal: 3, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 3, percentage: '17%', visitDealRatio: '29.3 : 1', revisitDealRatio: '3.4 : 1' }, // Accum should be 4? Matches image
  { areaRange: '21坪~25坪', item: '21坪~25坪', previousTotal: 5, prev3Weeks: 0, prev2Weeks: 1, prev1Week: 1, thisWeek: 2, cumulativeTotal: 9, percentage: '38%', visitDealRatio: '20.1 : 1', revisitDealRatio: '6.5 : 1' }, // Accum should be 9? Matches image
  { areaRange: '20坪以下', item: '20坪以下', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '#DIV/0!' },
];

// C.2 購屋用途(複選)
const purchasePurposeVisitsData: PurchasePurposeDataType[] = [
  { purpose: '小換大', item: '小換大', previousTotal: 58, prev3Weeks: 1, prev2Weeks: 3, prev1Week: 2, thisWeek: 1, cumulativeTotal: 65, percentage: '10%' },
  { purpose: '大換小', item: '大換小', previousTotal: 15, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 15, percentage: '2%' },
  { purpose: '舊換新', item: '舊換新', previousTotal: 95, prev3Weeks: 3, prev2Weeks: 3, prev1Week: 7, thisWeek: 0, cumulativeTotal: 108, percentage: '17%' },
  { purpose: '換環境', item: '換環境', previousTotal: 32, prev3Weeks: 2, prev2Weeks: 0, prev1Week: 5, thisWeek: 0, cumulativeTotal: 39, percentage: '6%' },
  { purpose: '首購', item: '首購', previousTotal: 109, prev3Weeks: 2, prev2Weeks: 7, prev1Week: 6, thisWeek: 2, cumulativeTotal: 126, percentage: '20%' },
  { purpose: '投資', item: '投資', previousTotal: 24, prev3Weeks: 1, prev2Weeks: 0, prev1Week: 2, thisWeek: 1, cumulativeTotal: 28, percentage: '4%' },
  { purpose: '置產', item: '置產', previousTotal: 181, prev3Weeks: 6, prev2Weeks: 5, prev1Week: 15, thisWeek: 5, cumulativeTotal: 212, percentage: '34%' },
  { purpose: '隨興', item: '隨興', previousTotal: 27, prev3Weeks: 1, prev2Weeks: 1, prev1Week: 1, thisWeek: 0, cumulativeTotal: 30, percentage: '5%' },
];
const purchasePurposeRevisitsData: PurchasePurposeDataType[] = [
  { purpose: '小換大', item: '小換大', previousTotal: 19, prev3Weeks: 1, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 21, percentage: '14%' },
  { purpose: '大換小', item: '大換小', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { purpose: '舊換新', item: '舊換新', previousTotal: 24, prev3Weeks: 0, prev2Weeks: 2, prev1Week: 1, thisWeek: 0, cumulativeTotal: 27, percentage: '18%' },
  { purpose: '換環境', item: '換環境', previousTotal: 10, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 11, percentage: '7%' }, // Add missing prev2Weeks: 0
  { purpose: '首購', item: '首購', previousTotal: 24, prev3Weeks: 2, prev2Weeks: 1, prev1Week: 1, thisWeek: 0, cumulativeTotal: 28, percentage: '19%' },
  { purpose: '投資', item: '投資', previousTotal: 7, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 7, percentage: '5%' },
  { purpose: '置產', item: '置產', previousTotal: 45, prev3Weeks: 4, prev2Weeks: 1, prev1Week: 1, thisWeek: 2, cumulativeTotal: 53, percentage: '36%' },
  { purpose: '隨興', item: '隨興', previousTotal: 2, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 2, percentage: '1%' },
];
const purchasePurposeDealsData: PurchasePurposeDealDataType[] = [
  { purpose: '小換大', item: '小換大', previousTotal: 1, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 1, percentage: '4%', visitDealRatio: '65.0 : 1', revisitDealRatio: '3.1 : 1' },
  { purpose: '大換小', item: '大換小', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '#DIV/0!' },
  { purpose: '舊換新', item: '舊換新', previousTotal: 2, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 1, cumulativeTotal: 4, percentage: '17%', visitDealRatio: '27.0 : 1', revisitDealRatio: '4.0 : 1' },
  { purpose: '換環境', item: '換環境', previousTotal: 1, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 1, percentage: '4%', visitDealRatio: '39.0 : 1', revisitDealRatio: '3.5 : 1' }, // Removed duplicate item property
  { purpose: '首購', item: '首購', previousTotal: 6, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 7, percentage: '29%', visitDealRatio: '18.0 : 1', revisitDealRatio: '4.5 : 1' },
  { purpose: '投資', item: '投資', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '4.0 : 1' }, // Revisit Ratio seems off if deals=0
  { purpose: '置產', item: '置產', previousTotal: 4, prev3Weeks: 0, prev2Weeks: 2, prev1Week: 0, thisWeek: 2, cumulativeTotal: 8, percentage: '33%', visitDealRatio: '26.5 : 1', revisitDealRatio: '4.0 : 1' },
  { purpose: '隨興', item: '隨興', previousTotal: 2, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 3, percentage: '13%', visitDealRatio: '10.0 : 1', revisitDealRatio: '15.0 : 1' },
];

// C.3 需求格局
const layoutPreferenceVisitsData: LayoutPreferenceDataType[] = [
  { layout: '4房', item: '4房', previousTotal: 10, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 11, percentage: '2%' },
  { layout: '3+1房', item: '3+1房', previousTotal: 6, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 7, percentage: '1%' },
  { layout: '3房', item: '3房', previousTotal: 166, prev3Weeks: 2, prev2Weeks: 6, prev1Week: 7, thisWeek: 6, cumulativeTotal: 187, percentage: '32%' },
  { layout: '2+1房', item: '2+1房', previousTotal: 61, prev3Weeks: 3, prev2Weeks: 2, prev1Week: 5, thisWeek: 1, cumulativeTotal: 72, percentage: '13%' },
  { layout: '2房', item: '2房', previousTotal: 251, prev3Weeks: 7, prev2Weeks: 10, prev1Week: 16, thisWeek: 9, cumulativeTotal: 293, percentage: '51%' },
  { layout: '1+1房', item: '1+1房', previousTotal: 4, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 1, cumulativeTotal: 5, percentage: '1%' },
  { layout: '1房', item: '1房', previousTotal: 1, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 1, percentage: '0%' },
];
const layoutPreferenceRevisitsData: LayoutPreferenceDataType[] = [
  { layout: '4房', item: '4房', previousTotal: 6, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 6, percentage: '4%' },
  { layout: '3+1房', item: '3+1房', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 1, percentage: '1%' },
  { layout: '3房', item: '3房', previousTotal: 46, prev3Weeks: 0, prev2Weeks: 4, prev1Week: 2, thisWeek: 1, cumulativeTotal: 53, percentage: '37%' },
  { layout: '2+1房', item: '2+1房', previousTotal: 7, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 8, percentage: '6%' },
  { layout: '2房', item: '2房', previousTotal: 53, prev3Weeks: 3, prev2Weeks: 1, prev1Week: 1, thisWeek: 2, cumulativeTotal: 60, percentage: '42%' },
  { layout: '1+1房', item: '1+1房', previousTotal: 4, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 4, percentage: '3%' },
  { layout: '1房', item: '1房', previousTotal: 11, prev3Weeks: 1, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 12, percentage: '8%' },
];
const layoutPreferenceDealsData: LayoutPreferenceDealDataType[] = [
  { layout: '4房', item: '4房', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '1.8 : 1' },
  { layout: '3+1房', item: '3+1房', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '7.0 : 1' },
  { layout: '3房', item: '3房', previousTotal: 8, prev3Weeks: 0, prev2Weeks: 1, prev1Week: 1, thisWeek: 1, cumulativeTotal: 11, percentage: '46%', visitDealRatio: '17.0 : 1', revisitDealRatio: '3.5 : 1' },
  { layout: '2+1房', item: '2+1房', previousTotal: 3, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 3, percentage: '13%', visitDealRatio: '24.0 : 1', revisitDealRatio: '9.0 : 1' },
  { layout: '2房', item: '2房', previousTotal: 5, prev3Weeks: 0, prev2Weeks: 2, prev1Week: 1, thisWeek: 2, cumulativeTotal: 10, percentage: '42%', visitDealRatio: '29.3 : 1', revisitDealRatio: '4.9 : 1' },
  { layout: '1+1房', item: '1+1房', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '1.3 : 1' },
  { layout: '1房', item: '1房', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '.1 : 1' }, // revisitDealRatio seems odd
];

// C.4 樓層喜好
const floorPreferenceVisitsData: FloorPreferenceDataType[] = [
  { floorRange: '11~15樓中樓層', item: '11~15樓中樓層', previousTotal: 81, prev3Weeks: 4, prev2Weeks: 0, prev1Week: 11, thisWeek: 2, cumulativeTotal: 98, percentage: '17%' },
  { floorRange: '6~10樓中樓層', item: '6~10樓中樓層', previousTotal: 251, prev3Weeks: 4, prev2Weeks: 8, prev1Week: 7, thisWeek: 3, cumulativeTotal: 273, percentage: '47%' },
  { floorRange: '2~5樓低樓層', item: '2~5樓低樓層', previousTotal: 167, prev3Weeks: 8, prev2Weeks: 7, prev1Week: 12, thisWeek: 11, cumulativeTotal: 205, percentage: '36%' },
  { floorRange: '1樓店面', item: '1樓店面', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
];
const floorPreferenceRevisitsData: FloorPreferenceDataType[] = [
  { floorRange: '11~15樓中樓層', item: '11~15樓中樓層', previousTotal: 17, prev3Weeks: 0, prev2Weeks: 1, prev1Week: 1, thisWeek: 0, cumulativeTotal: 19, percentage: '14%' },
  { floorRange: '6~10樓中樓層', item: '6~10樓中樓層', previousTotal: 68, prev3Weeks: 2, prev2Weeks: 2, prev1Week: 0, thisWeek: 0, cumulativeTotal: 72, percentage: '55%' },
  { floorRange: '2~5樓低樓層', item: '2~5樓低樓層', previousTotal: 31, prev3Weeks: 4, prev2Weeks: 2, prev1Week: 2, thisWeek: 2, cumulativeTotal: 41, percentage: '31%' },
  { floorRange: '1樓店面', item: '1樓店面', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
];
const floorPreferenceDealsData: FloorPreferenceDealDataType[] = [
  { floorRange: '11~15樓中樓層', item: '11~15樓中樓層', previousTotal: 3, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 4, percentage: '17%', visitDealRatio: '24.5 : 1', revisitDealRatio: '5.2 : 1' },
  { floorRange: '6~10樓中樓層', item: '6~10樓中樓層', previousTotal: 5, prev3Weeks: 0, prev2Weeks: 3, prev1Week: 1, thisWeek: 1, cumulativeTotal: 10, percentage: '42%', visitDealRatio: '27.3 : 1', revisitDealRatio: '3.8 : 1' },
  { floorRange: '2~5樓低樓層', item: '2~5樓低樓層', previousTotal: 8, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 2, cumulativeTotal: 10, percentage: '42%', visitDealRatio: '20.5 : 1', revisitDealRatio: '5.0 : 1' },
  { floorRange: '1樓店面', item: '1樓店面', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '#DIV/0!' },
];

// C.5 購屋預算
const budgetPreferenceVisitsData: BudgetPreferenceDataType[] = [
  { budgetRange: '8001萬以上', item: '8001萬以上', previousTotal: 7, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 7, percentage: '1%' },
  { budgetRange: '7001~8000', item: '7001~8000', previousTotal: 5, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 6, percentage: '1%' },
  { budgetRange: '6001~7000', item: '6001~7000', previousTotal: 11, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 12, percentage: '2%' },
  { budgetRange: '5501~6000', item: '5501~6000', previousTotal: 30, prev3Weeks: 0, prev2Weeks: 1, prev1Week: 2, thisWeek: 0, cumulativeTotal: 33, percentage: '6%' },
  { budgetRange: '5001~5500', item: '5001~5500', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { budgetRange: '4501~5000', item: '4501~5000', previousTotal: 107, prev3Weeks: 4, prev2Weeks: 5, prev1Week: 2, thisWeek: 0, cumulativeTotal: 118, percentage: '21%' },
  { budgetRange: '4001~4500', item: '4001~4500', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { budgetRange: '3501~4000', item: '3501~4000', previousTotal: 114, prev3Weeks: 6, prev2Weeks: 4, prev1Week: 8, thisWeek: 4, cumulativeTotal: 136, percentage: '24%' },
  { budgetRange: '3001~3500', item: '3001~3500', previousTotal: 148, prev3Weeks: 6, prev2Weeks: 8, prev1Week: 7, thisWeek: 6, cumulativeTotal: 175, percentage: '30%' },
  { budgetRange: '2501~3000', item: '2501~3000', previousTotal: 77, prev3Weeks: 3, prev2Weeks: 7, prev1Week: 1, thisWeek: 0, cumulativeTotal: 88, percentage: '15%' },
  { budgetRange: '2500以下', item: '2500以下', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
];
const budgetPreferenceRevisitsData: BudgetPreferenceDataType[] = [
  { budgetRange: '8001萬以上', item: '8001萬以上', previousTotal: 2, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 2, percentage: '2%' },
  { budgetRange: '7001~8000', item: '7001~8000', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { budgetRange: '6001~7000', item: '6001~7000', previousTotal: 2, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 2, percentage: '2%' },
  { budgetRange: '5501~6000', item: '5501~6000', previousTotal: 7, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 8, percentage: '6%' },
  { budgetRange: '5001~5500', item: '5001~5500', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { budgetRange: '4501~5000', item: '4501~5000', previousTotal: 30, prev3Weeks: 1, prev2Weeks: 2, prev1Week: 1, thisWeek: 1, cumulativeTotal: 35, percentage: '27%' },
  { budgetRange: '4001~4500', item: '4001~4500', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
  { budgetRange: '3501~4000', item: '3501~4000', previousTotal: 33, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 4, thisWeek: 0, cumulativeTotal: 37, percentage: '28%' },
  { budgetRange: '3001~3500', item: '3001~3500', previousTotal: 33, prev3Weeks: 2, prev2Weeks: 1, prev1Week: 0, thisWeek: 2, cumulativeTotal: 38, percentage: '29%' },
  { budgetRange: '2501~3000', item: '2501~3000', previousTotal: 9, prev3Weeks: 0, prev2Weeks: 1, prev1Week: 0, thisWeek: 0, cumulativeTotal: 10, percentage: '8%' },
  { budgetRange: '2500以下', item: '2500以下', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%' },
];
const budgetPreferenceDealsData: BudgetPreferenceDealDataType[] = [
  { budgetRange: '8001萬以上', item: '8001萬以上', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '3.5 : 1' },
  { budgetRange: '7001~8000', item: '7001~8000', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '#DIV/0!' },
  { budgetRange: '6001~7000', item: '6001~7000', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '6.0 : 1' },
  { budgetRange: '5501~6000', item: '5501~6000', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '4.1 : 1' },
  { budgetRange: '5001~5500', item: '5001~5500', previousTotal: 4, prev3Weeks: 0, prev2Weeks: 1, prev1Week: 1, thisWeek: 1, cumulativeTotal: 7, percentage: '29%', visitDealRatio: '.0 : 1', revisitDealRatio: '#DIV/0!' },
  { budgetRange: '4501~5000', item: '4501~5000', previousTotal: 3, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 1, cumulativeTotal: 4, percentage: '17%', visitDealRatio: '29.5 : 1', revisitDealRatio: '3.4 : 1' },
  { budgetRange: '4001~4500', item: '4001~4500', previousTotal: 4, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 1, thisWeek: 0, cumulativeTotal: 5, percentage: '21%', visitDealRatio: '.0 : 1', revisitDealRatio: '#DIV/0!' },
  { budgetRange: '3501~4000', item: '3501~4000', previousTotal: 1, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 1, cumulativeTotal: 2, percentage: '8%', visitDealRatio: '68.0 : 1', revisitDealRatio: '3.7 : 1' },
  { budgetRange: '3001~3500', item: '3001~3500', previousTotal: 4, prev3Weeks: 0, prev2Weeks: 2, prev1Week: 0, thisWeek: 0, cumulativeTotal: 6, percentage: '25%', visitDealRatio: '29.2 : 1', revisitDealRatio: '4.6 : 1' },
  { budgetRange: '2501~3000', item: '2501~3000', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '8.8 : 1' },
  { budgetRange: '2500以下', item: '2500以下', previousTotal: 0, prev3Weeks: 0, prev2Weeks: 0, prev1Week: 0, thisWeek: 0, cumulativeTotal: 0, percentage: '0%', visitDealRatio: '#DIV/0!', revisitDealRatio: '#DIV/0!' },
];

// --- END C. 貴賓偏好類分析 資料定義 ---


// --- START D. 去化分析 資料定義 ---

// D. 去化分析
type UnitStatus = '售' | '定' | '未' | '樣' | null; // 樣 = 樣品屋?

interface BuildingFloorStatus {
  floor: string; // e.g., '15F', '可售', '已售', '去化百分比'
  unitA: UnitStatus | string | number | null;
  unitB: UnitStatus | string | number | null;
  unitC: UnitStatus | string | number | null;
  unitD: UnitStatus | string | number | null;
  unitE: UnitStatus | string | number | null;
  unitF: UnitStatus | string | number | null;
  floorSummary?: string; // e.g., '15F'
  available?: number | string | null; // 可售
  sold?: number | string | null; // 已售
  percentage?: string | null; // 去化百分比
}

interface ParkingSpaceStatus {
  number: string | number;
  status: string | null; // e.g., '售: E-6F', '地主保留', '定: A-5F', or null if available
  type?: 'large' | 'small' | 'accessible'; // For summary calculation if needed
}

interface ParkingLevelStatus {
  level: string; // e.g., 'P1F'
  spaces: (ParkingSpaceStatus | null)[]; // Array matching the grid, null for empty cells
  available: number;
  sold: number;
}

interface ParkingSummaryStatus {
  level: string; // e.g., 'P1', 'P2'
  type: '大' | '小' | '難停';
  available: number | string;
  sold: number | string;
  percentage?: string; // 去化百分比
}


// 定義摘要資料
// ... existing summaryData ...
// ... existing summaryColumns ...
// ... existing mainChartOption ...
// ... existing visitorPromotionList ...
// ... existing dealPromotionList ...
// ... existing mediaPromotionData ...
// ... existing mediaPromotionColumns ...
// ... existing marketNewsData ...
// ... existing stockChartOption ...
// ... existing weeklyOperationStatsData ...
// ... existing operationChartOption ...
// ... existing genderAnalysisData ...
// ... existing ageAnalysisData ...
// ... existing industryAnalysisData ...
// ... existing region data (taipei, newTaipei, other) ...
// ... existing mediaAnalysisCallsData ...
// ... existing C category data (areaPreference, purchasePurpose, etc.) ...

const buildingSalesStatusData: BuildingFloorStatus[] = [
  { floor: '15F', unitA: null, unitB: null, unitC: null, unitD: null, unitE: null, unitF: null, floorSummary: '15F', available: 5, sold: '-', percentage: '-' },
  { floor: '14F', unitA: null, unitB: null, unitC: null, unitD: null, unitE: null, unitF: '定', floorSummary: '14F', available: 5, sold: 1, percentage: '20%' },
  { floor: '13F', unitA: null, unitB: null, unitC: null, unitD: null, unitE: null, unitF: null, floorSummary: '13F', available: 4, sold: '-', percentage: '-' },
  { floor: '12F', unitA: '售', unitB: null, unitC: null, unitD: '定', unitE: null, unitF: null, floorSummary: '12F', available: 4, sold: 2, percentage: '50%' },
  { floor: '11F', unitA: null, unitB: null, unitC: null, unitD: '售', unitE: null, unitF: null, floorSummary: '11F', available: 4, sold: 1, percentage: '25%' },
  { floor: '10F', unitA: null, unitB: '定', unitC: null, unitD: null, unitE: null, unitF: null, floorSummary: '10F', available: 4, sold: 1, percentage: '25%' },
  { floor: '9F', unitA: null, unitB: null, unitC: null, unitD: null, unitE: '定', unitF: null, floorSummary: '9F', available: 5, sold: 1, percentage: '20%' },
  { floor: '8F', unitA: null, unitB: null, unitC: null, unitD: '售', unitE: null, unitF: null, floorSummary: '8F', available: 5, sold: 1, percentage: '20%' },
  { floor: '7F', unitA: '售', unitB: '定', unitC: null, unitD: '定', unitE: null, unitF: null, floorSummary: '7F', available: 6, sold: 3, percentage: '66%' }, // Sold count seems 3, % is 66?
  { floor: '6F', unitA: null, unitB: '售', unitC: null, unitD: '售', unitE: '售', unitF: '售', floorSummary: '6F', available: 5, sold: 4, percentage: '80%' },
  { floor: '5F', unitA: null, unitB: '售', unitC: null, unitD: null, unitE: null, unitF: '定', floorSummary: '5F', available: 3, sold: 2, percentage: '66%' }, // Sold count 2, % 66?
  { floor: '4F', unitA: null, unitB: '售', unitC: '售', unitD: '定', unitE: null, unitF: null, floorSummary: '4F', available: 5, sold: 3, percentage: '60%' },
  { floor: '3F', unitA: '售', unitB: '售', unitC: '定', unitD: '定', unitE: null, unitF: null, floorSummary: '3F', available: 6, sold: 4, percentage: '66%' },
  { floor: '2F', unitA: null, unitB: null, unitC: null, unitD: null, unitE: null, unitF: '售', floorSummary: '2F', available: 4, sold: 1, percentage: '25%' },
  { floor: '店面', unitA: null, unitB: null, unitC: null, unitD: null, unitE: null, unitF: null, floorSummary: '1F', available: 4, sold: '-', percentage: '-' },
  { floor: '可售', unitA: 13, unitB: 8, unitC: 6, unitD: 15, unitE: 13, unitF: 14, floorSummary: null, available: 69, sold: null, percentage: null },
  { floor: '已售', unitA: 6, unitB: 3, unitC: 2, unitD: 7, unitE: 2, unitF: 4, floorSummary: null, available: null, sold: 24, percentage: null },
  { floor: '去化百分比', unitA: '46%', unitB: '37%', unitC: '33%', unitD: '46%', unitE: '15%', unitF: '28%', floorSummary: null, available: null, sold: null, percentage: null },
];

// Parking data needs careful mapping from the grid
const parkingSalesStatusData: ParkingLevelStatus[] = [
  {
    level: 'P1F',
    spaces: [
      { number: 50, status: null }, { number: 51, status: null }, { number: 52, status: null }, { number: 53, status: null }, { number: 54, status: null }, { number: 55, status: null }, { number: 56, status: null },
      { number: 57, status: null }, { number: 58, status: null }, { number: 59, status: null }, { number: 60, status: null }, { number: 61, status: null }, { number: 62, status: null }
    ],
    available: 11, sold: 0
  },
  {
    level: 'P2F',
    spaces: [
      { number: 37, status: null }, { number: 38, status: null }, { number: 39, status: '售: E-6F' }, { number: 40, status: '售: A-12F' }, { number: 41, status: null }, { number: 42, status: null }, { number: 43, status: '地主保留' },
      { number: 44, status: null }, { number: 45, status: null }, { number: 46, status: null }, { number: 47, status: null }, { number: 48, status: null }, { number: 49, status: null }
    ],
    available: 12, sold: 2
  },
  {
    level: 'P3F',
    spaces: [
      { number: 19, status: null }, { number: 20, status: null }, { number: 21, status: '售: A-5F' }, { number: 22, status: '售: A-10F' }, { number: 23, status: null }, { number: 24, status: null }, { number: 25, status: '定: B-7F' },
      { number: 26, status: '售: F-5F' }, { number: 27, status: '售: A-3F' }, { number: 28, status: '地主保留' }, { number: 29, status: null }, { number: 30, status: null }, { number: 31, status: null },
      { number: 32, status: null }, { number: 33, status: '售: B-4F' }, { number: 34, status: null }, { number: 35, status: null }, { number: 36, status: null }, null, null, // Empty cells
      null, null, null, null, null, null // Empty cells
    ],
    available: 17, sold: 6
  },
  {
    level: 'P4F',
    spaces: [
      { number: 1, status: '售: D-11F' }, { number: 2, status: null }, { number: 3, status: null }, { number: 4, status: null }, { number: 5, status: null }, { number: 6, status: null }, { number: 7, status: null },
      { number: 8, status: '地主保留' }, { number: 9, status: '地主保留' }, { number: 10, status: '售: B-3F' }, { number: 11, status: '地主保留' }, { number: 12, status: '定: E-9F' }, { number: 13, status: null },
      { number: 14, status: '售: A-6F' }, { number: 15, status: null }, { number: 16, status: '售: F-14F' }, { number: 17, status: '售: A-7F' }, { number: 18, status: null }, null, null, // Empty cells
      null, null, null, null, null, null // Empty cells
    ],
    available: 15, sold: 7
  }
];

const parkingSummaryData: ParkingSummaryStatus[] = [
  { level: 'P1', type: '大', available: 6, sold: '-', percentage: '-' },
  { level: 'P1', type: '小', available: 5, sold: '-', percentage: '-' },
  { level: 'P2', type: '大', available: 9, sold: 2, percentage: '22.0%' },
  { level: 'P2', type: '小', available: 3, sold: '-', percentage: '-' },
  { level: 'P3', type: '大', available: 12, sold: 5, percentage: '41.0%' },
  { level: 'P3', type: '小', available: 2, sold: 1, percentage: '50.0%' },
  { level: 'P3', type: '難停', available: 3, sold: '-', percentage: '-' },
  { level: 'P4', type: '大', available: 13, sold: 5, percentage: '38.0%' },
  { level: 'P4', type: '小', available: 2, sold: 2, percentage: '100.0%' },
];

// --- END D. 去化分析 資料定義 ---


// 封裝獲取報表數據的函數
export const getWeeklyReportData = async () => {
  // 模擬 API 延遲
  await new Promise(resolve => setTimeout(resolve, 500));

  // 返回所有定義好的數據
  return {
    summary: summaryData,
    summaryColumns,
    mainChartOption,
    visitorPromotionList,
    dealPromotionList,
    mediaPromotionData,
    mediaPromotionColumns,
    marketNewsData,
    stockChartOption,
    weeklyOperationStatsData,
    operationChartOption,
    // genderAnalysisData,
    ageAnalysisData,
    industryAnalysisData,
    taipeiRegionCallsData,
    taipeiRegionVisitsData,
    taipeiRegionRevisitsData,
    taipeiRegionDealsData,
    newTaipeiRegionCallsData,
    newTaipeiRegionVisitsData,
    newTaipeiRegionRevisitsData,
    newTaipeiRegionDealsData,
    otherRegionCallsData,
    otherRegionVisitsData,
    otherRegionRevisitsData,
    otherRegionDealsData,
    mediaAnalysisCallsData,
    // Add C category data
    areaPreferenceCallsData,
    areaPreferenceVisitsData,
    areaPreferenceRevisitsData,
    areaPreferenceDealsData,
    purchasePurposeVisitsData,
    purchasePurposeRevisitsData,
    purchasePurposeDealsData,
    layoutPreferenceVisitsData,
    layoutPreferenceRevisitsData,
    layoutPreferenceDealsData,
    floorPreferenceVisitsData,
    floorPreferenceRevisitsData,
    floorPreferenceDealsData,
    budgetPreferenceVisitsData,
    budgetPreferenceRevisitsData,
    budgetPreferenceDealsData,
    // Add D category data
    buildingSalesStatusData,
    parkingSalesStatusData,
    parkingSummaryData,
  };
};

// 導出類型 (可選，方便在組件中引用)
export type {
  SummaryDataType,
  MediaDataType,
  NewsDataType,
  OperationStatsDataType,
  // GenderAnalysisDataType,
  AgeAnalysisDataType,
  IndustryAnalysisDataType,
  RegionAnalysisBaseType,
  RegionAnalysisDealType,
  MediaAnalysisDataType,
  // Add C category types
  GuestAnalysisDealType,
  AreaPreferenceDataType,
  AreaPreferenceDealDataType,
  PurchasePurposeDataType,
  PurchasePurposeDealDataType,
  LayoutPreferenceDataType,
  LayoutPreferenceDealDataType,
  FloorPreferenceDataType,
  FloorPreferenceDealDataType,
  BudgetPreferenceDataType,
  BudgetPreferenceDealDataType,
  // Add D category types
  BuildingFloorStatus,
  ParkingLevelStatus,
  ParkingSpaceStatus,
  ParkingSummaryStatus,
}; 