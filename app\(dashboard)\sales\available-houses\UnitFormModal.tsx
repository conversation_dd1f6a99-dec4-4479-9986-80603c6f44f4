'use client';

import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, InputNumber, Switch, App, Spin } from 'antd';
import type { CreateUnitDto, UpdateUnitDto, UnitDetailDto } from '@/app/interfaces/dto/unit.dto';
import type { DropdownItem } from '@/app/interfaces/dto/common.dto';
import { buildingApi } from '@/app/services/api/buildingApi';
// import { floorApi } from '@/app/services/api/floorApi'; // Might not be needed if using common dropdown

const { Option } = Select;

interface UnitFormModalProps {
  open: boolean;
  loading: boolean;
  isEditMode: boolean;
  initialValues?: UnitDetailDto | null;
  onCancel: () => void;
  onSubmit: (values: CreateUnitDto | UpdateUnitDto) => Promise<void>;
}

const UnitFormModal: React.FC<UnitFormModalProps> = ({
  open,
  loading,
  isEditMode,
  initialValues,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const { message: messageApi } = App.useApp();

  const [sites, setSites] = useState<DropdownItem[]>([]);
  const [buildings, setBuildings] = useState<DropdownItem[]>([]);
  const [floors, setFloors] = useState<DropdownItem[]>([]);

  const [selectedSiteCode, setSelectedSiteCode] = useState<string | undefined>(undefined);
  const [selectedBuildingId, setSelectedBuildingId] = useState<number | undefined>(undefined);
  
  const [dropdownLoading, setDropdownLoading] = useState({
    sites: false,
    buildings: false,
    floors: false,
  });

  // Load sites
  useEffect(() => {
    const fetchSites = async () => {
      setDropdownLoading(prev => ({ ...prev, sites: true }));
      try {
        const response = await buildingApi.getCommonDropdownList({ type: 'site' });
        if (response.isSuccess && response.body) {
          setSites(response.body);
        } else {
          messageApi.error(response.message || '獲取案場列表失敗');
        }
      } catch (error) {
        messageApi.error('無法載入案場資料');
      } finally {
        setDropdownLoading(prev => ({ ...prev, sites: false }));
      }
    };
    if (open) { // Only load when modal is opening or open
        fetchSites();
    }
  }, [open, messageApi]);

  // Load buildings when site changes
  useEffect(() => {
    const fetchBuildings = async (siteCode: string) => {
      setDropdownLoading(prev => ({ ...prev, buildings: true }));
      setBuildings([]);
      setFloors([]); // Clear floors when site changes
      form.setFieldsValue({ BuildingId: undefined, FloorId: undefined });
      try {
        const response = await buildingApi.getCommonDropdownList({ type: 'building', siteCode: siteCode });
        if (response.isSuccess && response.body) {
          setBuildings(response.body);
        } else {
          messageApi.error(response.message || '獲取建築列表失敗');
        }
      } catch (error) {
        messageApi.error('無法載入建築資料');
      } finally {
        setDropdownLoading(prev => ({ ...prev, buildings: false }));
      }
    };
    if (selectedSiteCode && open) {
      fetchBuildings(selectedSiteCode);
    } else {
      setBuildings([]); // Clear if no site selected
    }
  }, [selectedSiteCode, open, messageApi, form]);

  // Load floors when building changes
  useEffect(() => {
    const fetchFloors = async (buildingId: number) => {
      setDropdownLoading(prev => ({ ...prev, floors: true }));
      setFloors([]);
      form.setFieldsValue({ FloorId: undefined });
      try {
        // Assuming 'floor' type requires BuildingId
        const response = await buildingApi.getCommonDropdownList({ type: 'floor', buildingId: buildingId });
        if (response.isSuccess && response.body) {
          setFloors(response.body);
        } else {
          messageApi.error(response.message || '獲取樓層列表失敗');
        }
      } catch (error) {
        messageApi.error('無法載入樓層資料');
      } finally {
        setDropdownLoading(prev => ({ ...prev, floors: false }));
      }
    };
    if (selectedBuildingId && open) {
      fetchFloors(selectedBuildingId);
    } else {
      setFloors([]); // Clear if no building selected
    }
  }, [selectedBuildingId, open, messageApi, form]);

  useEffect(() => {
    if (open) {
        if (isEditMode && initialValues) {
            form.setFieldsValue({
                ...initialValues,
                // Ensure SiteCode, BuildingId, FloorId are set for dropdowns to load and select
                SiteCode: initialValues.SiteCode,
                BuildingId: initialValues.BuildingId,
                FloorId: initialValues.FloorId,
            });
            // Trigger loading of dependent dropdowns if values exist
            if (initialValues.SiteCode) {
                setSelectedSiteCode(initialValues.SiteCode);
            }
            if (initialValues.BuildingId) {
                setSelectedBuildingId(initialValues.BuildingId);
            }
        } else {
            form.resetFields();
            setSelectedSiteCode(undefined);
            setSelectedBuildingId(undefined);
            setBuildings([]);
            setFloors([]);
        }
    }
  }, [open, isEditMode, initialValues, form]);


  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      // Ensure numeric fields are numbers if they are optional and might be empty strings
      const payload = {
        ...values,
        MainArea: values.MainArea ? Number(values.MainArea) : undefined,
        AuxiliaryArea: values.AuxiliaryArea ? Number(values.AuxiliaryArea) : undefined,
        PublicAreaShare: values.PublicAreaShare ? Number(values.PublicAreaShare) : undefined,
        TotalArea: values.TotalArea ? Number(values.TotalArea) : undefined,
        ListPrice: values.ListPrice ? Number(values.ListPrice) : undefined,
        MinimumPrice: values.MinimumPrice ? Number(values.MinimumPrice) : undefined,
      };
      await onSubmit(payload);
      // Form reset is handled by parent component re-rendering or by useEffect on 'open'
    } catch (info) {
      console.log('欄位驗證失敗:', info);
      messageApi.warning('請檢查表單欄位是否都已正確填寫！');
    }
  };
  
  const unitStatuses = ['可售', '保留', '已訂', '已簽約', '已付訂', '已售', '已交屋', '暫緩銷售', '鎖控'];
  const unitTypes = ['住宅', '店鋪', '辦公室', '車位', '其他']; // Example types
  const layouts = ['一房', '二房', '三房', '四房', '五房以上', '套房', '特殊格局']; // Example layouts

  return (
    <Modal
      title={isEditMode ? '編輯房屋單位' : '新增房屋單位'}
      open={open}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
      destroyOnClose // Destroys children when closed, helps reset state within form elements
      maskClosable={false}
      width={800} // Adjust width as needed
    >
      <Spin spinning={dropdownLoading.sites || dropdownLoading.buildings || dropdownLoading.floors}>
        <Form form={form} layout="vertical" name="unitForm">
          <Form.Item
            name="SiteCode"
            label="案場"
            rules={[{ required: true, message: '請選擇案場' }]}
          >
            <Select
              placeholder="請選擇案場"
              onChange={(value) => {
                setSelectedSiteCode(value);
                // Reset dependent fields. Building/Floor useEffects will handle clearing their options.
                form.setFieldsValue({ BuildingId: undefined, FloorId: undefined });
                setSelectedBuildingId(undefined); // Clear selected building ID
              }}
              loading={dropdownLoading.sites}
              disabled={isEditMode} // Usually SiteCode is not editable for an existing unit
            >
              {sites.map(site => (
                <Option key={site.Value} value={site.Value}>{site.Name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="BuildingId"
            label="建築"
            rules={[{ required: true, message: '請選擇建築' }]}
          >
            <Select
              placeholder="請先選擇案場"
              onChange={(value) => {
                setSelectedBuildingId(value);
                // Reset dependent fields. Floor useEffect will handle clearing its options.
                form.setFieldsValue({ FloorId: undefined });
              }}
              loading={dropdownLoading.buildings}
              disabled={!selectedSiteCode || (isEditMode)} // Usually BuildingId is not editable
            >
              {buildings.map(building => (
                <Option key={building.Value} value={Number(building.Value)}>{building.Name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="FloorId"
            label="樓層"
            rules={[{ required: true, message: '請選擇樓層' }]}
          >
            <Select
              placeholder="請先選擇建築"
              loading={dropdownLoading.floors}
              disabled={!selectedBuildingId || (isEditMode)} // Usually FloorId is not editable
            >
              {floors.map(floor => (
                <Option key={floor.Value} value={Number(floor.Value)}>{floor.Name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="UnitNumber"
            label="戶號"
            rules={[{ required: true, message: '請輸入戶號' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="UnitType"
            label="房屋類型"
            rules={[{ required: true, message: '請選擇房屋類型' }]}
          >
            <Select placeholder="請選擇房屋類型">
                {unitTypes.map(type => <Option key={type} value={type}>{type}</Option>)}
            </Select>
          </Form.Item>

          <Form.Item
            name="Layout"
            label="格局"
            rules={[{ required: true, message: '請選擇格局' }]}
          >
             <Select placeholder="請選擇格局">
                {layouts.map(layout => <Option key={layout} value={layout}>{layout}</Option>)}
            </Select>
          </Form.Item>

          <Form.Item name="Orientation" label="座向">
            <Input />
          </Form.Item>

          <Form.Item name="MainArea" label="主建物面積 (坪)">
            <InputNumber style={{ width: '100%' }} min={0} precision={2} />
          </Form.Item>

          <Form.Item name="AuxiliaryArea" label="附屬建物面積 (坪)">
            <InputNumber style={{ width: '100%' }} min={0} precision={2} />
          </Form.Item>

          <Form.Item name="PublicAreaShare" label="公設面積分攤 (坪)">
            <InputNumber style={{ width: '100%' }} min={0} precision={2} />
          </Form.Item>

          <Form.Item name="TotalArea" label="權狀面積 (坪)">
            <InputNumber style={{ width: '100%' }} min={0} precision={2} />
          </Form.Item>

          <Form.Item name="ListPrice" label="表價 (元)">
            <InputNumber style={{ width: '100%' }} min={0} precision={0} formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')} parser={(value: string | undefined): number | null => {
              const cleaned = value?.replace(/\$\s?|(,*)/g, '');
              if (!cleaned) return null;
              const num = parseFloat(cleaned);
              return isNaN(num) ? null : num;
            }} />
          </Form.Item>

          <Form.Item name="MinimumPrice" label="底價 (元)">
            <InputNumber style={{ width: '100%' }} min={0} precision={0} formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')} parser={(value: string | undefined): number | null => {
              const cleaned = value?.replace(/\$\s?|(,*)/g, '');
              if (!cleaned) return null;
              const num = parseFloat(cleaned);
              return isNaN(num) ? null : num;
            }} />
          </Form.Item>
          
          <Form.Item
            name="Status"
            label="狀態"
            rules={[{ required: true, message: '請選擇狀態' }]}
          >
            <Select placeholder="請選擇狀態">
                {unitStatuses.map(status => <Option key={status} value={status}>{status}</Option>)}
            </Select>
          </Form.Item>

          <Form.Item name="IsPublicAreaIncluded" label="是否計入公設" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name="AssociatedParkingSpaceIds" label="關聯車位ID (以逗號分隔)">
            <Input placeholder="例如: 101,102,103"/>
          </Form.Item>

          <Form.Item name="Remarks" label="備註">
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default UnitFormModal; 