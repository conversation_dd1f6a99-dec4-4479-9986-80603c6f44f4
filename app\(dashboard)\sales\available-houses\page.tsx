'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { App, Button, Space, Card, Select, Input, DatePicker, Form, Row, Col } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import DataTable from '@/app/components/DataTable';
import { unifiedSalesApi } from '@/app/services/api/unifiedSalesApi';
import type { UnifiedSalesItem, HouseListItem } from '@/app/interfaces/dto/unified-sales.dto';
import type { BaseQueryParams, SearchTermInfo, SortOrderInfo } from '@/app/interfaces/dto/common.dto';
import type { SorterResult } from 'antd/es/table/interface';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;

export default function AvailableHousesPage() {
  const { message: messageApi } = App.useApp();
  const [form] = Form.useForm();
  
  // 資料狀態
  const [houses, setHouses] = useState<HouseListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(null);
  const [searchInfos, setSearchInfos] = useState<SearchTermInfo[] | undefined>(undefined);

  // 篩選狀態
  const [filterValues, setFilterValues] = useState({
    siteCode: '',
    buildingName: '',
    customerName: '',
    status: '',
    number: '',
    area: '',
    saleDate: null as [dayjs.Dayjs, dayjs.Dayjs] | null,
    reserveDate: null as [dayjs.Dayjs, dayjs.Dayjs] | null,
    contractDate: null as [dayjs.Dayjs, dayjs.Dayjs] | null,
  });

  // 表格欄位定義
  const columns: ColumnsType<HouseListItem> = [
    {
      title: '表單代碼',
      dataIndex: 'Id',
      key: 'Id',
    },
    {
      title: '建築',
      dataIndex: 'BuildingName',
      key: 'BuildingName',
    },
    {
      title: '建案名稱',
      dataIndex: 'SiteName',
      key: 'SiteName',
    },
    {
      title: '客戶名稱',
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: '坪數',
      dataIndex: 'Area',
      key: 'Area',
      align: 'right',
      render: (value: number) => value ? `${value.toFixed(2)}坪` : '-',
    },
    {
      title: '單位清潔',
      dataIndex: 'Layout',
      key: 'Layout',
    },
    {
      title: '售日期',
      dataIndex: 'saleDate',
      key: 'saleDate',
      render: (value: string) => value ? dayjs(value).format('YYYY-MM-DD') : '-',
    },
    {
      title: '定日期',
      dataIndex: 'reserveDate',
      key: 'reserveDate',
      render: (value: string) => value ? dayjs(value).format('YYYY-MM-DD') : '-',
    },
    {
      title: '簽日期',
      dataIndex: 'contractDate',
      key: 'contractDate',
      render: (value: string) => value ? dayjs(value).format('YYYY-MM-DD') : '-',
    },
    {
      title: '請款期數',
      dataIndex: 'Status',
      key: 'Status',
    },
    {
      title: '房地車總金額',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      align: 'right',
      render: (value: number) => value ? `$${value.toLocaleString()}` : '-',
    },
    {
      title: '業務業績比率清單',
      dataIndex: 'salesPersonCommission',
      key: 'salesPersonCommission',
    },
  ];

  // 獲取資料
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const sortOrderInfos: SortOrderInfo[] | undefined = sortField && sortOrder
        ? [{ SortField: sortField, SortOrder: sortOrder === 'ascend' ? 'asc' : 'desc' }]
        : undefined;

      const apiParams = {
        UsingPaging: true,
        PageIndex: currentPage,
        NumberOfPperPage: pageSize,
        SortOrderInfos: sortOrderInfos,
        SearchTermInfos: searchInfos,
        siteCode: filterValues.siteCode || undefined,
        number: filterValues.number || undefined,
      };

      const response = await unifiedSalesApi.getHousesAvailableReserved(apiParams);

      if (response.isSuccess && response.body) {
        // 直接使用API返回的資料
        setHouses(response.body.Detail);
        setTotal(response.body.RecordCount || 0);
        setCurrentPage(response.body.PageIndex || currentPage);
        setPageSize(response.body.NumberOfPperPage || pageSize);
      } else {
        messageApi.error(response.message || '獲取房屋資料失敗');
        // 提供一些模擬資料以便開發和測試
        const mockData: HouseListItem[] = Array.from({ length: 5 }, (_, index) => ({
          id: index + 1,
          itemType: '房屋',
          siteCode: 'SITE001',
          siteName: '台北龍都',
          buildingId: 1,
          buildingName: '吉美父來',
          floorId: index + 1,
          floorLabel: `${index + 10}F`,
          number: `A${index + 1}-1F-A`,
          type: '住宅',
          area: 30 + index * 5,
          layout: '3房2廳2衛',
          listPrice: 20000000 + index * 1000000,
          minimumPrice: 18000000 + index * 1000000,
          status: '可售',
          createdTime: '2024-01-15T09:30:00Z',
          updatedTime: '2024-01-15T09:30:00Z',
          customerName: '張先生',
          saleDate: '2024-01-15',
          reserveDate: '2024-01-20',
          contractDate: '2024-01-25',
          totalAmount: 25000000 + index * 1000000,
          salesPersonCommission: '業務A: 3%',
        }));
        setHouses(mockData);
        setTotal(5);
      }
    } catch (error) {
      console.error('獲取房屋資料失敗:', error);
      messageApi.error('無法載入房屋資料');
      // 提供一些模擬資料以便開發和測試
      const mockData: HouseListItem[] = Array.from({ length: 3 }, (_, index) => ({
        id: index + 1,
        itemType: '房屋',
        siteCode: 'SITE001',
        siteName: '台北龍都',
        buildingId: 1,
        buildingName: '吉美父來',
        floorId: index + 1,
        floorLabel: `${index + 10}F`,
        number: `A${index + 1}-1F-A`,
        type: '住宅',
        area: 30 + index * 5,
        layout: '3房2廳2衛',
        listPrice: 20000000 + index * 1000000,
        minimumPrice: 18000000 + index * 1000000,
        status: '可售',
        createdTime: '2024-01-15T09:30:00Z',
        updatedTime: '2024-01-15T09:30:00Z',
        customerName: '張先生',
        saleDate: '2024-01-15',
        reserveDate: '2024-01-20',
        contractDate: '2024-01-25',
        totalAmount: 25000000 + index * 1000000,
        salesPersonCommission: '業務A: 3%',
      }));
      setHouses(mockData);
      setTotal(3);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, sortField, sortOrder, searchInfos, filterValues, messageApi]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 分頁處理
  const handlePageChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  // 排序處理
  const handleSort = (sorter: SorterResult<HouseListItem> | SorterResult<HouseListItem>[]) => {
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    const newSortField = (currentSorter?.field as string) ?? null;
    const newSortOrder = currentSorter?.order ?? null;

    if (newSortField !== sortField || newSortOrder !== sortOrder) {
      setCurrentPage(1);
      setSortField(newSortField);
      setSortOrder(newSortOrder);
    }
  };

  // 篩選處理
  const handleFilter = (currentSearchInfos: SearchTermInfo[]) => {
    const newSearchInfos = currentSearchInfos.length > 0 ? currentSearchInfos : undefined;
    if (JSON.stringify(newSearchInfos) !== JSON.stringify(searchInfos)) {
      setCurrentPage(1);
      setSearchInfos(newSearchInfos);
    }
  };

  // 搜尋處理
  const handleSearch = () => {
    setCurrentPage(1);
    fetchData();
  };

  // 重置篩選
  const handleReset = () => {
    form.resetFields();
    setFilterValues({
      siteCode: '',
      buildingName: '',
      customerName: '',
      status: '',
      number: '',
      area: '',
      saleDate: null,
      reserveDate: null,
      contractDate: null,
    });
    setCurrentPage(1);
    setSearchInfos(undefined);
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">可售/保留(房屋資料)</h1>
      </div>

      {/* 上方搜尋條件 */}
      <Card title="篩選條件" className="mb-6">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={(changedValues, allValues) => {
            setFilterValues(prev => ({ ...prev, ...changedValues }));
          }}
        >
          <Row gutter={[16, 0]}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="案場代碼" name="siteCode">
                <Select placeholder="請選擇案場代碼" allowClear>
                  <Option value="SITE001">帝寶花園</Option>
                  <Option value="SITE002">美術館特區</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="建築名稱" name="buildingName">
                <Select placeholder="請選擇建築" allowClear>
                  <Option value="A棟">A棟</Option>
                  <Option value="B棟">B棟</Option>
                  <Option value="C棟">C棟</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="客戶名稱" name="customerName">
                <Input placeholder="請輸入客戶名稱" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="表單代碼" name="number">
                <Input placeholder="請輸入表單代碼" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="坪數" name="area">
                <Input placeholder="請輸入坪數" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="狀態" name="status">
                <Select placeholder="請選擇狀態" allowClear>
                  <Option value="可售">可售</Option>
                  <Option value="保留">保留</Option>
                  <Option value="已預訂">已預訂</Option>
                  <Option value="已售">已售</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="售日期" name="saleDate">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="定日期" name="reserveDate">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="簽日期" name="contractDate">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label=" " colon={false}>
                <Space>
                  <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                    搜尋
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={handleReset}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 下方資料表格 */}
      <Card>
        <DataTable<HouseListItem>
          columns={columns}
          dataSource={houses}
          loading={loading}
          total={total}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onSort={handleSort}
          onFilter={handleFilter}
          rowKey="Id"
        />
      </Card>
    </div>
  );
} 