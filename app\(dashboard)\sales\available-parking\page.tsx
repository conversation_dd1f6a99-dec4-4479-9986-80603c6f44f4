'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { App, Button, Space, Card, Select, Input, DatePicker, Form, Row, Col } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import DataTable from '@/app/components/DataTable';
import { unifiedSalesApi } from '@/app/services/api/unifiedSalesApi';
import type { ParkingListItem } from '@/app/interfaces/dto/unified-sales.dto';
import type { BaseQueryParams, SearchTermInfo, SortOrderInfo } from '@/app/interfaces/dto/common.dto';
import type { SorterResult } from 'antd/es/table/interface';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;

export default function AvailableParkingPage() {
  const { message: messageApi } = App.useApp();
  const [form] = Form.useForm();
  
  // 資料狀態
  const [parkingSpaces, setParkingSpaces] = useState<ParkingListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(null);
  const [searchInfos, setSearchInfos] = useState<SearchTermInfo[] | undefined>(undefined);

  // 篩選狀態
  const [filterValues, setFilterValues] = useState({
    siteCode: '',
    buildingName: '',
    customerName: '',
    status: '',
    number: '',
    houseOrderNumber: '',
    saleDate: null as [dayjs.Dayjs, dayjs.Dayjs] | null,
    reserveDate: null as [dayjs.Dayjs, dayjs.Dayjs] | null,
    contractDate: null as [dayjs.Dayjs, dayjs.Dayjs] | null,
  });

  // 表格欄位定義
  const columns: ColumnsType<ParkingListItem> = [
    {
      title: '表單代碼',
      dataIndex: 'Id',
      key: 'Id',
    },
    {
      title: '房屋訂單編號',
      dataIndex: 'houseOrderNumber',
      key: 'houseOrderNumber',
    },
    {
      title: '案場名稱',
      dataIndex: 'SiteName',
      key: 'SiteName',
    },
    {
      title: '客戶名稱',
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: '車位代碼',
      dataIndex: 'Number',
      key: 'Number',
    },
    {
      title: '售日期',
      dataIndex: 'saleDate',
      key: 'saleDate',
      render: (value: string) => value ? dayjs(value).format('YYYY-MM-DD') : '-',
    },
    {
      title: '定日期',
      dataIndex: 'reserveDate',
      key: 'reserveDate',
      render: (value: string) => value ? dayjs(value).format('YYYY-MM-DD') : '-',
    },
    {
      title: '簽日期',
      dataIndex: 'contractDate',
      key: 'contractDate',
      render: (value: string) => value ? dayjs(value).format('YYYY-MM-DD') : '-',
    },
    {
      title: '車位售價',
      dataIndex: 'ListPrice',
      key: 'ListPrice',
      align: 'right',
      render: (value: number) => value ? `$${value.toLocaleString()}` : '-',
    },
    {
      title: '車位底價',
      dataIndex: 'MinimumPrice',
      key: 'MinimumPrice',
      align: 'right',
      render: (value: number) => value ? `$${value.toLocaleString()}` : '-',
    },
    {
      title: '車位執行價',
      dataIndex: 'executionPrice',
      key: 'executionPrice',
      align: 'right',
      render: (value: number) => value ? `$${value.toLocaleString()}` : '-',
    },
    {
      title: '車位執行狀況',
      dataIndex: 'executionStatus',
      key: 'executionStatus',
    },
    {
      title: '車位訂單編號',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
    },
    {
      title: '車位訂單編輯',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="link" size="small">
            編輯
          </Button>
          <Button type="link" size="small">
            檢視
          </Button>
        </Space>
      ),
    },
  ];

  // 獲取資料
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const sortOrderInfos: SortOrderInfo[] | undefined = sortField && sortOrder
        ? [{ SortField: sortField, SortOrder: sortOrder === 'ascend' ? 'asc' : 'desc' }]
        : undefined;

      const apiParams = {
        UsingPaging: true,
        PageIndex: currentPage,
        NumberOfPperPage: pageSize,
        SortOrderInfos: sortOrderInfos,
        SearchTermInfos: searchInfos,
        siteCode: filterValues.siteCode || undefined,
        number: filterValues.number || undefined,
      };

      const response = await unifiedSalesApi.getParkingAvailableReserved(apiParams);

      if (response.isSuccess && response.body) {
        // 直接使用API返回的資料
        setParkingSpaces(response.body.Detail);
        setTotal(response.body.RecordCount || 0);
        setCurrentPage(response.body.PageIndex || currentPage);
        setPageSize(response.body.NumberOfPperPage || pageSize);
      } else {
        messageApi.error(response.message || '獲取車位資料失敗');
        // 提供一些模擬資料以便開發和測試
        const mockData: ParkingListItem[] = Array.from({ length: 5 }, (_, index) => ({
          id: index + 1,
          itemType: '車位',
          siteCode: 'SITE001',
          siteName: '台北龍都',
          buildingId: 1,
          buildingName: '吉美父來',
          floorId: 1,
          floorLabel: 'B1F',
          number: `P${(index + 1).toString().padStart(3, '0')}`,
          type: '坡道平面',
          layout: '250x550cm',
          listPrice: 1500000 + index * 100000,
          minimumPrice: 1300000 + index * 100000,
          status: '可售',
          createdTime: '2024-01-15T10:00:00Z',
          updatedTime: '2024-01-15T10:00:00Z',
          houseOrderNumber: 'H001-2024',
          customerName: '李先生',
          saleDate: '2024-01-16',
          reserveDate: '2024-01-21',
          contractDate: '2024-01-26',
          executionPrice: 1500000 + index * 100000,
          executionStatus: '正常',
          orderNumber: `P${(index + 1).toString().padStart(3, '0')}-2024`,
        }));
        setParkingSpaces(mockData);
        setTotal(5);
      }
    } catch (error) {
      console.error('獲取車位資料失敗:', error);
      messageApi.error('無法載入車位資料');
      // 提供一些模擬資料以便開發和測試
      const mockData: ParkingListItem[] = Array.from({ length: 3 }, (_, index) => ({
        id: index + 1,
        itemType: '車位',
        siteCode: 'SITE001',
        siteName: '台北龍都',
        buildingId: 1,
        buildingName: '吉美父來',
        floorId: 1,
        floorLabel: 'B1F',
        number: `P${(index + 1).toString().padStart(3, '0')}`,
        type: '坡道平面',
        layout: '250x550cm',
        listPrice: 1500000 + index * 100000,
        minimumPrice: 1300000 + index * 100000,
        status: '可售',
        createdTime: '2024-01-15T10:00:00Z',
        updatedTime: '2024-01-15T10:00:00Z',
        houseOrderNumber: 'H001-2024',
        customerName: '李先生',
        saleDate: '2024-01-16',
        reserveDate: '2024-01-21',
        contractDate: '2024-01-26',
        executionPrice: 1500000 + index * 100000,
        executionStatus: '正常',
        orderNumber: `P${(index + 1).toString().padStart(3, '0')}-2024`,
      }));
      setParkingSpaces(mockData);
      setTotal(3);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, sortField, sortOrder, searchInfos, filterValues, messageApi]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 分頁處理
  const handlePageChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  // 排序處理
  const handleSort = (sorter: SorterResult<ParkingListItem> | SorterResult<ParkingListItem>[]) => {
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    const newSortField = (currentSorter?.field as string) ?? null;
    const newSortOrder = currentSorter?.order ?? null;

    if (newSortField !== sortField || newSortOrder !== sortOrder) {
      setCurrentPage(1);
      setSortField(newSortField);
      setSortOrder(newSortOrder);
    }
  };

  // 篩選處理
  const handleFilter = (currentSearchInfos: SearchTermInfo[]) => {
    const newSearchInfos = currentSearchInfos.length > 0 ? currentSearchInfos : undefined;
    if (JSON.stringify(newSearchInfos) !== JSON.stringify(searchInfos)) {
      setCurrentPage(1);
      setSearchInfos(newSearchInfos);
    }
  };

  // 搜尋處理
  const handleSearch = () => {
    setCurrentPage(1);
    fetchData();
  };

  // 重置篩選
  const handleReset = () => {
    form.resetFields();
    setFilterValues({
      siteCode: '',
      buildingName: '',
      customerName: '',
      status: '',
      number: '',
      houseOrderNumber: '',
      saleDate: null,
      reserveDate: null,
      contractDate: null,
    });
    setCurrentPage(1);
    setSearchInfos(undefined);
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">可售/保留(車位資料)</h1>
      </div>

      {/* 上方搜尋條件 */}
      <Card title="篩選條件" className="mb-6">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={(changedValues, allValues) => {
            setFilterValues(prev => ({ ...prev, ...changedValues }));
          }}
        >
          <Row gutter={[16, 0]}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="案場代碼" name="siteCode">
                <Select placeholder="請選擇案場代碼" allowClear>
                  <Option value="SITE001">帝寶花園</Option>
                  <Option value="SITE002">美術館特區</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="建築名稱" name="buildingName">
                <Select placeholder="請選擇建築" allowClear>
                  <Option value="A棟">A棟</Option>
                  <Option value="B棟">B棟</Option>
                  <Option value="C棟">C棟</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="客戶名稱" name="customerName">
                <Input placeholder="請輸入客戶名稱" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="車位代碼" name="number">
                <Input placeholder="請輸入車位代碼" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="房屋訂單編號" name="houseOrderNumber">
                <Input placeholder="請輸入房屋訂單編號" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="狀態" name="status">
                <Select placeholder="請選擇狀態" allowClear>
                  <Option value="可售">可售</Option>
                  <Option value="保留">保留</Option>
                  <Option value="已預訂">已預訂</Option>
                  <Option value="已售">已售</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="售日期" name="saleDate">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="定日期" name="reserveDate">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="簽日期" name="contractDate">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label=" " colon={false}>
                <Space>
                  <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                    搜尋
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={handleReset}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 下方資料表格 */}
      <Card>
        <DataTable<ParkingListItem>
          columns={columns}
          dataSource={parkingSpaces}
          loading={loading}
          total={total}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onSort={handleSort}
          onFilter={handleFilter}
          rowKey="Id"
        />
      </Card>
    </div>
  );
} 