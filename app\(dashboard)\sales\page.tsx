'use client';

import React from 'react';
import { Card, Row, Col, Statistic, Button, Space } from 'antd';
import { HomeOutlined, CarOutlined, BarChartOutlined, FileTextOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';

export default function SalesPage() {
  const router = useRouter();

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">銷售管理</h1>
        <p className="text-gray-600">統一管理房屋與車位的銷售資料</p>
      </div>

      {/* 統計卡片 */}
      <Row gutter={[16, 16]} className="mb-8">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="總可售房屋"
              value={25}
              prefix={<HomeOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="總可售車位"
              value={15}
              prefix={<CarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="本月銷售"
              value={8}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="保留物件"
              value={12}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 功能導航 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Card
            title="房屋銷售管理"
            extra={
              <Button 
                type="primary" 
                onClick={() => router.push('/sales/available-houses')}
              >
                查看詳情
              </Button>
            }
            className="h-full"
          >
            <div className="space-y-4">
              <p className="text-gray-600">
                管理可售與保留的房屋資料，包含房屋代碼、建案名稱、客戶資訊、
                銷售日期、總金額等完整資訊。
              </p>
              <div className="flex justify-between text-sm">
                <span>可售房屋: <strong className="text-green-600">25戶</strong></span>
                <span>保留房屋: <strong className="text-orange-600">5戶</strong></span>
                <span>已售房屋: <strong className="text-red-600">20戶</strong></span>
              </div>
              <Space wrap>
                <Button 
                  size="small" 
                  onClick={() => router.push('/sales/available-houses')}
                >
                  房屋資料查詢
                </Button>
                <Button size="small">銷售統計</Button>
                <Button size="small">客戶管理</Button>
              </Space>
            </div>
          </Card>
        </Col>

        <Col xs={24} md={12}>
          <Card
            title="車位銷售管理"
            extra={
              <Button 
                type="primary" 
                onClick={() => router.push('/sales/available-parking')}
              >
                查看詳情
              </Button>
            }
            className="h-full"
          >
            <div className="space-y-4">
              <p className="text-gray-600">
                管理可售與保留的車位資料，包含車位代碼、案場名稱、關聯房屋訂單、
                車位價格、執行狀況等資訊。
              </p>
              <div className="flex justify-between text-sm">
                <span>可售車位: <strong className="text-green-600">15個</strong></span>
                <span>保留車位: <strong className="text-orange-600">3個</strong></span>
                <span>已售車位: <strong className="text-red-600">32個</strong></span>
              </div>
              <Space wrap>
                <Button 
                  size="small" 
                  onClick={() => router.push('/sales/available-parking')}
                >
                  車位資料查詢
                </Button>
                <Button size="small">價格分析</Button>
                <Button size="small">訂單管理</Button>
              </Space>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" className="mt-6">
        <Space wrap size="large">
          <Button 
            type="default" 
            icon={<BarChartOutlined />}
            size="large"
          >
            銷售統計報表
          </Button>
          <Button 
            type="default" 
            icon={<FileTextOutlined />}
            size="large"
          >
            銷售合約管理
          </Button>
          <Button 
            type="default" 
            icon={<HomeOutlined />}
            size="large"
          >
            物件狀態更新
          </Button>
          <Button 
            type="default" 
            icon={<CarOutlined />}
            size="large"
          >
            車位配置管理
          </Button>
        </Space>
      </Card>
    </div>
  );
} 