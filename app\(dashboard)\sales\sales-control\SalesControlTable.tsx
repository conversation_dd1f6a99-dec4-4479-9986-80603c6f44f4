'use client';

import React from 'react';
import { Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { BuildingSalesData, FloorInfo, UnitInfo } from './salesControlData';

interface SalesControlTableProps {
  data: BuildingSalesData;
  onUnitClick?: (floor: string, unit: string, unitInfo: UnitInfo) => void;
}

// 單位狀態樣式
const getUnitStatusStyle = (status: string): React.CSSProperties => {
  switch (status) {
    case '售':
      return {
        backgroundColor: '#e1f5fe', // 淺藍色
        color: '#000',
        textAlign: 'center',
        padding: '8px 4px',
        cursor: 'pointer',
        width: '100%',
        height: '100%',
        minHeight: '40px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        margin: 0,
        border: 'none'
      };
    case '足':
      return {
        backgroundColor: '#fff9c4', // 鵝黃色
        color: '#000',
        textAlign: 'center',
        padding: '8px 4px',
        cursor: 'pointer',
        width: '100%',
        height: '100%',
        minHeight: '40px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        margin: 0,
        border: 'none'
      };
    case '簽':
      return {
        backgroundColor: '#fce4ec', // 粉紅色
        color: '#000',
        textAlign: 'center',
        padding: '8px 4px',
        cursor: 'pointer',
        width: '100%',
        height: '100%',
        minHeight: '40px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        margin: 0,
        border: 'none'
      };

    case '保留':
      return {
        backgroundColor: '#e0e0e0', // 灰色
        color: '#000',
        textAlign: 'center',
        padding: '8px 4px',
        cursor: 'pointer',
        width: '100%',
        height: '100%',
        minHeight: '40px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        margin: 0,
        border: 'none'
      };
    case '可售':
    default:
      return {
        backgroundColor: '#fff', // 無色 (白色)
        color: '#000',
        textAlign: 'center',
        padding: '8px 4px',
        cursor: 'pointer',
        width: '100%',
        height: '100%',
        minHeight: '40px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        margin: 0,
        border: 'none'
      };
  }
};

// 渲染單位格子
const renderUnit = (
  unitInfo: UnitInfo, 
  floorNumber: string, 
  onUnitClick?: (floor: string, unit: string, unitInfo: UnitInfo) => void
) => {
  const { status, unitNumber, customerName, saleDate, purchasedParkingSpaces, area, requestDate, receiveDate, priceRegistrationSubmissionDate } = unitInfo;
  
  // 決定要顯示的內容 - 固定三行格式，保持空格佔位
  let displayContent: (string | null)[] = [];
  
  // 處理客戶姓名顯示邏輯：客戶姓名 / 請 or 客戶姓名 / 領 (如果請跟領都有則顯示領)
  const getCustomerNameDisplay = () => {
    if (!customerName) return null;
    
    if (receiveDate) {
      return `${customerName} / 領`;
    } else if (requestDate) {
      return `${customerName} / 請`;
    } else {
      return customerName;
    }
  };

  // 處理坪數顯示邏輯：坪數 / 登(如果有實價登錄的值的話)
  const getAreaDisplay = () => {
    if (!area) return null;
    
    if (priceRegistrationSubmissionDate) {
      return `${area}坪 / 登`;
    } else {
      return `${area}坪`;
    }
  };

  // 根據狀態決定顯示內容，已售狀態固定三行格式
  if (status === '售' || status === '簽' || status === '足') {
    // 已售出或相關狀態，固定三行：日期、客戶姓名、坪數
    displayContent = [
      saleDate || null,           // 第1行：售出日期
      getCustomerNameDisplay(),   // 第2行：客戶姓名 (可能包含 / 請 或 / 領)
      getAreaDisplay()            // 第3行：坪數 (可能包含 / 登)
    ];
  } else if (status === '保留') {
    // 保留狀態，固定三行格式（與已售狀態保持一致）
    displayContent = [
      null,                       // 第1行：空（保留狀態通常沒有售出日期）
      getCustomerNameDisplay(),   // 第2行：客戶姓名 (可能包含 / 請 或 / 領)
      getAreaDisplay()            // 第3行：坪數 (可能包含 / 登)
    ];
  } else {
    // 可售狀態，固定三行格式（與已售狀態保持一致）
    displayContent = [
      null,                       // 第1行：空
      null,                       // 第2行：空
      getAreaDisplay()            // 第3行：坪數 (可能包含 / 登)
    ];
  }
  
  const tooltipInfo = [
    `${floorNumber}-${unitNumber}`,
    `狀態: ${status}`,
    customerName && `客戶: ${customerName}`,
    saleDate && `售出日期: ${saleDate}`,
    requestDate && `請的日期: ${requestDate}`,
    receiveDate && `領的日期: ${receiveDate}`,
    priceRegistrationSubmissionDate && `實價登錄: ${priceRegistrationSubmissionDate}`,
    area && `坪數: ${area}坪`,
    purchasedParkingSpaces && `車位: ${purchasedParkingSpaces}`
  ].filter(Boolean).join('\n');
  
  return (
    <div
      style={getUnitStatusStyle(status)}
      onClick={() => onUnitClick?.(floorNumber, unitNumber, unitInfo)}
      title={tooltipInfo}
    >
      <div style={{ 
        fontSize: '11px', 
        lineHeight: '1.1',
        wordBreak: 'break-word',
        textAlign: 'center',
        padding: '2px'
      }}>
        {displayContent.map((content, index) => (
          <div key={index} style={{ 
            marginBottom: index < displayContent.length - 1 ? '1px' : '0',
            minHeight: '12px' // 確保空行也有高度
          }}>
            {content || '\u00A0'} {/* 使用不間斷空格佔位 */}
          </div>
        ))}
      </div>
    </div>
  );
};

const SalesControlTable: React.FC<SalesControlTableProps> = ({ data, onUnitClick }) => {
  // 從 ColumnSummary 獲取動態的欄位列表
  const dynamicColumns = data.columnSummary.map(col => col.column);
  
  // 組合表格資料：樓層資料 + 摘要資料
  const tableData = [
    ...data.floors.map((floor, index) => ({ ...floor, rowType: 'floor', rowIndex: index })),
    // 分隔線
    {
      floorNumber: '---',
      units: dynamicColumns.map(() => ({ unitNumber: '', status: '可售' })),
      summary: { available: 0, sold: 0, salesRate: '' },
      rowType: 'separator',
      rowIndex: 'separator'
    },
    // 可售總計
    {
      floorNumber: '可售',
      units: data.columnSummary.map(col => ({
        unitNumber: col.column,
        status: '可售',
        displayValue: col.available
      })),
      summary: { available: data.totalSummary.totalAvailable, sold: 0, salesRate: '' },
      rowType: 'summary',
      rowIndex: 'available'
    },
    // 已售總計
    {
      floorNumber: '已售',
      units: data.columnSummary.map(col => ({
        unitNumber: col.column,
        status: '可售',
        displayValue: col.sold
      })),
      summary: { available: 0, sold: data.totalSummary.totalSold, salesRate: '' },
      rowType: 'summary',
      rowIndex: 'sold'
    },
    // 去化百分比
    {
      floorNumber: '去化百分比',
      units: data.columnSummary.map(col => ({
        unitNumber: col.column,
        status: '可售',
        displayValue: col.salesRate
      })),
      summary: { available: 0, sold: 0, salesRate: data.totalSummary.overallSalesRate },
      rowType: 'summary',
      rowIndex: 'rate'
    }
  ];

  // 動態生成表格欄位配置
  const generateDynamicColumns = (): ColumnsType<any> => {
    const columns: ColumnsType<any> = [
      {
        title: '樓層',
        dataIndex: 'floorNumber',
        key: 'floorNumber',
        width: 80,
        fixed: 'left',
        align: 'center',
        render: (text: string) => {
          if (text === '---') return <div style={{ height: '2px', backgroundColor: '#d9d9d9' }} />;
          return <strong>{text}</strong>;
        }
      }
    ];

    // 動態添加單位欄位 (A, B, C, ...)
    dynamicColumns.forEach((columnName, columnIndex) => {
      columns.push({
        title: columnName,
        key: `unit${columnName}`,
        width: 120,
        align: 'center',
        render: (_, record: FloorInfo) => {
          if (record.floorNumber === '---') return null;
          
          // 查找對應的單位資料
          const unit = record.units?.find(u => u.unitNumber === columnName);
          
          if (!unit) {
            return <div style={{ textAlign: 'center', padding: '8px' }}>-</div>;
          }
          
          if (unit.displayValue !== undefined) {
            return <div style={{ textAlign: 'center', padding: '8px' }}>{unit.displayValue}</div>;
          }
          
          return renderUnit(unit, record.floorNumber, onUnitClick);
        }
      });
    });

    // 右側摘要欄位
    columns.push(
      {
        title: '樓層',
        key: 'floorSummary',
        width: 80,
        align: 'center',
        render: (_, record: FloorInfo) => {
          if (record.floorNumber === '---') return null;
          if (['可售', '已售', '去化百分比'].includes(record.floorNumber)) return null;
          return <strong>{record.floorNumber}</strong>;
        }
      },
      {
        title: '可售',
        key: 'available',
        width: 80,
        align: 'center',
        render: (_, record: FloorInfo) => {
          if (record.floorNumber === '---') return null;
          if (['已售', '去化百分比'].includes(record.floorNumber)) return null;
          return record.summary.available || '-';
        }
      },
      {
        title: '已售',
        key: 'sold',
        width: 80,
        align: 'center',
        render: (_, record: FloorInfo) => {
          if (record.floorNumber === '---') return null;
          if (['可售', '去化百分比'].includes(record.floorNumber)) return null;
          return record.summary.sold || '-';
        }
      },
      {
        title: '去化百分比',
        key: 'salesRate',
        width: 100,
        align: 'center',
        render: (_, record: FloorInfo) => {
          if (record.floorNumber === '---') return null;
          if (['可售', '已售'].includes(record.floorNumber)) return null;
          return record.summary.salesRate || '-';
        }
      }
    );

    return columns;
  };

  const columns = generateDynamicColumns();

  return (
    <div className="sales-control-table">
      <Table
        columns={columns}
        dataSource={tableData}
        pagination={false}
        scroll={{ x: 'max-content' }}
        size="small"
        rowKey={(record) => `${record.rowType || 'floor'}-${record.floorNumber}-${record.rowIndex || 'default'}`}
        bordered
        className="sales-control-antd-table"
      />
      
      <style jsx global>{`
        .sales-control-antd-table .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: bold;
          text-align: center;
          padding: 8px 4px;
          vertical-align: middle;
        }
        
        .sales-control-antd-table .ant-table-tbody > tr > td {
          padding: 0 !important;
          vertical-align: middle;
          text-align: center;
          height: 60px;
        }
        
        .sales-control-antd-table .ant-table-tbody > tr:hover > td {
          background: inherit;
        }
        
        .sales-control-antd-table .ant-table-cell {
          vertical-align: middle !important;
        }
        
        .sales-control-table {
          background: white;
          border-radius: 6px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </div>
  );
};

export default SalesControlTable; 