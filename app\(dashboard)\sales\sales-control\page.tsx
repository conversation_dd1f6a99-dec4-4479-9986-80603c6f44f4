'use client';

import React, { useState, useEffect } from 'react';
import { Card, Spin, Typography, Space, Button, Modal, Form, Select, Input, Row, Col, App, Switch, Tag } from 'antd';
import { EditOutlined, InfoCircleOutlined, TableOutlined, AppstoreOutlined } from '@ant-design/icons';
import SalesControlTable from './SalesControlTable';
import DataTable from '@/app/components/DataTable';
import { BuildingSalesData, UnitInfo, UnitSalesStatus } from './salesControlData';
import { siteApi } from '@/app/services/api/siteApi';
import { salesControlApi } from '@/app/services/api/salesControlApi';
import { buildingApi } from '@/app/services/api/buildingApi';
import type { DropdownItem } from '@/app/interfaces/dto/common.dto';
import type { 
  SalesControlData, 
  GetSalesControlParams,
  UpdateUnitStatusDto,
  SalesStatistics
} from '@/app/interfaces/dto/sales-control.dto';
import type { SearchInfo } from '@/app/components/DataTable';

const { Title, Text } = Typography;
const { Option } = Select;

// 列表檢視的資料介面
interface SalesControlListItem {
  id: string;
  saleDate: string; // 售
  fullPaymentDate: string; // 足
  contractDate: string; // 簽
  customerName: string; // 客戶姓名
  unitType: string; // 戶別
  area: number; // 坪數
  housePrice: number; // 房成交
  parkingSpace: string; // 車位
  parkingPrice: string; // 車成交
  realPriceRegistration: string; // 實價登錄
  salesperson: string; // 業務員
}

// 資料轉換函數：將後端格式轉換為前端格式
const transformSalesControlData = (apiData: SalesControlData): BuildingSalesData => {
  // 處理去化率的函數 - 確保正確的百分比顯示
  const formatSalesRate = (rate: string | number): string => {
    if (rate === null || rate === undefined || rate === '') {
      return '-';
    }
    
    // 如果是字串且已經包含%，直接返回
    if (typeof rate === 'string' && rate.includes('%')) {
      return rate;
    }
    
    // 如果是字串且為'-'，直接返回
    if (typeof rate === 'string' && rate === '-') {
      return rate;
    }
    
    // 轉換為數字
    const numRate = typeof rate === 'string' ? parseFloat(rate) : rate;
    
    // 如果是無效數字，返回'-'
    if (isNaN(numRate)) {
      return '-';
    }
    
    // 格式化為百分比（保留到整數）
    return `${Math.round(numRate)}%`;
  };

  return {
    floors: apiData.Floors.map(floor => ({
      floorNumber: floor.FloorLevel > 0 ? `${floor.FloorLevel}F` : floor.FloorLabel,
      units: floor.Units.map(unit => ({
        unitNumber: unit.UnitNumber,
        status: (unit.Status?.includes('保留') ? '保留' : unit.Status) as UnitSalesStatus,
        purchaseInfo: unit.PurchaseInfo || '',
        salePrice: unit.SalePrice || 0,
        area: unit.Area,
        unitType: unit.UnitType,
        displayValue: unit.DisplayValue,
        unitId: unit.UnitId, // 新增 unitId 以便更新
        // 新增後端補上的欄位
        saleDate: unit.SaleDate,
        customerName: unit.CustomerName,
        purchasedParkingSpaces: unit.PurchasedParkingSpaces,
        floorLabel: unit.FloorLabel,
        buildingName: unit.BuildingName
      })),
      summary: {
        available: floor.Summary.Available,
        sold: floor.Summary.Sold,
        salesRate: formatSalesRate(floor.Summary.SalesRate)
      }
    })),
    columnSummary: apiData.ColumnSummary.map(col => ({
      column: col.Column,
      available: col.Available,
      sold: col.Sold,
      salesRate: formatSalesRate(col.SalesRate)
    })),
    totalSummary: {
      totalAvailable: apiData.TotalSummary.TotalAvailable,
      totalSold: apiData.TotalSummary.TotalSold,
      totalReserved: apiData.TotalSummary.TotalReserved,
      overallSalesRate: formatSalesRate(apiData.TotalSummary.OverallSalesRate)
    }
  };
};

// 生成假資料的函數
const generateMockListData = (): SalesControlListItem[] => {
  const mockData: SalesControlListItem[] = [];
  const customers = ['王小明', '李小華', '張美玲', '陳大雄', '林淑芬', '趙志明', '黃美惠', ''];
  const unitTypes = ['A-15F', 'B-15F', 'C-12F', 'A-8F', 'B-10F', 'C-15F', 'D-5F', 'E-3F'];
  const singleParkingSpaces = ['P2-50', 'P2-51', 'P2-52', 'P1-01', 'P1-05', 'B1-10', 'B2-15', 'B3-20'];
  const salespeople = ['張業務', '李業務', '王業務', '陳業務', '林業務', '黃業務'];
  const registrationStatuses = ['已登錄', '未登錄', '處理中', ''];
  
  // 生成50筆假資料
  for (let i = 1; i <= 50; i++) {
    const customerIndex = Math.floor(Math.random() * customers.length);
    const hasCustomer = customers[customerIndex] && customers[customerIndex] !== '';
    
    // 隨機決定車位數量（0-3個車位）
    const parkingSpaceCount = Math.floor(Math.random() * 4);
    let parkingSpaceData = '';
    let parkingPriceData = '';
    
    if (parkingSpaceCount > 0) {
      // 隨機選擇車位
      const selectedSpaces: string[] = [];
      const spacePrices: string[] = [];
      for (let j = 0; j < parkingSpaceCount; j++) {
        const randomSpace = singleParkingSpaces[Math.floor(Math.random() * singleParkingSpaces.length)];
        if (!selectedSpaces.includes(randomSpace)) {
          selectedSpaces.push(randomSpace);
          // 為每個車位生成不同的價格 (200-350萬)
          const price = Math.floor(Math.random() * 150) + 200;
          spacePrices.push(price.toString());
        }
      }
      parkingSpaceData = selectedSpaces.join(',');
      parkingPriceData = spacePrices.join(',');
    }
    
    // 生成日期
    const generateRandomDate = () => {
      const year = 2024;
      const month = Math.floor(Math.random() * 12) + 1;
      const day = Math.floor(Math.random() * 28) + 1;
      return hasCustomer && Math.random() > 0.3 ? `${year}/${month}/${day}` : '';
    };
    
    mockData.push({
      id: `unit-${i}`,
      saleDate: generateRandomDate(), // 售
      fullPaymentDate: generateRandomDate(), // 足
      contractDate: generateRandomDate(), // 簽
      customerName: customers[customerIndex] || '', // 客戶姓名
      unitType: unitTypes[Math.floor(Math.random() * unitTypes.length)], // 戶別
      area: Math.floor(Math.random() * 20) + 25, // 25-45坪
      housePrice: hasCustomer ? Math.floor(Math.random() * 2000) + 3000 : 0, // 房成交 3000-5000萬
      parkingSpace: parkingSpaceData, // 車位
      parkingPrice: parkingPriceData, // 車成交
      realPriceRegistration: hasCustomer ? registrationStatuses[Math.floor(Math.random() * registrationStatuses.length)] : '', // 實價登錄
      salesperson: hasCustomer ? salespeople[Math.floor(Math.random() * salespeople.length)] : '' // 業務員
    });
  }
  
  return mockData;
};

const SalesControlPageContent: React.FC = () => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState(true);
  const [salesData, setSalesData] = useState<BuildingSalesData | null>(null);
  const [salesStats, setSalesStats] = useState<SalesStatistics | null>(null);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedUnit, setSelectedUnit] = useState<{
    floor: string;
    unit: string;
    unitInfo: UnitInfo & { unitId?: number };
  } | null>(null);
  const [form] = Form.useForm();
  
  // 檢視模式狀態
  const [isListView, setIsListView] = useState(false);
  
  // 列表檢視相關狀態
  const [listData, setListData] = useState<SalesControlListItem[]>([]);
  const [listLoading, setListLoading] = useState(false);
  const [listTotal, setListTotal] = useState(0);
  const [listCurrentPage, setListCurrentPage] = useState(1);
  const [listPageSize, setListPageSize] = useState(10);
  const [listSearchInfos, setListSearchInfos] = useState<SearchInfo[]>([]);
  
  // 案場相關狀態
  const [sites, setSites] = useState<DropdownItem[]>([]);
  const [selectedSite, setSelectedSite] = useState<string | undefined>(undefined);
  const [sitesLoading, setSitesLoading] = useState(false);

  // 建築物相關狀態
  const [buildings, setBuildings] = useState<DropdownItem[]>([]);
  const [selectedBuilding, setSelectedBuilding] = useState<string>('all'); // 預設為"全部"
  const [buildingsLoading, setBuildingsLoading] = useState(false);

  // 載入案場下拉清單
  const fetchSites = async () => {
    setSitesLoading(true);
    try {
      const res = await siteApi.getSiteDropdownList();
      if (res.isSuccess && res.body) {
        setSites(res.body);
        // 如果有案場資料，預設選擇第一個
        if (res.body.length > 0) {
          setSelectedSite(res.body[0].Value);
        }
      } else {
        message.error(res.message || '載入案場列表失敗');
        setSites([]); // 確保失敗時設為空陣列
      }
    } catch (error) {
      console.error('載入案場列表失敗:', error);
      message.error('載入案場列表失敗');
      setSites([]); // 確保錯誤時設為空陣列
    } finally {
      setSitesLoading(false);
    }
  };

  // 載入建築物清單
  const fetchBuildings = async (siteCode: string) => {
    if (!siteCode) return;
    
    setBuildingsLoading(true);
    try {
      const res = await buildingApi.getCommonDropdownList({ 
        type: 'building', 
        siteCode: siteCode 
      });
      
      if (res.isSuccess && res.body) {
        setBuildings(res.body);
        // 重置為"全部"選項
        setSelectedBuilding('all');
      } else {
        message.error(res.message || '載入建築物列表失敗');
        setBuildings([]);
        setSelectedBuilding('all');
      }
    } catch (error) {
      console.error('載入建築物列表失敗:', error);
      message.error('載入建築物列表失敗');
      setBuildings([]);
      setSelectedBuilding('all');
    } finally {
      setBuildingsLoading(false);
    }
  };

  // 載入銷控表資料
  const fetchSalesControlData = async (siteCode: string, buildingSelection: string) => {
    if (!siteCode) return;
    
    setLoading(true);
    try {
      // 根據建築物選擇決定 API 參數
      const params: GetSalesControlParams = {
        SiteCode: siteCode,
        ...(buildingSelection !== 'all' && { BuildingId: Number(buildingSelection) })
      };
      
      const res = await salesControlApi.getSalesControl(params);
      if (res.isSuccess && res.body) {
        const transformedData = transformSalesControlData(res.body);
        setSalesData(transformedData);
      } else {
        message.error(res.message || '載入銷控表失敗');
        setSalesData(null);
      }
    } catch (error) {
      console.error('載入銷控表失敗:', error);
      message.error('載入銷控表失敗');
      setSalesData(null);
    } finally {
      setLoading(false);
    }
  };

  // 載入銷售統計
  const fetchSalesStatistics = async (siteCode: string) => {
    if (!siteCode) return;
    
    try {
      const res = await salesControlApi.getSalesStatistics(siteCode);
      if (res.isSuccess && res.body) {
        setSalesStats(res.body);
      } else {
        console.warn('載入銷售統計失敗:', res.message);
        setSalesStats(null);
      }
    } catch (error) {
      console.error('載入銷售統計失敗:', error);
      setSalesStats(null);
    }
  };

  // 初始化假資料
  useEffect(() => {
    const mockData = generateMockListData();
    setListData(mockData);
    setListTotal(mockData.length);
  }, []);

  // 初始載入
  useEffect(() => {
    fetchSites();
  }, []);

  // 當選中案場時載入建築物清單
  useEffect(() => {
    if (selectedSite) {
      fetchBuildings(selectedSite);
      fetchSalesStatistics(selectedSite);
    }
  }, [selectedSite]);

  // 當選中建築物時載入銷控表
  useEffect(() => {
    if (selectedSite && selectedBuilding) {
      fetchSalesControlData(selectedSite, selectedBuilding);
    }
  }, [selectedSite, selectedBuilding]);

  // 案場變更處理
  const handleSiteChange = (siteCode: string) => {
    setSelectedSite(siteCode);
    setSelectedBuilding('all'); // 重置為"全部"
    setSalesData(null); // 清空銷控表資料
    const siteName = Array.isArray(sites) ? sites.find(s => s.Value === siteCode)?.Name || siteCode : siteCode;
    message.info(`切換至案場：${siteName}`);
  };

  // 建築物變更處理
  const handleBuildingChange = (buildingSelection: string) => {
    setSelectedBuilding(buildingSelection);
    if (buildingSelection === 'all') {
      message.info('顯示全部建築物');
    } else {
      const buildingName = buildings.find(b => b.Value === buildingSelection)?.Name || buildingSelection;
      message.info(`切換至建築物：${buildingName}`);
    }
  };

  // 處理單位點擊
  const handleUnitClick = (floor: string, unit: string, unitInfo: UnitInfo) => {
    const extendedUnitInfo = unitInfo as UnitInfo & { unitId?: number };
    setSelectedUnit({ floor, unit, unitInfo: extendedUnitInfo });
    setEditModalVisible(true);
    
    // 設定表單預設值
    setTimeout(() => {
      form.setFieldsValue({
        status: unitInfo.status,
        purchaseInfo: unitInfo.purchaseInfo || '',
        salePrice: unitInfo.salePrice || '',
        area: unitInfo.area || '',
        unitType: unitInfo.unitType || '',
        orderId: 0,
        remarks: '',
        // 新增欄位的預設值
        customerName: unitInfo.customerName || '',
        saleDate: unitInfo.saleDate || '',
        purchasedParkingSpaces: unitInfo.purchasedParkingSpaces || ''
      });
    }, 0);
  };

  // 更新單位狀態
  const handleUpdateUnit = async (values: any) => {
    if (!selectedUnit || !selectedUnit.unitInfo.unitId) {
      message.error('找不到單位ID，無法更新');
      return;
    }

    try {
      const updateData: UpdateUnitStatusDto = {
        Status: values.status,
        PurchaseInfo: values.purchaseInfo || '',
        SalePrice: values.salePrice ? Number(values.salePrice) : 0,
        Area: values.area ? Number(values.area) : 0,
        UnitType: values.unitType || '',
        OrderId: values.orderId ? Number(values.orderId) : 0,
        Remarks: values.remarks || '',
        CustomerName: values.customerName || '',
        SaleDate: values.saleDate || '',
        PurchasedParkingSpaces: values.purchasedParkingSpaces || ''
      };

      const res = await salesControlApi.updateSalesControlUnit(selectedUnit.unitInfo.unitId, updateData);
      
      if (res.isSuccess) {
        message.success('單位狀態更新成功');
        setEditModalVisible(false);
        form.resetFields(); // 清空表單
        // 重新載入銷控表資料
        if (selectedSite && selectedBuilding) {
          await fetchSalesControlData(selectedSite, selectedBuilding);
        }
      } else {
        message.error(res.message || '更新失敗');
      }
    } catch (error) {
      console.error('更新單位狀態失敗:', error);
      message.error('更新失敗');
    }
  };

  // 處理 Modal 關閉
  const handleModalCancel = () => {
    setEditModalVisible(false);
    form.resetFields(); // 清空表單
    setSelectedUnit(null);
  };

  // 列表檢視處理函數
  const handleListPageChange = (page: number, size: number) => {
    setListCurrentPage(page);
    setListPageSize(size);
  };

  const handleListFilter = (searchInfos: SearchInfo[]) => {
    setListSearchInfos(searchInfos);
    setListCurrentPage(1);
  };

  // 切換檢視模式
  const handleViewToggle = (checked: boolean) => {
    setIsListView(checked);
    message.info(checked ? '已切換至列表檢視' : '已切換至網格檢視');
  };

  const statusOptions = [
    { value: '可售', label: '可售', color: '#fff' },
    { value: '保留', label: '保留', color: '#e0e0e0' },
    { value: '售', label: '售（訂金未付足）', color: '#e1f5fe' },
    { value: '足', label: '足（補足訂金）', color: '#fff9c4' },
    { value: '簽', label: '簽（已簽合約）', color: '#fce4ec' }
  ];

  // 列表檢視的欄位設定
  const listColumns = [
    {
      title: '售',
      dataIndex: 'saleDate',
      key: 'saleDate',
      sorter: true,
      allowSearch: true,
      width: 100
    },
    {
      title: '足',
      dataIndex: 'fullPaymentDate',
      key: 'fullPaymentDate',
      sorter: true,
      allowSearch: true,
      width: 100
    },
    {
      title: '簽',
      dataIndex: 'contractDate',
      key: 'contractDate',
      sorter: true,
      allowSearch: true,
      width: 100
    },
    {
      title: '客戶姓名',
      dataIndex: 'customerName',
      key: 'customerName',
      sorter: true,
      allowSearch: true,
      width: 120
    },
    {
      title: '戶別',
      dataIndex: 'unitType',
      key: 'unitType',
      sorter: true,
      allowSearch: true,
      width: 100
    },
    {
      title: '坪數',
      dataIndex: 'area',
      key: 'area',
      sorter: true,
      width: 80,
      render: (area: number) => `${area}坪`
    },
    {
      title: '房成交',
      dataIndex: 'housePrice',
      key: 'housePrice',
      sorter: true,
      width: 120,
      render: (price: number) => price > 0 ? `${price.toLocaleString()}萬` : ''
    },
    {
      title: '車位',
      dataIndex: 'parkingSpace',
      key: 'parkingSpace',
      sorter: true,
      allowSearch: true,
      width: 80,
      render: (parkingSpace: string) => {
        if (!parkingSpace) {
          return '';
        }
        
        // 用逗號分割車位資料
        const spaces = parkingSpace.split(',').map(space => space.trim()).filter(space => space);
        
        return (
          <div className="flex flex-col gap-1">
            {spaces.map((space, index) => (
              <Tag key={index} color="blue" style={{ width: 'fit-content' }}>
                {space}
              </Tag>
            ))}
          </div>
        );
      }
    },
    {
      title: '車成交',
      dataIndex: 'parkingPrice',
      key: 'parkingPrice',
      sorter: true,
      width: 100,
      render: (parkingPrice: string) => {
        if (!parkingPrice) {
          return '';
        }
        
        // 用逗號分割車位價格資料
        const prices = parkingPrice.split(',').map(price => price.trim()).filter(price => price);
        
        return (
          <div className="flex flex-col gap-1">
            {prices.map((price, index) => (
              <div key={index} className="text-sm">
                {price}萬
              </div>
            ))}
          </div>
        );
      }
    },
    {
      title: '實價登錄',
      dataIndex: 'realPriceRegistration',
      key: 'realPriceRegistration',
      sorter: true,
      allowSearch: true,
      width: 100
    },
    {
      title: '業務員',
      dataIndex: 'salesperson',
      key: 'salesperson',
      sorter: true,
      allowSearch: true,
      width: 100
    }
  ];

  return (
    <Spin spinning={loading} tip="載入銷控表中..." size="large">
      <div className="p-6 space-y-6">
        {/* 頁面標題 */}
        <div className="flex justify-between items-center">
          <div>
            <Title level={2} className="!mb-2">銷控表管理</Title>
            <Text type="secondary">建案銷售狀況控制表</Text>
          </div>
          <Space>
            <Select
              placeholder="請選擇案場"
              value={selectedSite}
              onChange={handleSiteChange}
              loading={sitesLoading}
              style={{ minWidth: 200 }}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                ((option?.children as any) || '')?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {Array.isArray(sites) && sites.map(site => (
                <Option key={site.Value} value={site.Value}>
                  {site.Name}
                </Option>
              ))}
            </Select>
            <Select
              placeholder="請選擇建築物"
              value={selectedBuilding}
              onChange={handleBuildingChange}
              loading={buildingsLoading}
              style={{ minWidth: 150 }}
              disabled={!selectedSite}
            >
              <Option key="all" value="all">全部</Option>
              {Array.isArray(buildings) && buildings.map(building => (
                <Option key={building.Value} value={building.Value}>
                  {building.Name}
                </Option>
              ))}
            </Select>
            <div className="flex items-center space-x-2">
              <AppstoreOutlined />
              <Switch 
                checked={isListView}
                onChange={handleViewToggle}
                size="small"
              />
              <TableOutlined />
              <Text type="secondary">切換列表</Text>
            </div>
            <Button 
              type="primary" 
              icon={<InfoCircleOutlined />}
              onClick={() => message.info('點擊任意單位格可進行編輯')}
            >
              使用說明
            </Button>
          </Space>
        </div>

        {/* 摘要卡片 */}
        {salesData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card size="small">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {salesStats?.AvailableUnits || salesData.totalSummary.totalAvailable}
                </div>
                <div className="text-gray-500">總可售</div>
              </div>
            </Card>
            <Card size="small">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {salesStats?.SoldUnits || salesData.totalSummary.totalSold}
                </div>
                <div className="text-gray-500">已售出</div>
              </div>
            </Card>
            <Card size="small">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {(() => {
                    if (salesStats?.SalesRate !== undefined && salesStats?.SalesRate !== null) {
                      // salesStats 中的 SalesRate 可能是小數格式 (0.4211) 或百分比數字 (42.11)
                      const rate = salesStats.SalesRate;
                      if (rate <= 1) {
                        // 如果 <= 1，視為小數格式，需要乘以100
                        return `${Math.round(rate * 100)}%`;
                      } else {
                        // 如果 > 1，視為百分比數字，直接四捨五入
                        return `${Math.round(rate)}%`;
                      }
                    }
                    // 使用已經格式化過的 salesData
                    return salesData.totalSummary.overallSalesRate;
                  })()}
                </div>
                <div className="text-gray-500">去化率</div>
              </div>
            </Card>
            <Card size="small">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {salesStats?.TotalUnits || (salesData.totalSummary.totalAvailable + salesData.totalSummary.totalSold + salesData.totalSummary.totalReserved)}
                </div>
                <div className="text-gray-500">總單位數</div>
              </div>
            </Card>
          </div>
        )}

        {/* 狀態說明 */}
        <Card size="small" title="狀態說明">
          <div className="flex gap-4 flex-wrap">
            <div className="flex items-center">
              <div className="w-4 h-4 bg-white border border-gray-300 mr-2"></div>
              <span>可售</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 border border-gray-300 mr-2" style={{ backgroundColor: '#e0e0e0' }}></div>
              <span>保留</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 border border-gray-300 mr-2" style={{ backgroundColor: '#e1f5fe' }}></div>
              <span>售</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 border border-gray-300 mr-2" style={{ backgroundColor: '#fff9c4' }}></div>
              <span>足</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 border border-gray-300 mr-2" style={{ backgroundColor: '#fce4ec' }}></div>
              <span>簽</span>
            </div>
          </div>
        </Card>

        {/* 銷控表 */}
        {!isListView && salesData && (
          <Card title="銷控表" className="w-full">
            <SalesControlTable 
              data={salesData} 
              onUnitClick={handleUnitClick}
            />
          </Card>
        )}

        {/* 列表檢視 */}
        {isListView && (
          <Card title="銷控列表" className="w-full">
            <DataTable<SalesControlListItem>
              columns={listColumns}
              dataSource={listData.slice(
                (listCurrentPage - 1) * listPageSize,
                listCurrentPage * listPageSize
              )}
              loading={listLoading}
              total={listTotal}
              currentPage={listCurrentPage}
              pageSize={listPageSize}
              onPageChange={handleListPageChange}
              onFilter={handleListFilter}
              rowKey="id"
            />
          </Card>
        )}

        {/* 編輯單位彈窗 */}
        <Modal
          title={selectedUnit ? `編輯單位 ${selectedUnit.floor}-${selectedUnit.unit}` : '編輯單位'}
          open={editModalVisible}
          onCancel={handleModalCancel}
          onOk={() => form.submit()}
          width={600}
          destroyOnClose={false}
          maskClosable={false}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleUpdateUnit}
            preserve={false}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="status"
                  label="銷售狀態"
                  rules={[{ required: true, message: '請選擇銷售狀態' }]}
                >
                  <Select placeholder="請選擇狀態">
                    {statusOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        <div className="flex items-center">
                          <div 
                            className="w-4 h-4 rounded mr-2 border"
                            style={{ backgroundColor: option.color }}
                          />
                          {option.label}
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="customerName"
                  label="客戶姓名"
                >
                  <Input placeholder="請輸入客戶姓名" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="saleDate"
                  label="售出日期"
                >
                  <Input placeholder="請輸入售出日期" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="purchasedParkingSpaces"
                  label="車位資訊"
                >
                  <Input placeholder="請輸入車位資訊" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="purchaseInfo"
                  label="購買者資訊"
                >
                  <Input placeholder="請輸入購買者資訊" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="salePrice"
                  label="售價"
                >
                  <Input type="number" placeholder="請輸入售價" addonAfter="萬" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="area"
                  label="坪數"
                >
                  <Input type="number" placeholder="請輸入坪數" addonAfter="坪" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="unitType"
                  label="房型"
                >
                  <Input placeholder="請輸入房型，如：3房2廳2衛" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="orderId"
                  label="訂單ID"
                >
                  <Input type="number" placeholder="請輸入訂單ID" />
                </Form.Item>
              </Col>
              <Col span={12}>
                {/* 預留空間 */}
              </Col>
            </Row>

            <Form.Item
              name="remarks"
              label="備註"
            >
              <Input.TextArea placeholder="請輸入備註" rows={3} />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </Spin>
  );
};

const SalesControlPage: React.FC = () => {
  return (
    <App>
      <SalesControlPageContent />
    </App>
  );
};

export default SalesControlPage; 