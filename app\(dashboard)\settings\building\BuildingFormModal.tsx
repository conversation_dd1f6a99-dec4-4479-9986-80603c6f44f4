import React, { useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Select } from 'antd';
import type { BuildingListItem, CreateBuildingDto, UpdateBuildingDto } from '@/app/interfaces/dto/building.dto';
import type { DropdownItem } from '@/app/interfaces/dto/common.dto';

interface BuildingFormModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (data: CreateBuildingDto | UpdateBuildingDto) => void;
  building: BuildingListItem | null;
  sites: DropdownItem[];
}

export default function BuildingFormModal({ open, onCancel, onOk, building, sites }: BuildingFormModalProps) {
  const [form] = Form.useForm<CreateBuildingDto | UpdateBuildingDto>();

  useEffect(() => {
    if (open) {
      if (building) {
        form.setFieldsValue({
          SiteCode: building.SiteCode,
          BuildingName: building.BuildingName,
          TotalAboveGroundFloors: building.TotalAboveGroundFloors,
          TotalBelowGroundFloors: building.TotalBelowGroundFloors,
          BuildingType: building.BuildingType,
          CompletionDate: building.CompletionDate,
          Remarks: (building as any).Remarks || ''
        });
      } else {
        form.resetFields();
      }
    }
  }, [open, building, form]);

  const handleFinish = (values: any) => {
    onOk(values as CreateBuildingDto | UpdateBuildingDto);
  };

  return (
    <Modal
      title={building ? '編輯建築' : '新增建築'}
      open={open}
      onCancel={onCancel}
      onOk={() => form.submit()}
      okText="確認"
      cancelText="取消"
      destroyOnClose
      width={800}
      styles={{
        body: { maxHeight: 'calc(100vh - 300px)', overflowY: 'auto' }
      }}
    >
      <Form form={form} layout="vertical" onFinish={handleFinish}>
        <Form.Item name="SiteCode" label="案場代號" rules={[{ required: true, message: '請選擇案場代號' }]}> 
          <Select placeholder="請選擇案場代號">
            {sites.map(item => (
              <Select.Option key={item.Value} value={item.Value}>
                {item.Name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item name="BuildingName" label="建築名稱" rules={[{ required: true, message: '請輸入建築名稱' }]}> 
          <Input />
        </Form.Item>

        <Form.Item name="TotalAboveGroundFloors" label="樓上層數" rules={[{ required: true, message: '請輸入樓上層數' }]}> 
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item name="TotalBelowGroundFloors" label="樓下層數" rules={[{ required: true, message: '請輸入樓下層數' }]}> 
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item name="BuildingType" label="建築類型" rules={[{ required: true, message: '請輸入建築類型' }]}> 
          <Input />
        </Form.Item>

        <Form.Item name="CompletionDate" label="完工日期" rules={[{ required: true, message: '請選擇完工日期' }]}> 
          <Input type="date" />
        </Form.Item>

        <Form.Item name="Remarks" label="備註"> 
          <Input.TextArea rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
} 