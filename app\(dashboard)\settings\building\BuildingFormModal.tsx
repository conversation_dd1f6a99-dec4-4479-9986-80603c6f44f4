import React, { useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Select, App, Row, Col, DatePicker } from 'antd';
import type { BuildingListItem, CreateBuildingDto, UpdateBuildingDto } from '@/app/interfaces/dto/building.dto';
import type { DropdownItem } from '@/app/interfaces/dto/common.dto';
import { siteApi } from '@/app/services/api/siteApi';
import { buildingApi } from '@/app/services/api/buildingApi';
import moment from 'moment';

interface BuildingFormModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (data: CreateBuildingDto | UpdateBuildingDto) => void;
  building: BuildingListItem | null;
  sites: DropdownItem[];
}

const { Option } = Select;

export default function BuildingFormModal({ open, onCancel, onOk, building, sites }: BuildingFormModalProps) {
  const [form] = Form.useForm<CreateBuildingDto | UpdateBuildingDto>();

  useEffect(() => {
    if (open) {
      if (building) {
        const formData = {
          ...building,
          CompletionDate: building.CompletionDate ? moment(building.CompletionDate, 'YYYY-MM-DD') : null,
        };
        form.setFieldsValue(formData as any);
      } else {
        form.resetFields();
      }
    }
  }, [open, building, form]);

  const handleFinish = (values: any) => {
    onOk(values as CreateBuildingDto | UpdateBuildingDto);
  };

  return (
    <Modal
      title={building ? '編輯建築' : '新增建築'}
      open={open}
      onCancel={onCancel}
      onOk={() => form.submit()}
      okText="確認"
      cancelText="取消"
      destroyOnClose
      width={800}
      styles={{
        body: { maxHeight: 'calc(100vh - 300px)', overflowY: 'auto' }
      }}
    >
      <Form form={form} layout="vertical" onFinish={handleFinish}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="SiteCode" label="案場名稱" rules={[{ required: true, message: '請選擇案場' }]}>
              <Select placeholder="請選擇案場" allowClear>
                {sites.map(site => (
                  <Select.Option key={site.Value} value={site.Value}>
                    {site.Name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="BuildingName" label="建築名稱" rules={[{ required: true, message: '請輸入建築名稱' }]}>
              <Input />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="TotalAboveGroundFloors" label="樓上層數" rules={[{ required: true, message: '請輸入樓上層數' }]}>
              <InputNumber style={{ width: '100%' }} min={1} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="TotalBelowGroundFloors" label="樓下層數" rules={[{ required: true, message: '請輸入樓下層數' }]}>
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="BuildingType" label="建築類型">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="CompletionDate" label="完工日期">
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item name="Remarks" label="備註">
          <Input.TextArea rows={4} />
        </Form.Item>
      </Form>
    </Modal>
  );
} 