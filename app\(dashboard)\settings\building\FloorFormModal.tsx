import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, InputNumber, Select, App, Row, Col } from 'antd';
import type { FloorListItem, CreateFloorDto, UpdateFloorDto } from '@/app/interfaces/dto/floor.dto';
import type { DropdownItem } from '@/app/interfaces/dto/common.dto';
import { buildingApi } from '@/app/services/api/buildingApi';

interface FloorFormModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (data: CreateFloorDto | UpdateFloorDto) => void;
  floor: FloorListItem | null;
  sites: DropdownItem[];
}

export default function FloorFormModal({ open, onCancel, onOk, floor, sites }: FloorFormModalProps) {
  const { message } = App.useApp();
  const [form] = Form.useForm<CreateFloorDto | UpdateFloorDto>();
  const [selectedSiteCode, setSelectedSiteCode] = useState<string | undefined>(undefined);
  const [buildings, setBuildings] = useState<DropdownItem[]>([]);

  useEffect(() => {
    if (open) {
      if (floor) {
        form.setFieldsValue({
          BuildingId: floor.BuildingId,
          SiteCode: floor.SiteCode,
          FloorLabel: floor.FloorLabel,
          FloorLevel: floor.FloorLevel,
          FloorType: floor.FloorType,
          FloorHeight: floor.FloorHeight,
          Remarks: (floor as any).Remarks || ''
        });
        if (floor.SiteCode) {
          setSelectedSiteCode(floor.SiteCode);
        }
      } else {
        form.resetFields();
        setSelectedSiteCode(undefined);
        setBuildings([]);
      }
    } else {
      // Optionally clear states when modal is not open, if desired
      // setSelectedSiteCode(undefined);
      // setBuildings([]);
    }
  }, [open, floor, form]);

  useEffect(() => {
    if (open && selectedSiteCode) {
      buildingApi.getCommonDropdownList({ type: 'building', siteCode: selectedSiteCode })
        .then(res => {
          if (res.isSuccess && res.body) {
            setBuildings(res.body);
          } else {
            message.error(res.message || '載入建築下拉失敗');
            setBuildings([]);
          }
        })
        .catch(() => {
          message.error('載入建築下拉失敗');
          setBuildings([]);
        });
      form.setFieldsValue({ BuildingId: undefined });
    } else if (!selectedSiteCode) {
      setBuildings([]);
    }
  }, [open, selectedSiteCode, form, message]);

  const handleSiteChange = (value: string) => {
    setSelectedSiteCode(value);
  };

  const handleFinish = (values: any) => {
    onOk(values as CreateFloorDto | UpdateFloorDto);
  };

  return (
    <Modal
      title={floor ? '編輯樓層' : '新增樓層'}
      open={open}
      onCancel={onCancel}
      onOk={() => form.submit()}
      okText="確認"
      cancelText="取消"
      destroyOnClose
      width={800}
      styles={{ body: { maxHeight: 'calc(100vh - 300px)', overflowY: 'auto' } }}
    >
      <Form form={form} layout="vertical" onFinish={handleFinish}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="SiteCode" label="案場名稱" rules={[{ required: true, message: '請選擇案場' }]}>
              <Select onChange={setSelectedSiteCode} placeholder="請選擇案場" allowClear>
                {sites.map(site => <Select.Option key={site.Value} value={site.Value}>{site.Name}</Select.Option>)}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="BuildingId" label="建築名稱" rules={[{ required: true, message: '請選擇建築' }]}>
              <Select disabled={!selectedSiteCode || buildings.length === 0} placeholder="請選擇建築" allowClear>
                {buildings.map(b => <Select.Option key={b.Value} value={Number(b.Value)}>{b.Name}</Select.Option>)}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="FloorLabel" label="樓層名稱" rules={[{ required: true, message: '請輸入樓層名稱' }]}>
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="FloorLevel" label="樓層" rules={[{ required: true, message: '請輸入樓層' }]}>
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="FloorType" label="主要用途">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="FloorHeight" label="樓層高度 (m)">
              <InputNumber style={{ width: '100%' }} min={0} addonAfter="m" />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item name="Remarks" label="備註">
          <Input.TextArea rows={4} />
        </Form.Item>
      </Form>
    </Modal>
  );
} 