import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, App } from 'antd';
import type { 
  ParkingSpaceCreateInput, 
  ParkingSpaceUpdateInput, 
  ParkingSpaceListOutput 
} from '@/app/interfaces/dto/parkingSpace.dto';
import type { DropdownItem } from '@/app/interfaces/dto/common.dto';
import { buildingApi } from '@/app/services/api/buildingApi';

const { Option } = Select;

interface ParkingSpaceFormModalProps {
  open: boolean;
  parkingSpace: ParkingSpaceListOutput | null;
  sites: DropdownItem[];
  onCancel: () => void;
  onOk: (values: ParkingSpaceCreateInput | ParkingSpaceUpdateInput) => void;
}

const ParkingSpaceFormModal: React.FC<ParkingSpaceFormModalProps> = ({ 
  open, 
  parkingSpace, 
  sites, 
  onCancel, 
  onOk 
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [selectedSiteCode, setSelectedSiteCode] = useState<string | undefined>(undefined);
  const [buildings, setBuildings] = useState<DropdownItem[]>([]);
  const [selectedBuildingId, setSelectedBuildingId] = useState<number | undefined>(undefined);
  const [floors, setFloors] = useState<DropdownItem[]>([]);

  useEffect(() => {
    if (open) {
      if (parkingSpace) {
        // 將新格式的資料轉換為表單格式
        form.setFieldsValue({
          siteCode: (parkingSpace as any).SiteCode,
          buildingId: (parkingSpace as any).BuildingId,
          floorId: (parkingSpace as any).FloorId,
          spaceNumber: (parkingSpace as any).SpaceNumber,
          spaceType: (parkingSpace as any).SpaceType,
          dimensions: (parkingSpace as any).Dimensions,
          location: (parkingSpace as any).Location,
          remarks: (parkingSpace as any).Remarks,
        });
        setSelectedSiteCode((parkingSpace as any).SiteCode);
        setSelectedBuildingId((parkingSpace as any).BuildingId);
      } else {
        form.resetFields();
        setSelectedSiteCode(undefined);
        setBuildings([]);
        setSelectedBuildingId(undefined);
        setFloors([]);
      }
    }
  }, [parkingSpace, form, open]);

  useEffect(() => {
    if (selectedSiteCode) {
buildingApi.getCommonDropdownList({ type: "building", siteCode: selectedSiteCode })
        .then(res => {
          if (res.isSuccess && res.body) setBuildings(res.body);
          else setBuildings([]);
        })
        .catch(() => {
          message.error('載入建築下拉失敗');
          setBuildings([]);
        });
      form.setFieldsValue({ buildingId: undefined, floorId: undefined });
      setSelectedBuildingId(undefined);
      setFloors([]);
    } else {
      setBuildings([]);
    }
  }, [selectedSiteCode, message, form]);

  useEffect(() => {
    if (selectedBuildingId) {
buildingApi.getCommonDropdownList({ type: "floor", buildingId: selectedBuildingId })
        .then(res => {
          if (res.isSuccess && res.body) setFloors(res.body);
          else setFloors([]);
        })
        .catch(() => {
          message.error('載入樓層下拉失敗');
          setFloors([]);
        });
      form.setFieldsValue({ floorId: undefined });
    } else {
      setFloors([]);
    }
  }, [selectedBuildingId, message, form]);

  const handleOk = () => {
    form.validateFields()
      .then(values => {
        // 確保資料格式符合新API要求
        const formattedValues: ParkingSpaceCreateInput | ParkingSpaceUpdateInput = {
          siteCode: values.siteCode,
          buildingId: values.buildingId,
          floorId: values.floorId,
          spaceNumber: values.spaceNumber,
          spaceType: values.spaceType,
          dimensions: values.dimensions || undefined,
          location: values.location || undefined,
          remarks: values.remarks || undefined,
        };
        onOk(formattedValues);
      })
      .catch(info => {
        console.log('Validate Failed:', info);
      });
  };

  return (
    <Modal
      title={parkingSpace ? '編輯停車位' : '新增停車位'}
      open={open}
      onCancel={onCancel}
      onOk={handleOk}
      confirmLoading={false}
      destroyOnClose
      width={800}
      styles={{ body: { maxHeight: 'calc(100vh - 300px)', overflowY: 'auto' } }}
    >
      <Form form={form} layout="vertical" name="parkingSpaceForm">
        <Form.Item name="siteCode" label="案場代號" rules={[{ required: true, message: '請選擇案場代號' }]}>
          <Select placeholder="請選擇案場" onChange={setSelectedSiteCode} allowClear>
            {sites.map(site => <Option key={site.Value} value={site.Value}>{site.Name}</Option>)}
          </Select>
        </Form.Item>
        
        <Form.Item name="buildingId" label="建築編號" rules={[{ required: true, message: '請選擇建築' }]}>
          <Select 
            placeholder="請選擇建築" 
            onChange={setSelectedBuildingId} 
            disabled={!selectedSiteCode || buildings.length === 0} 
            allowClear
          >
            {buildings.map(b => <Option key={b.Value} value={Number(b.Value)}>{b.Name}</Option>)}
          </Select>
        </Form.Item>
        
        <Form.Item name="floorId" label="樓層編號" rules={[{ required: true, message: '請選擇樓層' }]}>
          <Select 
            placeholder="請選擇樓層" 
            disabled={!selectedBuildingId || floors.length === 0} 
            allowClear
          >
            {floors.map(f => <Option key={f.Value} value={Number(f.Value)}>{f.Name}</Option>)}
          </Select>
        </Form.Item>
        
        <Form.Item name="spaceNumber" label="車位號碼" rules={[{ required: true, message: '請輸入車位號碼' }]}>
          <Input placeholder="例如：P001" />
        </Form.Item>
        
        <Form.Item name="spaceType" label="車位類型" rules={[{ required: true, message: '請輸入車位類型' }]}>
          <Select placeholder="請選擇車位類型">
            <Option value="坡道平面">坡道平面</Option>
            <Option value="機械">機械</Option>
            <Option value="平面">平面</Option>
            <Option value="其他">其他</Option>
          </Select>
        </Form.Item>
        
        <Form.Item name="dimensions" label="車位尺寸">
          <Input placeholder="例如：250x550cm" />
        </Form.Item>
        
        <Form.Item name="location" label="位置描述">
          <Input placeholder="例如：靠近電梯" />
        </Form.Item>
        
        <Form.Item name="remarks" label="備註">
          <Input.TextArea rows={3} placeholder="其他說明..." />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ParkingSpaceFormModal; 