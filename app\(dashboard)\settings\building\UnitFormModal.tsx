import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, InputNumber, Select, App, Row, Col } from 'antd';
import type { UnitListItemDto, CreateUnitDto, UpdateUnitDto } from '@/app/interfaces/dto/unit.dto';
import type { DropdownItem } from '@/app/interfaces/dto/common.dto';
import { buildingApi } from '@/app/services/api/buildingApi';

const { Option } = Select;

const statusOptions = [
  { value: '可售', label: '可售', color: '#fff' },
  { value: '保留', label: '保留', color: '#e0e0e0' },
  { value: '售', label: '售（訂金未付足）', color: '#e1f5fe' },
  { value: '足', label: '足（補足訂金）', color: '#fff9c4' },
  { value: '簽', label: '簽（已簽合約）', color: '#fce4ec' }
];

interface UnitFormModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (data: CreateUnitDto | UpdateUnitDto) => void;
  unit: UnitListItemDto | null;
  sites: DropdownItem[];
}

export default function UnitFormModal({ open, onCancel, onOk, unit, sites }: UnitFormModalProps) {
  const { message } = App.useApp();
  const [form] = Form.useForm<CreateUnitDto | UpdateUnitDto>();
  const [selectedSiteCode, setSelectedSiteCode] = useState<string | undefined>(undefined);
  const [buildings, setBuildings] = useState<DropdownItem[]>([]);
  const [selectedBuildingId, setSelectedBuildingId] = useState<number | undefined>(undefined);
  const [floors, setFloors] = useState<DropdownItem[]>([]);

  useEffect(() => {
    if (open) {
      if (unit) {
        form.setFieldsValue({
          ...unit,
          // Ensure number values are set correctly
          FloorId: unit.FloorId,
          BuildingId: unit.BuildingId,
        });
        if (unit.SiteCode) setSelectedSiteCode(unit.SiteCode);
        if (unit.BuildingId) setSelectedBuildingId(unit.BuildingId);
      } else {
        form.resetFields();
        setSelectedSiteCode(undefined);
        setBuildings([]);
        setSelectedBuildingId(undefined);
        setFloors([]);
      }
    }
  }, [open, unit, form]);

  useEffect(() => {
    if (open && selectedSiteCode) {
      const isManualChange = !unit || unit.SiteCode !== selectedSiteCode;
      buildingApi.getCommonDropdownList({ type: 'building', siteCode: selectedSiteCode })
        .then(res => setBuildings(res.isSuccess && res.body ? res.body : []));
      
      if (isManualChange) {
        form.setFieldsValue({ BuildingId: undefined, FloorId: undefined });
        setSelectedBuildingId(undefined);
        setFloors([]);
      }
    } else if (!open) {
      setBuildings([]);
    }
  }, [open, selectedSiteCode, form, unit]);

  useEffect(() => {
    if (open && selectedBuildingId) {
      const isManualChange = !unit || unit.BuildingId !== selectedBuildingId;
      buildingApi.getCommonDropdownList({ type: 'floor', buildingId: selectedBuildingId })
        .then(res => {
          if (res.isSuccess && res.body) {
            setFloors(res.body);
          } else {
            setFloors([]);
          }
        });
      
      if (isManualChange) {
        form.setFieldsValue({ FloorId: undefined });
      }
    } else if (!open) {
      setFloors([]);
    }
  }, [open, selectedBuildingId, form, unit]);

  const handleFinish = (values: any) => {
    onOk(values as CreateUnitDto | UpdateUnitDto);
  };

  return (
    <Modal
      title={unit ? '編輯房屋' : '新增房屋'}
      open={open}
      onCancel={onCancel}
      onOk={() => form.submit()}
      okText="確認"
      cancelText="取消"
      destroyOnClose
      width={800}
      styles={{ body: { maxHeight: 'calc(100vh - 300px)', overflowY: 'auto' } }}
    >
      <Form form={form} layout="vertical" onFinish={handleFinish}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="SiteCode" label="案場名稱" rules={[{ required: true, message: '請選擇案場' }]}>
              <Select onChange={setSelectedSiteCode} placeholder="請選擇案場" allowClear>
                {sites.map(site => <Option key={site.Value} value={site.Value}>{site.Name}</Option>)}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="BuildingId" label="建築名稱" rules={[{ required: true, message: '請選擇建築' }]}>
              <Select 
                onChange={setSelectedBuildingId} 
                disabled={!selectedSiteCode || buildings.length === 0} 
                placeholder="請選擇建築" 
                allowClear
              >
                {buildings.map(b => <Option key={b.Value} value={Number(b.Value)}>{b.Name}</Option>)}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="FloorId" label="樓層" rules={[{ required: true, message: '請選擇樓層' }]}>
              <Select 
                disabled={!selectedBuildingId || floors.length === 0} 
                placeholder="請選擇樓層" 
                allowClear
              >
                {floors.map(f => <Option key={f.Value} value={Number(f.Value)}>{f.Name}</Option>)}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="Layout" label="格局" rules={[{ required: true, message: '請輸入格局' }]}>
              <Input />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="Orientation" label="座向">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="MainArea" label="主建物面積 (坪)">
              <InputNumber style={{ width: '100%' }} min={0} addonAfter="坪" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="SmallPublicArea" label="小公面積 (坪)">
              <InputNumber style={{ width: '100%' }} min={0} addonAfter="坪" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="LargePublicArea" label="大公面積 (坪)">
              <InputNumber style={{ width: '100%' }} min={0} addonAfter="坪" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="AwningArea" label="雨遮面積 (坪)">
              <InputNumber style={{ width: '100%' }} min={0} addonAfter="坪" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="BalconyArea" label="陽台面積 (坪)">
              <InputNumber style={{ width: '100%' }} min={0} addonAfter="坪" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="TotalArea" label="總面積 (坪)">
              <InputNumber style={{ width: '100%' }} min={0} addonAfter="坪" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="ListPrice" label="表價">
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="Status" label="狀態" rules={[{ required: true, message: '請選擇狀態' }]}>
              <Select placeholder="請選擇狀態">
                {statusOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    <div className="flex items-center">
                      <div 
                        className="w-4 h-4 rounded mr-2 border"
                        style={{ backgroundColor: option.color }}
                      />
                      {option.label}
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Form.Item name="Remarks" label="備註">
          <Input.TextArea rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
}
