'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { App, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import DataTable from '@/app/components/DataTable';
import BuildingFormModal from '@/app/(dashboard)/settings/building/BuildingFormModal';
import { buildingApi } from '@/app/services/api/buildingApi';
import { siteApi } from '@/app/services/api/siteApi';
import type { BuildingListItem, CreateBuildingDto, UpdateBuildingDto, BuildingQueryDto } from '@/app/interfaces/dto/building.dto';
import type { BaseQueryParams, SearchTermInfo, DropdownItem } from '@/app/interfaces/dto/common.dto';
import type { SorterResult } from 'antd/es/table/interface';
import FloorFormModal from '@/app/(dashboard)/settings/building/FloorFormModal';
import { floorApi } from '@/app/services/api/floorApi';
import type { FloorListItem, CreateFloorDto, UpdateFloorDto, FloorQueryDto } from '@/app/interfaces/dto/floor.dto';
import { parkingSpaceApi } from '@/app/services/api/parkingSpaceApi';
import type { 
  ParkingSpaceListOutput, 
  ParkingSpaceCreateInput, 
  ParkingSpaceUpdateInput, 
  ParkingSpaceQueryInput,
  PagedListOutput,
  // 舊介面用於向後相容
  ParkingSpaceListItemDto, 
  CreateParkingSpaceDto, 
  UpdateParkingSpaceDto, 
  ParkingSpaceQueryDto, 
  ParkingSpaceListPagedResult 
} from '@/app/interfaces/dto/parkingSpace.dto';
import ParkingSpaceFormModal from '@/app/(dashboard)/settings/building/ParkingSpaceFormModal';
import { unitApi } from '@/app/services/api/unitApi';
import type {
  UnitListItemDto,
  CreateUnitDto,
  UpdateUnitDto,
  UnitQueryDto
} from '@/app/interfaces/dto/unit.dto';
import UnitFormModal from './UnitFormModal';

export default function BuildingSettingsPage() {
  const { message: messageApi } = App.useApp();
  const [buildings, setBuildings] = useState<BuildingListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'ascend'|'descend'|null>(null);
  const [searchInfos, setSearchInfos] = useState<SearchTermInfo[]|undefined>(undefined);

  const [sites, setSites] = useState<DropdownItem[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingBuilding, setEditingBuilding] = useState<BuildingListItem|null>(null);

  const [floors, setFloors] = useState<FloorListItem[]>([]);
  const [floorLoading, setFloorLoading] = useState(false);
  const [floorTotal, setFloorTotal] = useState(0);
  const [floorPage, setFloorPage] = useState(1);
  const [floorSize, setFloorSize] = useState(10);
  const [floorSortField, setFloorSortField] = useState<string | null>(null);
  const [floorSortOrder, setFloorSortOrder] = useState<'ascend'|'descend'|null>(null);
  const [floorSearchInfos, setFloorSearchInfos] = useState<SearchTermInfo[]|undefined>(undefined);
  const [isFloorModalOpen, setIsFloorModalOpen] = useState(false);
  const [editingFloor, setEditingFloor] = useState<FloorListItem|null>(null);

  const [units, setUnits] = useState<UnitListItemDto[]>([]);
  const [unitLoading, setUnitLoading] = useState(false);
  const [unitTotal, setUnitTotal] = useState(0);
  const [unitPage, setUnitPage] = useState(1);
  const [unitSize, setUnitSize] = useState(10);
  const [unitSortField, setUnitSortField] = useState<string | null>(null);
  const [unitSortOrder, setUnitSortOrder] = useState<'ascend'|'descend'|null>(null);
  const [unitSearchInfos, setUnitSearchInfos] = useState<SearchTermInfo[]|undefined>(undefined);
  const [isUnitModalOpen, setIsUnitModalOpen] = useState(false);
  const [editingUnit, setEditingUnit] = useState<UnitListItemDto|null>(null);

  const [parkingSpaces, setParkingSpaces] = useState<ParkingSpaceListOutput[]>([]);
  const [parkingSpaceLoading, setParkingSpaceLoading] = useState(false);
  const [parkingSpaceTotal, setParkingSpaceTotal] = useState(0);
  const [parkingSpaceCurrentPage, setParkingSpaceCurrentPage] = useState(1);
  const [parkingSpacePageSize, setParkingSpacePageSize] = useState(10);
  const [parkingSpaceSortField, setParkingSpaceSortField] = useState<string | null>(null);
  const [parkingSpaceSortOrder, setParkingSpaceSortOrder] = useState<'ascend'|'descend'|null>(null);
  const [parkingSpaceSearchInfos, setParkingSpaceSearchInfos] = useState<SearchTermInfo[]|undefined>(undefined);
  const [isParkingSpaceModalOpen, setIsParkingSpaceModalOpen] = useState(false);
  const [editingParkingSpace, setEditingParkingSpace] = useState<ParkingSpaceListOutput|null>(null);

  const fetchSites = useCallback(async () => {
    try {
      const res = await siteApi.getSiteDropdownList();
      if (res.isSuccess && res.body) setSites(res.body);
    } catch {
      messageApi.error('載入案場下拉失敗');
    }
  }, [messageApi]);

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const params: BuildingQueryDto & BaseQueryParams = {
        UsingPaging: true,
        PageIndex: currentPage,
        NumberOfPperPage: pageSize,
        SortOrderInfos: sortField && sortOrder ? [{ SortField: sortField, SortOrder: sortOrder === 'ascend' ? 'asc' : 'desc' }] : undefined,
        SearchTermInfos: searchInfos,
        SiteCode: undefined,
        BuildingName: undefined,
        BuildingType: undefined
      };
      const response = await buildingApi.getBuildings(params);
      if (response.isSuccess && response.body) {
        setBuildings(response.body.Detail);
        setTotal(response.body.RecordCount);
        setCurrentPage(response.body.PageIndex);
        setPageSize(response.body.NumberOfPperPage);
      } else {
        messageApi.error(response.message || '獲取建築列表失敗');
      }
    } catch {
      messageApi.error('獲取建築列表失敗');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, sortField, sortOrder, searchInfos, messageApi]);

  const fetchFloors = useCallback(async () => {
    setFloorLoading(true);
    try {
      const params: FloorQueryDto & BaseQueryParams = {
        UsingPaging: true,
        PageIndex: floorPage,
        NumberOfPperPage: floorSize,
        SortOrderInfos: floorSortField && floorSortOrder ? [{ SortField: floorSortField, SortOrder: floorSortOrder==='ascend'?'asc':'desc' }] : undefined,
        SearchTermInfos: floorSearchInfos,
        BuildingId: undefined,
        FloorType: undefined
      };
      const res = await floorApi.getFloors(params);
      if(res.isSuccess && res.body) {
        setFloors(res.body.Detail);
        setFloorTotal(res.body.RecordCount);
      }
    } catch {
    } finally { setFloorLoading(false); }
  }, [floorPage, floorSize, floorSortField, floorSortOrder, floorSearchInfos]);

  const fetchUnits = useCallback(async () => {
    setUnitLoading(true);
    try {
      const params: UnitQueryDto = {
        UsingPaging: true,
        PageIndex: unitPage,
        NumberOfPperPage: unitSize,
        SortOrderInfos: unitSortField && unitSortOrder ? [{ SortField: unitSortField, SortOrder: unitSortOrder === 'ascend' ? 'asc' : 'desc' }] : undefined,
        SearchTermInfos: unitSearchInfos,
      };
      const res = await unitApi.getUnits(params);
      if (res.isSuccess && res.body) {
        setUnits(res.body.Detail);
        // 如果後端回傳 RecordCount 為 0 但實際有資料，暫時用資料長度當總數，以利顯示
        const total = res.body.RecordCount > 0 ? res.body.RecordCount : res.body.Detail.length;
        setUnitTotal(total);
      } else {
        messageApi.error(res.message || '獲取房屋列表失敗');
        setUnits([]);
        setUnitTotal(0);
      }
    } catch (err) {
      console.error("fetchUnits error:", err);
      messageApi.error('獲取房屋列表失敗');
      setUnits([]);
      setUnitTotal(0);
    } finally { setUnitLoading(false); }
  }, [unitPage, unitSize, unitSortField, unitSortOrder, unitSearchInfos, messageApi]);

  const fetchParkingSpaces = useCallback(async () => {
    setParkingSpaceLoading(true);
    try {
      const params: ParkingSpaceQueryInput = {
        usingPaging: true,
        pageIndex: parkingSpaceCurrentPage,
        numberOfPerPage: parkingSpacePageSize,
        sortOrderInfos: parkingSpaceSortField && parkingSpaceSortOrder ? [{ 
          SortField: parkingSpaceSortField, 
          SortOrder: parkingSpaceSortOrder === 'ascend' ? 'asc' : 'desc' 
        }] : undefined,
        searchTermInfos: parkingSpaceSearchInfos,
      };
      const response = await parkingSpaceApi.getParkingSpaceList(params);
      if (response.isSuccess && response.body) {
        setParkingSpaces((response.body as any).Detail || []);
        setParkingSpaceTotal((response.body as any).RecordCount || 0);
      } else {
        messageApi.error(response.message || '獲取停車位列表失敗');
        setParkingSpaces([]);
        setParkingSpaceTotal(0);
      }
    } catch (error) {
      console.error("fetchParkingSpaces error:", error);
      messageApi.error('獲取停車位列表失敗');
      // 設置空陣列避免顯示問題
      setParkingSpaces([]);
      setParkingSpaceTotal(0);
    } finally {
      setParkingSpaceLoading(false);
    }
  }, [parkingSpaceCurrentPage, parkingSpacePageSize, parkingSpaceSortField, parkingSpaceSortOrder, parkingSpaceSearchInfos, messageApi]);

  useEffect(() => {
    fetchSites();
  }, [fetchSites]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchFloors();
  }, [fetchFloors]);

  useEffect(() => {
    fetchUnits();
  }, [fetchUnits]);

  useEffect(() => {
    fetchParkingSpaces();
  }, [fetchParkingSpaces]);

  const handleAdd = () => { setEditingBuilding(null); setIsModalOpen(true); };
  const handleEdit = (record: BuildingListItem) => { setEditingBuilding(record); setIsModalOpen(true); };
  const handleDelete = async (record: BuildingListItem) => {
    setLoading(true);
    try {
      await buildingApi.deleteBuilding(record.BuildingId);
      messageApi.success('刪除建築成功');
      fetchData();
    } catch {
      messageApi.error('刪除建築失敗');
    } finally { setLoading(false); }
  };

  const handleFormSubmit = async (data: CreateBuildingDto|UpdateBuildingDto) => {
    setLoading(true);
    try {
      if (editingBuilding) {
        await buildingApi.updateBuilding(editingBuilding.BuildingId, data as UpdateBuildingDto);
        messageApi.success('更新建築成功');
      } else {
        await buildingApi.createBuilding(data as CreateBuildingDto);
        messageApi.success('新增建築成功');
      }
      setIsModalOpen(false);
      fetchData();
    } catch {
      messageApi.error('保存建築失敗');
    } finally { setLoading(false); }
  };

  const handleFloorFormSubmit = async (data: CreateFloorDto | UpdateFloorDto) => {
    setFloorLoading(true);
    try {
      if (editingFloor) {
        await floorApi.updateFloor(editingFloor.FloorId, data as UpdateFloorDto);
        messageApi.success('更新樓層成功');
      } else {
        await floorApi.createFloor(data as CreateFloorDto);
        messageApi.success('新增樓層成功');
      }
      setIsFloorModalOpen(false);
      fetchFloors();
    } catch {
      messageApi.error('保存樓層失敗');
    } finally {
      setFloorLoading(false);
    }
  };

  const handleUnitFormSubmit = async (data: CreateUnitDto | UpdateUnitDto) => {
    setUnitLoading(true);
    try {
      if (editingUnit) {
        await unitApi.updateUnit(editingUnit.UnitId, data as UpdateUnitDto);
        messageApi.success('更新房屋成功');
      } else {
        await unitApi.createUnit(data as CreateUnitDto);
        messageApi.success('新增房屋成功');
      }
      setIsUnitModalOpen(false);
      fetchUnits();
    } catch {
      messageApi.error('保存房屋失敗');
    } finally {
      setUnitLoading(false);
    }
  };

  const handleAddParkingSpace = () => {
    setEditingParkingSpace(null);
    setIsParkingSpaceModalOpen(true);
  };

  const handleEditParkingSpace = (record: ParkingSpaceListOutput) => {
    setEditingParkingSpace(record);
    setIsParkingSpaceModalOpen(true);
  };

  const handleDeleteParkingSpace = async (record: ParkingSpaceListOutput) => {
    setParkingSpaceLoading(true);
    try {
      await parkingSpaceApi.deleteParkingSpaceNew((record as any).ParkingSpaceId);
      messageApi.success('刪除停車位成功');
      fetchParkingSpaces();
    } catch (error) {
      console.error("handleDeleteParkingSpace error:", error);
      messageApi.error('刪除停車位失敗');
    } finally {
      setParkingSpaceLoading(false);
    }
  };

  const handleParkingSpaceFormSubmit = async (data: ParkingSpaceCreateInput | ParkingSpaceUpdateInput) => {
    setParkingSpaceLoading(true);
    try {
      if (editingParkingSpace) {
        await parkingSpaceApi.updateParkingSpaceNew((editingParkingSpace as any).ParkingSpaceId, data as ParkingSpaceUpdateInput);
        messageApi.success('更新停車位成功');
      } else {
        await parkingSpaceApi.createParkingSpaceNew(data as ParkingSpaceCreateInput);
        messageApi.success('新增停車位成功');
      }
      setIsParkingSpaceModalOpen(false);
      fetchParkingSpaces();
    } catch (error) {
      console.error("handleParkingSpaceFormSubmit error:", error);
      messageApi.error('保存停車位失敗');
    } finally {
      setParkingSpaceLoading(false);
    }
  };

  const buildingColumns = [
    { title: '建築ID', dataIndex: 'BuildingId', key: 'BuildingId', sorter: true, allowSearch: true },
    { title: '案場代號', dataIndex: 'SiteCode', key: 'SiteCode', sorter: true, allowSearch: true },
    { title: '案場名稱', dataIndex: 'SiteName', key: 'SiteName', sorter: true, allowSearch: true },
    { title: '建築名稱', dataIndex: 'BuildingName', key: 'BuildingName', sorter: true, allowSearch: true },
    { title: '樓上層數', dataIndex: 'TotalAboveGroundFloors', key: 'TotalAboveGroundFloors', sorter: true },
    { title: '樓下層數', dataIndex: 'TotalBelowGroundFloors', key: 'TotalBelowGroundFloors', sorter: true },
    { title: '建築類型', dataIndex: 'BuildingType', key: 'BuildingType', sorter: true, allowSearch: true },
    { title: '完工日期', dataIndex: 'CompletionDate', key: 'CompletionDate', sorter: true },
    { title: '建立時間', dataIndex: 'CreatedTime', key: 'CreatedTime', sorter: true },
    {
      title: '操作', key: 'action', render: (_: any, record: BuildingListItem) => (
        <>
          <Button type="link" onClick={() => handleEdit(record)}>編輯</Button>
          <Popconfirm title="確定刪除？" onConfirm={() => handleDelete(record)} okText="確定" cancelText="取消">
            <Button type="link" danger>刪除</Button>
          </Popconfirm>
        </>
      ), sorter: false, allowSearch: false
    }
  ];

  const floorColumns = [
    { title: '樓層ID', dataIndex: 'FloorId', key: 'FloorId', sorter: true, allowSearch: true },
    { title: '案場編號', dataIndex: 'SiteCode', key: 'SiteCode', sorter: true, allowSearch: true },
    { title: '案場名稱', dataIndex: 'SiteName', key: 'SiteName', sorter: true, allowSearch: true },
    { title: '樓層名稱', dataIndex: 'FloorLabel', key: 'FloorLabel', sorter: true, allowSearch: true },
    { title: '樓層', dataIndex: 'FloorLevel', key: 'FloorLevel', sorter: true },
    { title: '主要用途', dataIndex: 'FloorType', key: 'FloorType', sorter: true, allowSearch: true },
    { title: '高度', dataIndex: 'FloorHeight', key: 'FloorHeight', sorter: true },
    { title: '備註', dataIndex: 'Remarks', key: 'Remarks', sorter: true },
    { title: '操作', key: 'action', render: (_, r) => (
      <>
        <Button type="link" onClick={() => { setEditingFloor(r); setIsFloorModalOpen(true); }}>編輯</Button>
        <Popconfirm title="確定要刪除此樓層嗎？" onConfirm={() => { floorApi.deleteFloor(r.FloorId).then(fetchFloors); }} okText="確定" cancelText="取消">
          <Button type="link" danger>刪除</Button>
        </Popconfirm>
      </>
    ), sorter: false, allowSearch: false }
  ];

  const unitColumns = [
    { 
      title: '案場', 
      dataIndex: 'SiteCode', 
      key: 'SiteCode', 
      sorter: true, 
      allowSearch: true,
      render: (siteCode: string) => sites.find(s => s.Value === siteCode)?.Name || siteCode 
    },
    { title: '建築', dataIndex: 'BuildingName', key: 'BuildingName', sorter: true, allowSearch: true },
    { title: '樓層', dataIndex: 'FloorLabel', key: 'FloorLabel', sorter: true, allowSearch: true },
    { title: '格局', dataIndex: 'Layout', key: 'Layout', sorter: true, allowSearch: true },
    { title: '小公面積', dataIndex: 'SmallPublicArea', key: 'SmallPublicArea', sorter: true },
    { title: '大公面積', dataIndex: 'LargePublicArea', key: 'LargePublicArea', sorter: true },
    { title: '雨遮面積', dataIndex: 'AwningArea', key: 'AwningArea', sorter: true },
    { title: '陽台面積', dataIndex: 'BalconyArea', key: 'BalconyArea', sorter: true },
    { title: '狀態', dataIndex: 'Status', key: 'Status', sorter: true, allowSearch: true },
    { title: '操作', key: 'action', render: (_:any, record: UnitListItemDto) => (
      <Space>
        <Button type="link" onClick={() => { setEditingUnit(record); setIsUnitModalOpen(true); }}>編輯</Button>
        <Popconfirm title="確定刪除?" onConfirm={async () => {
          setUnitLoading(true);
          try {
            await unitApi.deleteUnit(record.UnitId);
            messageApi.success('刪除房屋成功');
            fetchUnits();
          } catch { messageApi.error('刪除房屋失敗'); }
          finally { setUnitLoading(false); }
        }}>
          <Button type="link" danger>刪除</Button>
        </Popconfirm>
      </Space>
    )}
  ];

  const parkingSpaceColumns = [
    { title: '車位ID', dataIndex: 'ParkingSpaceId', key: 'ParkingSpaceId', sorter: true, allowSearch: true },
    { title: '案場代號', dataIndex: 'SiteCode', key: 'SiteCode', sorter: true, allowSearch: true },
    { title: '案場名稱', dataIndex: 'SiteName', key: 'SiteName', sorter: true, allowSearch: true },
    { title: '建築名稱', dataIndex: 'BuildingName', key: 'BuildingName', sorter: true, allowSearch: true, render: (text, record) => (record as any).BuildingName || (record as any).BuildingId },
    { title: '樓層標籤', dataIndex: 'FloorLabel', key: 'FloorLabel', sorter: true, allowSearch: true, render: (text, record) => (record as any).FloorLabel || (record as any).FloorId },
    { title: '車位號碼', dataIndex: 'SpaceNumber', key: 'SpaceNumber', sorter: true, allowSearch: true },
    { title: '車位類型', dataIndex: 'SpaceType', key: 'SpaceType', sorter: true, allowSearch: true },
    { title: '車位尺寸', dataIndex: 'Dimensions', key: 'Dimensions', sorter: true, allowSearch: true },
    { title: '位置描述', dataIndex: 'Location', key: 'Location', sorter: true, allowSearch: true },
    { title: '備註', dataIndex: 'Remarks', key: 'Remarks', sorter: true, allowSearch: true },
    { title: '建立時間', dataIndex: 'CreatedTime', key: 'CreatedTime', sorter: true, render: (val) => new Date(val).toLocaleString() },
    {
      title: '操作', key: 'action', render: (_: any, record: ParkingSpaceListOutput) => (
        <>
          <Button type="link" onClick={() => handleEditParkingSpace(record)}>編輯</Button>
          <Popconfirm title="確定刪除此車位？" onConfirm={() => handleDeleteParkingSpace(record)} okText="確定" cancelText="取消">
            <Button type="link" danger>刪除</Button>
          </Popconfirm>
        </>
      ), sorter: false, allowSearch: false
    }
  ];

  const tabItems = [
    {
      key: 'buildings',
      label: '建築管理',
      children: (
        <>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd} style={{ marginBottom: 16 }}>
            新增建築
          </Button>
          <DataTable
            columns={buildingColumns}
            dataSource={buildings}
            loading={loading}
            total={total}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={(page, size) => { setCurrentPage(page); setPageSize(size); }}
            onSort={(sorter) => {
              const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
              setSortField(currentSorter.field as string);
              setSortOrder(currentSorter.order);
            }}
            onFilter={(filters) => setSearchInfos(filters.map(f => ({ SearchField: f.SearchField, SearchValue: f.SearchValue })))}
            rowKey="BuildingId"
          />
        </>
      ),
    },
    {
      key: 'floors',
      label: '樓層管理',
      children: (
        <>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => { setEditingFloor(null); setIsFloorModalOpen(true); }} style={{ marginBottom: 16 }}>
            新增樓層
          </Button>
          <DataTable
            columns={floorColumns}
            dataSource={floors}
            loading={floorLoading}
            total={floorTotal}
            currentPage={floorPage}
            pageSize={floorSize}
            onPageChange={(page, size) => { setFloorPage(page); setFloorSize(size); }}
            onSort={(sorter) => {
              const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
              setFloorSortField(currentSorter.field as string);
              setFloorSortOrder(currentSorter.order);
            }}
            onFilter={(filters) => setFloorSearchInfos(filters.map(f => ({ SearchField: f.SearchField, SearchValue: f.SearchValue })))}
            rowKey="FloorId"
          />
        </>
      ),
    },
    {
      key: 'units',
      label: '房屋管理',
      children: (
        <>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => { setEditingUnit(null); setIsUnitModalOpen(true); }} style={{ marginBottom: 16 }}>
            新增房屋
          </Button>
          <DataTable
            columns={unitColumns}
            dataSource={units}
            loading={unitLoading}
            total={unitTotal}
            currentPage={unitPage}
            pageSize={unitSize}
            onPageChange={(page, size) => { setUnitPage(page); setUnitSize(size); }}
            onSort={(sorter) => {
              const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
              setUnitSortField(currentSorter.field as string);
              setUnitSortOrder(currentSorter.order);
            }}
            onFilter={(filters) => setUnitSearchInfos(filters.map(f => ({ SearchField: f.SearchField, SearchValue: f.SearchValue })))}
            rowKey="UnitId"
          />
        </>
      ),
    },
    {
      key: 'parking',
      label: '車位管理',
      children: (
        <>
          <div className="mb-4">
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddParkingSpace}>新增車位</Button>
          </div>
          <DataTable
            columns={parkingSpaceColumns}
            dataSource={parkingSpaces}
            loading={parkingSpaceLoading}
            total={parkingSpaceTotal}
            currentPage={parkingSpaceCurrentPage}
            pageSize={parkingSpacePageSize}
            onPageChange={(page, size) => { setParkingSpaceCurrentPage(page); setParkingSpacePageSize(size); }}
            onSort={(sorter: SorterResult<ParkingSpaceListOutput>) => {
              const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
              const field = currentSorter.field as string | null;
              const order = currentSorter.order as ('ascend'|'descend') | null;
              if (parkingSpaceSortField !== field || parkingSpaceSortOrder !== order) {
                setParkingSpaceSortField(field);
                setParkingSpaceSortOrder(order);
                if (parkingSpaceCurrentPage !== 1) setParkingSpaceCurrentPage(1);
              }
            }}
            onFilter={(infos: SearchTermInfo[]) => { 
              const newSearchInfos = infos.length ? infos : undefined;
              if (JSON.stringify(newSearchInfos) !== JSON.stringify(parkingSpaceSearchInfos)) {
                setParkingSpaceSearchInfos(newSearchInfos);
                if (parkingSpaceCurrentPage !== 1) setParkingSpaceCurrentPage(1);
              }
            }}
            rowKey="ParkingSpaceId"
          />
        </>
      )
    }
  ];

  return (
    <div className="p-4">
      <div className="bg-white rounded-lg shadow p-6">
        <Tabs
          defaultActiveKey="buildings"
          items={tabItems}
        />
      </div>
      <BuildingFormModal
        open={isModalOpen}
        building={editingBuilding}
        sites={sites}
        onCancel={() => setIsModalOpen(false)}
        onOk={handleFormSubmit}
      />
      <FloorFormModal
        open={isFloorModalOpen}
        floor={editingFloor}
        sites={sites}
        onCancel={() => setIsFloorModalOpen(false)}
        onOk={handleFloorFormSubmit}
      />
      <UnitFormModal
        open={isUnitModalOpen}
        onCancel={() => setIsUnitModalOpen(false)}
        onOk={handleUnitFormSubmit}
        unit={editingUnit}
        sites={sites}
      />
      <ParkingSpaceFormModal
        open={isParkingSpaceModalOpen}
        parkingSpace={editingParkingSpace}
        sites={sites}
        onCancel={() => setIsParkingSpaceModalOpen(false)}
        onOk={handleParkingSpaceFormSubmit}
      />
    </div>
  );
}
