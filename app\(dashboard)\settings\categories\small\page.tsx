'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Form, Input, InputNumber, Switch, Button, Modal, Popconfirm, Space, message, Select } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import DataTable from '../../../../components/DataTable';
import BackButton from '../../../../components/common/BackButton';
import { categoryApi } from '../../../../services/api/categoryApi';
import {
  SmallCategoryListItem,
  SmallCategoryQueryDto,
  CreateSmallCategoryDto,
  UpdateSmallCategoryDto,
  SmallCategoryDetail
} from '../../../../interfaces/dto/category.dto';
import { SearchTermInfo } from '../../../../interfaces/dto/common.dto';
import { SorterResult } from 'antd/es/table/interface';

const { Option } = Select;

export default function SmallCategoriesPage() {
  const [categories, setCategories] = useState<SmallCategoryListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'add' | 'edit'>('add');
  const [editingCategory, setEditingCategory] = useState<SmallCategoryDetail | null>(null);
  const [messageApi, contextHolder] = message.useMessage();
  const [isClient, setIsClient] = useState(false);

  // 分頁和排序狀態
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(null);
  const [searchInfos, setSearchInfos] = useState<SearchTermInfo[] | undefined>(undefined);
  const hasInitialized = useRef(false);

  // 預設新增表單值
  const defaultAddFormValues: Partial<CreateSmallCategoryDto> = {
    MediumCategoryId: undefined,
    Name: '',
    Description: '',
    SortOrder: 1,
    IsActive: true,
  };

  // 獲取資料
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const apiParams: SmallCategoryQueryDto = {
        UsingPaging: true,
        PageIndex: currentPage,
        NumberOfPperPage: pageSize,
        SortOrderInfos: sortField && sortOrder
          ? [{ SortField: sortField, SortOrder: sortOrder === 'ascend' ? 'asc' : 'desc' }]
          : undefined,
        SearchTermInfos: searchInfos,
      };

      const response = await categoryApi.getSmallCategories(apiParams);
      
      // 檢查回應結構 - API 回應可能包裝在 body 屬性中
      const actualData = (response as any).body || response;
      const dataArray = actualData.Detail || actualData.Details || [];
      
      setCategories(dataArray);
      setTotalCount(actualData.RecordCount || actualData.TotalCount || dataArray.length);
    } catch (error: any) {
      messageApi.error(error.message || '獲取小分類列表失敗');
      setCategories([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, sortField, sortOrder, searchInfos]);

  useEffect(() => {
    if (!hasInitialized.current) {
      hasInitialized.current = true;
      fetchData();
    }
  }, [fetchData]);

  // 客戶端渲染檢查，避免 hydration 錯誤
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 表格欄位定義
  const columns = [
    { 
      title: '大分類', 
      dataIndex: 'LargeCategoryName', 
      key: 'LargeCategoryName', 
      allowSearch: true 
    },
    { 
      title: '中分類', 
      dataIndex: 'MediumCategoryName', 
      key: 'MediumCategoryName', 
      allowSearch: true 
    },
    { 
      title: '小分類名稱', 
      dataIndex: 'Name', 
      key: 'Name', 
      sorter: true, 
      allowSearch: true 
    },
    { 
      title: '描述', 
      dataIndex: 'Description', 
      key: 'Description', 
      allowSearch: true,
      render: (text: string) => text || '-'
    },
    { 
      title: '排序', 
      dataIndex: 'SortOrder', 
      key: 'SortOrder', 
      sorter: true,
      width: 100
    },
    { 
      title: '狀態', 
      dataIndex: 'IsActive', 
      key: 'IsActive',
      width: 100,
      render: (isActive: boolean) => (
        <span className={isActive ? 'text-green-600' : 'text-red-600'}>
          {isActive ? '啟用' : '停用'}
        </span>
      )
    },
    { 
      title: '建立時間', 
      dataIndex: 'CreateTime', 
      key: 'CreateTime', 
      sorter: true,
      render: (text: string) => text ? new Date(text).toLocaleDateString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: SmallCategoryListItem) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定刪除此小分類嗎？"
            description="刪除後將無法復原。"
            onConfirm={() => handleDelete(record.SmallCategoryId)}
            okText="確定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 分頁處理
  const handlePageChange = (page: number, pageSizeParam: number) => {
    setCurrentPage(page);
    setPageSize(pageSizeParam);
  };

  // 排序處理
  const handleSortChange = (sorter: SorterResult<SmallCategoryListItem> | SorterResult<SmallCategoryListItem>[]) => {
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    const newSortField = (currentSorter?.field as string) || null;
    const newSortOrder = currentSorter?.order || null;

    if (newSortField !== sortField || newSortOrder !== sortOrder) {
      setSortField(newSortField);
      setSortOrder(newSortOrder);
      if (currentPage !== 1) setCurrentPage(1);
    }
  };

  // 篩選處理
  const handleFilterChange = (currentSearchInfos: SearchTermInfo[]) => {
    setSearchInfos(currentSearchInfos.length > 0 ? currentSearchInfos : undefined);
    if (currentPage !== 1) setCurrentPage(1);
  };

  // 顯示新增模態框
  const showAddModal = () => {
    setModalType('add');
    setEditingCategory(null);
    setIsModalOpen(true);
  };

  // 顯示編輯模態框
  const showEditModal = async (record: SmallCategoryListItem) => {
    setModalType('edit');
    try {
      setLoading(true);
      const detailResponse = await categoryApi.getSmallCategoryById(record.SmallCategoryId);
      setEditingCategory(detailResponse);
      setIsModalOpen(true);
    } catch (error: any) {
      messageApi.error(error.message || '獲取小分類詳細資料失敗');
    } finally {
      setLoading(false);
    }
  };

  // 關閉模態框
  const handleCancel = () => {
    setIsModalOpen(false);
    setEditingCategory(null);
  };

  // 刪除處理
  const handleDelete = async (categoryId: number) => {
    try {
      setLoading(true);
      await categoryApi.deleteSmallCategory(categoryId);
      messageApi.success('刪除小分類成功');
      fetchData();
    } catch (error: any) {
      messageApi.error(error.message || '刪除小分類失敗');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {contextHolder}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-4">
          <BackButton to="/settings/categories" text="返回分類管理" />
        </div>

        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">小分類管理</h1>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showAddModal}
          >
            新增小分類
          </Button>
        </div>

        <DataTable<SmallCategoryListItem>
          columns={columns}
          dataSource={categories}
          loading={loading}
          total={totalCount}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onSort={handleSortChange}
          onFilter={handleFilterChange}
          rowKey="SmallCategoryId"
        />

        {isClient && (
          <SmallCategoryFormModal
            isOpen={isModalOpen}
            modalType={modalType}
            editingCategory={editingCategory}
            defaultValues={defaultAddFormValues}
            onCancel={handleCancel}
            onSuccess={fetchData}
            messageApi={messageApi}
          />
        )}
      </div>
    </div>
  );
}

// 獨立的小分類表單模態框元件
interface SmallCategoryFormModalProps {
  isOpen: boolean;
  modalType: 'add' | 'edit';
  editingCategory: SmallCategoryDetail | null;
  defaultValues: Partial<CreateSmallCategoryDto>;
  onCancel: () => void;
  onSuccess: () => void;
  messageApi: any;
}

// 表單資料類型，包含大分類ID用於UI控制
interface SmallCategoryFormData extends CreateSmallCategoryDto {
  LargeCategoryId?: number;
}

function SmallCategoryFormModal({
  isOpen,
  modalType,
  editingCategory,
  defaultValues,
  onCancel,
  onSuccess,
  messageApi
}: SmallCategoryFormModalProps) {
  const [form] = Form.useForm<SmallCategoryFormData>();
  const [loading, setLoading] = useState(false);
  const [largeCategoryOptions, setLargeCategoryOptions] = useState<{ Value: number; Name: string }[]>([]);
  const [mediumCategoryOptions, setMediumCategoryOptions] = useState<{ Value: number; Name: string }[]>([]);
  const [selectedLargeCategoryId, setSelectedLargeCategoryId] = useState<number | undefined>(undefined);

  // 獲取大分類下拉選單
  const fetchLargeCategoryOptions = useCallback(async () => {
    try {
      const options = await categoryApi.getLargeCategoryDropdown();
      setLargeCategoryOptions(options);
    } catch (error: any) {
      messageApi.error(error.message || '獲取大分類選項失敗');
    }
  }, []);

  // 獲取中分類下拉選單
  const fetchMediumCategoryOptions = useCallback(async (largeCategoryId: number) => {
    try {
      const options = await categoryApi.getMediumCategoryDropdown(largeCategoryId);
      setMediumCategoryOptions(options);
    } catch (error: any) {
      messageApi.error(error.message || '獲取中分類選項失敗');
      setMediumCategoryOptions([]);
    }
  }, []);

  // 大分類選擇變更處理
  const handleLargeCategoryChange = (largeCategoryId: number) => {
    setSelectedLargeCategoryId(largeCategoryId);
    // 清空中分類選擇，因為大分類改變了
    form.setFieldsValue({
      MediumCategoryId: undefined
    });
    setMediumCategoryOptions([]); // 立即清空選項
    if (largeCategoryId) {
      fetchMediumCategoryOptions(largeCategoryId);
    }
  };

  // 當模態框打開時，獲取大分類選項並設置表單值
  useEffect(() => {
    if (isOpen) {
      if (modalType === 'edit' && editingCategory) {
        // 編輯模式：先載入大分類選項，然後設置表單值
        setSelectedLargeCategoryId(editingCategory.LargeCategoryId);

        // 同時載入大分類和中分類選項
        Promise.all([
          fetchLargeCategoryOptions(),
          fetchMediumCategoryOptions(editingCategory.LargeCategoryId)
        ]).then(() => {
          // 所有選項載入完成後，設置表單值
          const formValues = {
            LargeCategoryId: editingCategory.LargeCategoryId,
            MediumCategoryId: editingCategory.MediumCategoryId,
            Name: editingCategory.Name,
            Description: editingCategory.Description,
            SortOrder: editingCategory.SortOrder,
            IsActive: editingCategory.IsActive,
          };

          // 使用 requestAnimationFrame 確保在下一個渲染週期設定值
          requestAnimationFrame(() => {
            form.setFieldsValue(formValues);
          });
        }).catch((error) => {
          console.error('載入分類選項失敗:', error);
        });
      } else {
        // 新增模式：只載入大分類選項
        fetchLargeCategoryOptions().then(() => {
          requestAnimationFrame(() => {
            form.setFieldsValue(defaultValues as any);
          });
        });
        setSelectedLargeCategoryId(undefined);
        setMediumCategoryOptions([]);
      }
    }
  }, [isOpen, modalType, editingCategory?.SmallCategoryId]);

  // 清理效果：當模態框關閉時重置狀態
  useEffect(() => {
    if (!isOpen) {
      setSelectedLargeCategoryId(undefined);
      setMediumCategoryOptions([]);
      form.resetFields();
    }
  }, [isOpen, form]);

  // 提交表單
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 移除 LargeCategoryId，因為 API 不需要這個欄位
      const { LargeCategoryId, ...submitValues } = values;

      if (modalType === 'add') {
        await categoryApi.createSmallCategory(submitValues as CreateSmallCategoryDto);
        messageApi.success('新增小分類成功');
      } else {
        if (!editingCategory) {
          messageApi.error('編輯資料不存在');
          return;
        }
        await categoryApi.updateSmallCategory(editingCategory.SmallCategoryId, submitValues as UpdateSmallCategoryDto);
        messageApi.success('更新小分類成功');
      }

      // 延遲清理以避免循環引用問題
      setTimeout(() => {
        form.resetFields();
        setSelectedLargeCategoryId(undefined);
        setMediumCategoryOptions([]);
      }, 0);
      onCancel();
      onSuccess();
    } catch (error: any) {
      if (error.errorFields) {
        // 表單驗證錯誤
        return;
      }
      messageApi.error(error.message || `${modalType === 'add' ? '新增' : '更新'}小分類失敗`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={modalType === 'add' ? '新增小分類' : '編輯小分類'}
      open={isOpen}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          {modalType === 'add' ? '新增' : '更新'}
        </Button>,
      ]}
      width={600}
      destroyOnClose={false}
      forceRender
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={defaultValues}
      >
          <Form.Item
            label="所屬大分類"
            name="LargeCategoryId"
            rules={[
              { required: true, message: '請選擇所屬大分類' }
            ]}
          >
            <Select
              placeholder="請選擇所屬大分類"
              onChange={handleLargeCategoryChange}
              allowClear
              showSearch
              optionFilterProp="children"
            >
              {largeCategoryOptions.map(option => (
                <Option key={option.Value} value={option.Value}>
                  {option.Name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="所屬中分類"
            name="MediumCategoryId"
            rules={[
              { required: true, message: '請選擇所屬中分類' }
            ]}
          >
            <Select
              placeholder="請先選擇大分類"
              disabled={!selectedLargeCategoryId}
              allowClear
              showSearch
              optionFilterProp="children"
              notFoundContent={!selectedLargeCategoryId ? "請先選擇大分類" : "無可用選項"}
            >
              {mediumCategoryOptions.map(option => (
                <Option key={option.Value} value={option.Value}>
                  {option.Name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="小分類名稱"
            name="Name"
            rules={[
              { required: true, message: '請輸入小分類名稱' },
              { max: 100, message: '小分類名稱不能超過100個字元' }
            ]}
          >
            <Input placeholder="請輸入小分類名稱" />
          </Form.Item>

          <Form.Item
            label="描述"
            name="Description"
            rules={[
              { max: 500, message: '描述不能超過500個字元' }
            ]}
          >
            <Input.TextArea 
              placeholder="請輸入描述（選填）" 
              rows={3}
            />
          </Form.Item>

          <Form.Item
            label="排序"
            name="SortOrder"
            rules={[
              { required: true, message: '請輸入排序數值' },
              { type: 'number', min: 0, message: '排序數值不能小於0' }
            ]}
          >
            <InputNumber 
              placeholder="請輸入排序數值" 
              min={0}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            label="狀態"
            name="IsActive"
            valuePropName="checked"
          >
            <Switch 
              checkedChildren="啟用" 
              unCheckedChildren="停用" 
            />
          </Form.Item>
        </Form>
    </Modal>
  );
} 