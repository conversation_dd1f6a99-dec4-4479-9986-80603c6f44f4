'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { App, Button, Modal, Form, Input, Popconfirm, Row, Col, Select, Spin } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import DataTable from '@/app/components/DataTable'; // 假設 DataTable 元件路徑
import { ownerApi } from '@/app/services/api/ownerApi';
import type { OwnerListItemDto, GetOwnerListParamsDto, CreateOwnerDto, UpdateOwnerDto, OwnerListResponse } from '@/app/services/api/ownerApi';
import type { BaseQueryParams, SortOrderInfo, SearchTermInfo } from '@/app/interfaces/dto/common.dto';
import type { SorterResult } from 'antd/es/table/interface';

export default function OwnerSettingsPage() {
  // 使用 App.useApp() 獲取 message API 實例
  const { message: messageApi } = App.useApp();

  // --- 狀態定義 ---
  const [owners, setOwners] = useState<OwnerListItemDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(null);
  const [searchInfos, setSearchInfos] = useState<SearchTermInfo[] | undefined>(undefined);

  // Modal 狀態
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingOwner, setEditingOwner] = useState<OwnerListItemDto | null>(null);
  const [form] = Form.useForm<CreateOwnerDto | UpdateOwnerDto>();
  const [modalLoading, setModalLoading] = useState(false); // Modal 表單載入狀態

  // --- 獲取資料函數 ---
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const sortOrderInfos: SortOrderInfo[] | undefined = sortField && sortOrder
        ? [{ SortField: sortField, SortOrder: sortOrder === 'ascend' ? 'asc' : 'desc' }]
        : undefined;

      const apiParams: GetOwnerListParamsDto = {
        UsingPaging: true,
        PageIndex: currentPage,
        NumberOfPperPage: pageSize,
        SortOrderInfos: sortOrderInfos,
        SearchTermInfos: searchInfos,
        // 如有需要，可添加特定的業主搜索欄位，例如：
        // CompanyName: searchInfos?.find(s => s.SearchField === 'CompanyName')?.SearchValue,
      };

      // 注意：API 返回結構包含 body，需從 body 取值
      const response = await ownerApi.getOwnerList(apiParams);

      // 檢查 response.body 是否存在並從中獲取數據
      if (response && response.body) {
          setOwners(response.body.Detail || []);
          setTotal(response.body.RecordCount || 0);
          // 可選：根據需要從後端同步分頁狀態
          // setCurrentPage(response.body.PageIndex || 1);
          // setPageSize(response.body.NumberOfPperPage || 10);
      } else {
          // 處理 body 可能缺失的情況（儘管 API 成功時應存在）
          setOwners([]);
          setTotal(0);
          console.warn('API 回應 body 缺失或無效:', response);
          messageApi.error('無法解析業主列表回應格式');
      }

    } catch (error) {
      console.error('無法獲取業主列表:', error);
      messageApi.error('無法載入業主列表');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, sortField, sortOrder, searchInfos]);

  // --- useEffect 以獲取資料 ---
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // --- Modal 邏輯 ---
  const showModal = async (owner: OwnerListItemDto | null = null) => {
    if (owner) {
      // 編輯模式：先獲取詳細資料
      setModalLoading(true);
      setIsModalOpen(true); // 立即打開 Modal 以顯示載入狀態
      form.resetFields(); // 獲取前清除表單
      try {
        const response = await ownerApi.getOwner(owner.OwnerId);
        if (response.isSuccess && response.body) {
          form.setFieldsValue(response.body); // 使用獲取的詳細資料填充表單
          setEditingOwner(owner); // 在成功獲取並填充*之後*設定編輯狀態
        } else {
          messageApi.error(response.message || '無法載入業主詳細資料');
          setIsModalOpen(false); // 如果獲取失敗則關閉 Modal
        }
      } catch (error) {
        console.error('無法獲取業主詳細資料:', error);
        messageApi.error('載入業主詳細資料失敗');
        setIsModalOpen(false); // 發生錯誤時關閉 Modal
      } finally {
        setModalLoading(false);
      }
    } else {
      // 新增模式
      setEditingOwner(null);
      form.resetFields();
      setIsModalOpen(true);
    }
  };

  const handleOk = async () => {
    try {
      // Validate fields - required fields will now be checked by rules
      const values = await form.validateFields();

      setLoading(true); // 在儲存期間顯示載入狀態

      let response;
      if (editingOwner) {
        // 更新邏輯 - 值應透過表單從獲取的資料中填充
        const updateData: UpdateOwnerDto = {
            ...values, // 使用表單驗證後的值
             OwnerId: editingOwner.OwnerId,
             // 移除佔位符 - 依賴從 getOwner 填充的表單值
        };
         // 假設 updateOwner 預期 body 中包含 Omit<UpdateOwnerDto, 'OwnerId'>
        const { OwnerId, ...dataToUpdate } = updateData;
        response = await ownerApi.updateOwner(OwnerId, dataToUpdate);
      } else {
        // 新增邏輯
        const createData: CreateOwnerDto = {
          ...values,
           // 確保包含 CreateOwnerDto 的所有必填欄位
           // 如適用，保留新增時必要的預設值/佔位符
             CompanyPhone: values.CompanyPhone || '', 
             CompanyAddress: values.CompanyAddress || '', 
             MailingAddress: values.MailingAddress || '', 
             Email: values.Email || '', 
             ContactPhone2: values.ContactPhone2 || undefined,
        };
        response = await ownerApi.createOwner(createData);
      }

      // 修改成功判斷邏輯：優先檢查 code === '000'
      if (response && response.code === '000') { // 假設 '000' 代表成功
        messageApi.success(editingOwner ? '業主更新成功' : '業主新增成功');
        setIsModalOpen(false);
        setEditingOwner(null);
        fetchData(); // 刷新資料
      } else {
        // API 邏輯錯誤 (HTTP 200 但 isSuccess: false)
        messageApi.error(response.message || '操作失敗');
      }
    } catch (error: any) { // 使用 any 或更具體的 AxiosError 類型
      // HTTP 錯誤 (非 2xx 狀態碼) 或其他異常
      console.error('無法儲存業主:', error);
      let errMsg = '儲存業主失敗'; // 預設錯誤訊息

      // 嘗試從 Axios 錯誤回應中提取後端訊息
      if (error.response && error.response.data && typeof error.response.data.message === 'string') {
        errMsg = error.response.data.message;
      } else if (error instanceof Error && error.message.includes('Validation')) {
        // 如果是表單驗證錯誤，不顯示通用彈出訊息，讓表單自行處理欄位提示
         // setLoading(false); // 確保在 return 前關閉 loading (如果需要)
         // return; // 可以考慮提前返回
         // 目前維持原樣，讓 finally 處理 setLoading
      } else if (error instanceof Error) {
        // 對於其他 JS 錯誤，保留預設訊息
      }

      // 只有在不是表單驗證錯誤時才顯示彈出訊息
      if (!(error instanceof Error && error.message.includes('Validation'))) {
          messageApi.error(errMsg);
      }

    } finally {
        // 確保即使驗證提前失敗，也關閉載入狀態
         if (isModalOpen) { // 僅當 Modal 仍然相關時關閉載入
             // 注意：這裡的 setLoading(false) 控制的是 Modal OK 按鈕的 loading，不是 modalLoading
             setLoading(false); 
         }
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setEditingOwner(null);
    form.resetFields();
  };

  // --- 刪除邏輯 ---
  const handleDelete = async (ownerId: number) => {
    setLoading(true);
    try {
      const response = await ownerApi.deleteOwner(ownerId);
      if (response.isSuccess) {
        messageApi.success('業主刪除成功');

        // 檢查刪除後是否需要跳轉頁碼
        const newTotal = total - 1;
        const firstItemIndexOfCurrentPage = (currentPage - 1) * pageSize;

        if (firstItemIndexOfCurrentPage >= newTotal && currentPage > 1) {
          // 如果刪除的是最後一頁的最後一項，或刪除後當前頁變空，則跳轉到前一頁
          setCurrentPage(currentPage - 1);
          // useEffect 會自動觸發 fetchData
        } else {
          // 否則，重新載入當前頁
          fetchData();
        }
      } else {
        messageApi.error(response.message || '刪除失敗');
      }
    } catch (error) {
      console.error('無法刪除業主:', error);
      messageApi.error('刪除業主失敗');
    } finally {
      setLoading(false);
    }
  };


  // --- DataTable 回調函數 ---
  const handlePageChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  const handleSort = (sorter: SorterResult<OwnerListItemDto> | SorterResult<OwnerListItemDto>[]) => {
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    const newSortField = (currentSorter?.field as string) ?? null;
    const newSortOrder = currentSorter?.order ?? null;

    if (newSortField !== sortField || newSortOrder !== sortOrder) {
      setSortField(newSortField);
      setSortOrder(newSortOrder);
      if (currentPage !== 1) {
        setCurrentPage(1); // 排序變更時重置頁碼到 1
      }
      // 資料獲取由 useEffect 依賴變更處理
    }
  };

  const handleFilter = (currentSearchInfos: SearchTermInfo[]) => {
    const newSearchInfos = currentSearchInfos.length > 0 ? currentSearchInfos : undefined;
    // 基本檢查以防止不必要的狀態更新/重新獲取
    if (JSON.stringify(newSearchInfos) !== JSON.stringify(searchInfos)) {
        setSearchInfos(newSearchInfos);
        if (currentPage !== 1) {
            setCurrentPage(1); // 篩選變更時重置頁碼到 1
        }
         // 資料獲取由 useEffect 依賴變更處理
    }
  };

  // --- DataTable 欄位定義 ---
  const columns: any[] = [
    {
      title: '公司名稱',
      dataIndex: 'CompanyName',
      key: 'CompanyName',
      sorter: true,
      allowSearch: true,
    },
    {
      title: '負責人',
      dataIndex: 'ResponsiblePerson',
      key: 'ResponsiblePerson',
      sorter: true,
      allowSearch: true,
    },
    {
      title: '聯絡人',
      dataIndex: 'ContactPerson',
      key: 'ContactPerson',
      sorter: true,
      allowSearch: true,
    },
    {
      title: '聯絡電話1',
      dataIndex: 'ContactPhone1',
      key: 'ContactPhone1',
      sorter: true,
      allowSearch: true,
    },
    {
        title: '人別',
        dataIndex: 'PersonType',
        key: 'PersonType',
        sorter: true,
        allowSearch: true,
    },
    {
      title: '證號 (統編/ID)',
      dataIndex: 'IdentificationNumber',
      key: 'IdentificationNumber',
      sorter: true,
      allowSearch: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: OwnerListItemDto) => (
        <span className="space-x-2">
          <Button
            type="link"
            onClick={() => showModal(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定要刪除此業主嗎？"
            onConfirm={() => handleDelete(record.OwnerId)}
            okText="確定"
            cancelText="取消"
          >
            <Button type="link" danger>
              刪除
            </Button>
          </Popconfirm>
        </span>
      ),
    },
  ];

  // --- 渲染 ---
  return (
    <div className="p-4"> { /* 外部內距容器 */ }
      {/* 內容的白色背景容器 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-4">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => showModal()}
          >
            新增業主
          </Button>
        </div>

        <DataTable<OwnerListItemDto>
          columns={columns}
          dataSource={owners}
          loading={loading}
          total={total}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onSort={handleSort}
          onFilter={handleFilter}
          rowKey="OwnerId" // 重要：設定您唯一的列鍵值
        />
      </div> { /* 白色背景容器結束 */ }

      {/* 新增/編輯 Modal (保持在白色容器外部) */}
      <Modal
        title={editingOwner ? '編輯業主' : '新增業主'}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading} // 這是用於儲存期間「確定」按鈕的載入狀態
        width={800} // 根據需要調整寬度
      >
        {/* 根據 modalLoading 為 Form 添加旋轉狀態 */}
        <Spin spinning={modalLoading}>
          <Form
            form={form}
            layout="vertical"
            name="owner_form"
          >
            <Row gutter={16}> { /* 使用 gutter 為欄位之間提供間距 */ }
              {/* 左欄 */}
              <Col span={12}>
                <Form.Item
                  name="CompanyName"
                  label="公司名稱"
                  rules={[{ required: true, message: '請輸入公司名稱' }]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  name="ResponsiblePerson"
                  label="負責人"
                  required={false}
                >
                  <Input />
                </Form.Item>
                 <Form.Item
                  name="CompanyPhone"
                  label="公司電話"
                  required={false}
                >
                  <Input />
                </Form.Item>
                 <Form.Item
                  name="CompanyAddress"
                  label="公司地址"
                  required={false}
                >
                  <Input />
                </Form.Item>
                 <Form.Item
                  name="MailingAddress"
                  label="通訊地址"
                  required={false}
                >
                  <Input />
                </Form.Item>
                 <Form.Item
                  name="ContactPerson"
                  label="聯絡窗口"
                  required={false}
                >
                  <Input />
                </Form.Item>
                 <Form.Item
                  name="Email"
                  label="Email"
                  required={false}
                  rules={[{ type: 'email', message: '請輸入有效的 Email 格式' }]}
                >
                  <Input />
                </Form.Item>
              </Col>

              {/* 右欄 */}
              <Col span={12}>
                <Form.Item
                  name="PersonType"
                  label="人別"
                  rules={[{ required: true, message: '請選擇人別' }]}
                >
                  <Select placeholder="請選擇">
                    <Select.Option value="法人">法人</Select.Option>
                    <Select.Option value="自然人">自然人</Select.Option>
                    {/* 如有需要，添加其他選項 */}
                  </Select>
                </Form.Item>
                <Form.Item
                  name="IdentificationNumber"
                  label="證號 (統編/ID)"
                  rules={[{ required: true, message: '請輸入統一編號或身份證號' }]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  name="ContactPhone1"
                  label="聯絡電話 1"
                  required={false}
                >
                  <Input />
                </Form.Item>
                 <Form.Item
                  name="ContactPhone2"
                  label="聯絡電話 2 (選填)"
                  required={false}
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>
            {/* 已移除原始的順序表單項目 */}
          </Form>
        </Spin>
      </Modal>
    </div>
  );
} 