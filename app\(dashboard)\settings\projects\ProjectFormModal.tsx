import React, { useEffect, useState } from "react";
import { X } from "lucide-react";
import TableSelectModal from "../../../components/common/TableSelectModal";
import { CreateSiteDto, UpdateSiteDto, SiteListItem } from "../../../services/api/siteApi";
import { formatDateForInput } from "../../../utils/dateUtils";
import { DropdownItem } from "../../../interfaces/dto/common.dto";
import { Form, Input, Select, Button } from 'antd';

interface ProjectFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (formData: CreateSiteDto | UpdateSiteDto) => void;
  title: string;
  initialData?: SiteListItem | null;
  isEditMode?: boolean;
  companies: DropdownItem[];
  usersForDropdown: DropdownItem[];
}

const defaultFormValues: Partial<CreateSiteDto | UpdateSiteDto> = {
  SiteCode: '',
  CompanyId: '',
  SiteName: '',
  PromotionType: '',
  Chairman: '',
  Vice<PERSON>hairman: '',
  ProjectManager: '',
  DeputyProjectManager: '',
  BusinessIds: '',
  RunnerIds: '',
  ReceptionCenter: '',
  SitePhone: '',
  Broker: '',
  City: '',
  District: '',
  Developer: '',
  SiteLocation: '',
  LandArea: 0,
  Zoning: '',
  PublicFacilityRatio: 0,
  Structure: '',
  UnitSize: '',
  TotalSalePrice: 0,
  ParkingType: '',
  ContractPeriod: '',
  ExtensionPeriod: '',
  SellableTotalPrice: 0,
  ServiceFeeCalculation: '',
  ServiceFeeRate: 0,
  ReserveAmount: 0,
  AdvertisingBudget: 0,
  AdvertisingBudgetRate: 0,
  ExcessPriceAllocation: '',
  ContractedAmount: 0,
  ControlReserveRate: 0,
  PaidAmount: 0,
  AboveGroundFloors: '',
  BelowGroundFloors: '',
  PlannedResidentialUnits: 0,
  PlannedStoreUnits: 0,
  PlannedParkingSpaces: 0,
};

const ensureNumber = (value: any, defaultValue = 0): number => {
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
};

export default function ProjectFormModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData,
  isEditMode = false,
  companies,
  usersForDropdown,
}: ProjectFormModalProps) {
  const [form] = Form.useForm<CreateSiteDto | UpdateSiteDto>();
  const [isCompanyModalOpen, setIsCompanyModalOpen] = useState(false);
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [userModalTargetField, setUserModalTargetField] = useState<keyof (CreateSiteDto | UpdateSiteDto) | null>(null);
  const [isUserModalMultiSelect, setIsUserModalMultiSelect] = useState(false);
  const companyIdWatcher = Form.useWatch('CompanyId', form);

  useEffect(() => {
    if (isOpen) {
      if (isEditMode && initialData) {
        form.setFieldsValue({
          ...initialData,
          ContractPeriod: formatDateForInput(initialData.ContractPeriod),
          ExtensionPeriod: formatDateForInput(initialData.ExtensionPeriod),
        });
      } else {
        form.setFieldsValue(defaultFormValues);
      }
    } else {
      form.resetFields();
    }
  }, [isOpen, isEditMode, initialData, form]);

  const handleSubmit = async () => {
    try {
      const validatedValues = await form.validateFields();

      const dataToSubmit: CreateSiteDto | UpdateSiteDto = {
        ...validatedValues,
        LandArea: ensureNumber(validatedValues.LandArea),
        PublicFacilityRatio: ensureNumber(validatedValues.PublicFacilityRatio),
        TotalSalePrice: ensureNumber(validatedValues.TotalSalePrice),
        SellableTotalPrice: ensureNumber(validatedValues.SellableTotalPrice),
        ServiceFeeRate: ensureNumber(validatedValues.ServiceFeeRate),
        ReserveAmount: ensureNumber(validatedValues.ReserveAmount),
        AdvertisingBudget: ensureNumber(validatedValues.AdvertisingBudget),
        AdvertisingBudgetRate: ensureNumber(validatedValues.AdvertisingBudgetRate),
        ContractedAmount: ensureNumber(validatedValues.ContractedAmount),
        ControlReserveRate: ensureNumber(validatedValues.ControlReserveRate),
        PaidAmount: ensureNumber(validatedValues.PaidAmount),
        PlannedResidentialUnits: ensureNumber(validatedValues.PlannedResidentialUnits),
        PlannedStoreUnits: ensureNumber(validatedValues.PlannedStoreUnits),
        PlannedParkingSpaces: ensureNumber(validatedValues.PlannedParkingSpaces),
      };
      
      onSubmit(dataToSubmit);
    } catch (errorInfo) {
      console.log('Validation Failed:', errorInfo);
    }
  };

  if (!isOpen) return null;

  const selectedCompanyName = companies.find(c => c.Value === companyIdWatcher)?.Name;

  const openUserSelectModal = (fieldName: keyof (CreateSiteDto | UpdateSiteDto), multiSelect: boolean) => {
    setUserModalTargetField(fieldName);
    setIsUserModalMultiSelect(multiSelect);
    setIsUserModalOpen(true);
  };

  const findUserName = (userId: string | undefined | null): string => {
    if (!userId) return '';
    return usersForDropdown.find(u => u.Value === userId)?.Name || userId;
  };

  const findUserNames = (userIdsString: string | undefined | null): string => {
    if (!userIdsString) return '';
    const ids = userIdsString.split(',');
    return ids.map(id => findUserName(id.trim())).filter(name => name).join(', ');
  };

  const getCurrentFieldValue = (fieldName: keyof (CreateSiteDto | UpdateSiteDto)): string | undefined | null => form.getFieldValue(fieldName);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[90%] max-w-6xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold">{title}</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X className="h-6 w-6" />
            </button>
          </div>

          <Form form={form} onFinish={handleSubmit} layout="vertical" className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-lg font-semibold bg-blue-500 text-white p-3 rounded mb-6">
                基本資料
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Form.Item name="CompanyId" label="執行公司別" rules={[{ required: true, message: '請選擇執行公司別' }]}>
                  <Button
                    type="default"
                    onClick={() => setIsCompanyModalOpen(true)}
                    className="w-full text-left flex justify-between items-center h-10"
                  >
                    {selectedCompanyName ? (
                      <span className="text-gray-900">{selectedCompanyName}</span>
                    ) : (
                      <span className="text-gray-500">選擇執行公司別</span>
                    )}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                     </svg>
                  </Button>
                </Form.Item>
                <Form.Item name="PromotionType" label="推案型態" rules={[{ required: true, message: '請選擇推案型態' }]}>
                  <Select placeholder="請選擇">
                    <Select.Option value="預售">預售</Select.Option>
                    <Select.Option value="成屋">成屋</Select.Option>
                    <Select.Option value="商辦">商辦</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item name="SiteName" label="案名" rules={[{ required: true, message: '請輸入案名' }]}>
                  <Input />
                </Form.Item>
                <Form.Item name="SiteCode" label="案場編碼" rules={[{ required: true, message: '請輸入案場編碼' }]}>
                  <Input disabled={isEditMode} />
                </Form.Item>
                <Form.Item name="Chairman" label="主委">
                  <Button type="default" onClick={() => openUserSelectModal('Chairman', false)} className="w-full text-left flex justify-between items-center h-10">
                    {getCurrentFieldValue('Chairman') ? (
                      <span className="text-gray-900">{findUserName(getCurrentFieldValue('Chairman'))}</span>
                    ) : (
                      <span className="text-gray-500">選擇主委</span>
                    )}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                     </svg>
                  </Button>
                </Form.Item>
                <Form.Item name="ViceChairman" label="副主委">
                   <Button type="default" onClick={() => openUserSelectModal('ViceChairman', false)} className="w-full text-left flex justify-between items-center h-10">
                    {getCurrentFieldValue('ViceChairman') ? (
                      <span className="text-gray-900">{findUserName(getCurrentFieldValue('ViceChairman'))}</span>
                    ) : (
                      <span className="text-gray-500">選擇副主委</span>
                    )}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                     </svg>
                  </Button>
                </Form.Item>
                 <Form.Item name="ProjectManager" label="專案經理">
                   <Button type="default" onClick={() => openUserSelectModal('ProjectManager', false)} className="w-full text-left flex justify-between items-center h-10">
                    {getCurrentFieldValue('ProjectManager') ? (
                      <span className="text-gray-900">{findUserName(getCurrentFieldValue('ProjectManager'))}</span>
                    ) : (
                      <span className="text-gray-500">選擇專案經理</span>
                    )}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                     </svg>
                  </Button>
                 </Form.Item>
                 <Form.Item name="DeputyProjectManager" label="副專案經理">
                  <Button type="default" onClick={() => openUserSelectModal('DeputyProjectManager', false)} className="w-full text-left flex justify-between items-center h-10">
                    {getCurrentFieldValue('DeputyProjectManager') ? (
                      <span className="text-gray-900">{findUserName(getCurrentFieldValue('DeputyProjectManager'))}</span>
                    ) : (
                      <span className="text-gray-500">選擇副專案經理</span>
                    )}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                     </svg>
                  </Button>
                 </Form.Item>
                <Form.Item name="BusinessIds" label="業務 (可複選)">
                  <Button type="default" onClick={() => openUserSelectModal('BusinessIds', true)} className="w-full text-left flex justify-between items-center h-10 min-h-[2.5rem]">
                    {getCurrentFieldValue('BusinessIds') ? (
                      <span className="text-gray-900 text-xs line-clamp-2">{findUserNames(getCurrentFieldValue('BusinessIds'))}</span>
                    ) : (
                      <span className="text-gray-500">選擇業務</span>
                    )}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                     </svg>
                  </Button>
                </Form.Item>
                <Form.Item name="RunnerIds" label="跑單 (可複選)">
                  <Button type="default" onClick={() => openUserSelectModal('RunnerIds', true)} className="w-full text-left flex justify-between items-center h-10 min-h-[2.5rem]">
                    {getCurrentFieldValue('RunnerIds') ? (
                      <span className="text-gray-900 text-xs line-clamp-2">{findUserNames(getCurrentFieldValue('RunnerIds'))}</span>
                    ) : (
                      <span className="text-gray-500">選擇跑單</span>
                    )}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                     </svg>
                  </Button>
                </Form.Item>
                <Form.Item name="ReceptionCenter" label="接待中心">
                  <Input />
                </Form.Item>
                <Form.Item name="SitePhone" label="案場電話">
                  <Input />
                </Form.Item>
                <Form.Item name="Broker" label="經紀人">
                  <Input />
                </Form.Item>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-lg font-semibold bg-blue-500 text-white p-3 rounded mb-6">
                個案基本資料
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Form.Item name="City" label="城市">
                  <Input />
                </Form.Item>
                <Form.Item name="District" label="行政區">
                  <Input />
                </Form.Item>
                <Form.Item name="Developer" label="投資興建">
                  <Input />
                </Form.Item>
                <Form.Item name="SiteLocation" label="基地位置">
                  <Input />
                </Form.Item>
                <Form.Item name="LandArea" label="土地面積(坪)">
                  <Input type="number" step="any" />
                </Form.Item>
                <Form.Item name="Zoning" label="土地分區">
                  <Input />
                </Form.Item>
                <Form.Item name="PublicFacilityRatio" label="公設比(%)">
                  <Input type="number" step="any" />
                </Form.Item>
                <Form.Item name="Structure" label="建築結構">
                  <Input />
                </Form.Item>
                <Form.Item name="AboveGroundFloors" label="樓層規劃(地上)">
                  <Input />
                </Form.Item>
                <Form.Item name="BelowGroundFloors" label="樓層規劃(地下)">
                  <Input />
                </Form.Item>
                <Form.Item name="PlannedResidentialUnits" label="規劃戶數(住家)">
                  <Input type="number" />
                </Form.Item>
                <Form.Item name="PlannedStoreUnits" label="規劃戶數(店面)">
                  <Input type="number" />
                </Form.Item>
                <Form.Item name="PlannedParkingSpaces" label="規劃車位數">
                  <Input type="number" />
                </Form.Item>
                <Form.Item name="UnitSize" label="規劃坪數">
                  <Input />
                </Form.Item>
                <Form.Item name="ParkingType" label="車位類型">
                  <Input />
                </Form.Item>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-lg font-semibold bg-blue-500 text-white p-3 rounded mb-6">
                代銷合約約定
                  </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Form.Item name="ContractPeriod" label="合約期間">
                  <Input type="date" />
                </Form.Item>
                <Form.Item name="ExtensionPeriod" label="延長期間">
                  <Input type="date" />
                </Form.Item>
                <Form.Item name="SellableTotalPrice" label="總銷金額">
                  <Input type="number" step="any" />
                </Form.Item>
                <Form.Item name="ServiceFeeCalculation" label="服務費計算方式">
                  <Input />
                </Form.Item>
                <Form.Item name="ServiceFeeRate" label="服務費率(%)">
                  <Input type="number" step="any" />
                </Form.Item>
                <Form.Item name="ReserveAmount" label="包銷底價">
                  <Input type="number" step="any" />
                </Form.Item>
                <Form.Item name="AdvertisingBudget" label="廣告預算">
                  <Input type="number" step="any" />
                </Form.Item>
                <Form.Item name="AdvertisingBudgetRate" label="廣告預算費率(%)">
                  <Input type="number" step="any" />
                </Form.Item>
                <Form.Item name="ExcessPriceAllocation" label="溢價分配方式">
                  <Input />
                </Form.Item>
                <Form.Item name="ContractedAmount" label="簽約款">
                  <Input type="number" step="any" />
                </Form.Item>
                <Form.Item name="ControlReserveRate" label="管銷預備金提撥率(%)">
                  <Input type="number" step="any" />
                </Form.Item>
                <Form.Item name="PaidAmount" label="請款累計">
                  <Input type="number" step="any" />
                </Form.Item>
              </div>
            </div>

            <div className="flex justify-end space-x-4 pt-4 border-t mt-6">
              <Button
                type="default"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                className="px-4 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600"
              >
                {isEditMode ? "儲存變更" : "確認新增"}
              </Button>
            </div>
          </Form>
        </div>
      </div>
      <TableSelectModal
        isOpen={isCompanyModalOpen}
        onClose={() => setIsCompanyModalOpen(false)}
        onSelect={(selectedCompany: DropdownItem) => {
          if (selectedCompany) {
            form.setFieldsValue({ CompanyId: selectedCompany.Value });
          }
          setIsCompanyModalOpen(false);
        }}
        title="選擇執行公司別"
        data={companies}
        columns={[
          { key: "Value", title: "公司代碼" },
          { key: "Name", title: "公司名稱" },
        ]}
        rowKey="Value"
        initialSelectedValues={form.getFieldValue('CompanyId') ? [form.getFieldValue('CompanyId')] : []}
      />
      <TableSelectModal
        isOpen={isUserModalOpen}
        onClose={() => setIsUserModalOpen(false)}
        onSelect={(selectedItems) => {
          if (userModalTargetField) {
            let valueToSet: string;
            if (Array.isArray(selectedItems)) {
              valueToSet = selectedItems.map(item => item.Value).join(', ');
            } else {
              valueToSet = selectedItems.Value;
            }
            form.setFieldsValue({ [userModalTargetField]: valueToSet });
            form.validateFields([userModalTargetField]);
          }
          setIsUserModalOpen(false);
        }}
        title={`選擇人員`}
        data={usersForDropdown}
        columns={[
          { key: "Value", title: "帳號" },
          { key: "Name", title: "姓名" },
        ]}
        searchKeys={["Value", "Name"]}
        rowKey="Value"
        isMultiSelect={isUserModalMultiSelect}
        initialSelectedValues={
          userModalTargetField && getCurrentFieldValue(userModalTargetField) 
            ? (isUserModalMultiSelect ? String(getCurrentFieldValue(userModalTargetField)).split(',').map(s => s.trim()) : [String(getCurrentFieldValue(userModalTargetField))])
            : []
        }
      />
    </div>
  );
} 