"use client";

import React, { useState, useEffect } from "react";
import { ConfigProvider, Tag, Space, Popconfirm, message, Button } from 'antd';
import { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import zhTW from 'antd/lib/locale/zh_TW';
import ProjectFormModal from "./ProjectFormModal";
import TableSelectModal from "../../../components/common/TableSelectModal";
import DataTable from "../../../components/DataTable";
import { siteApi, SiteListItem, CreateSiteDto, UpdateSiteDto } from "../../../services/api/siteApi";
import { BaseQueryParams, DropdownItem, SortOrderInfo } from "../../../interfaces/dto/common.dto";
import { userInfoApi } from "../../../services/api/userInfoApi";

export default function ProjectSettingsPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedProject, setSelectedProject] = useState<SiteListItem | null>(null);
  const [isFirstCompanyModalOpen, setIsFirstCompanyModalOpen] = useState(false);

  const [projects, setProjects] = useState<SiteListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [messageApi, contextHolder] = message.useMessage();

  const [companies, setCompanies] = useState<DropdownItem[]>([]);
  const [usersForDropdown, setUsersForDropdown] = useState<DropdownItem[]>([]);

  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(null);

  const findUserNameById = (userId: string): string => {
    if (!userId) return userId;
    return usersForDropdown.find(u => u.Value === userId)?.Name || userId;
  };

  const findUserNamesByIds = (userIdsString: string): string => {
    if (!userIdsString) return userIdsString;
    const ids = userIdsString.split(',');
    return ids
      .map(id => findUserNameById(id.trim()))
      .filter(name => name)
      .join(', ');
  };

  const columns = [
    { title: '案場編碼', dataIndex: 'SiteCode', key: 'SiteCode', sorter: true, allowSearch: true },
    { title: '案名', dataIndex: 'SiteName', key: 'SiteName', sorter: true, allowSearch: true },
    {
      title: '執行公司別',
      dataIndex: 'CompanyId',
      key: 'CompanyId',
      sorter: true,
      allowSearch: true,
      render: (companyId: string) => {
        const company = companies.find(c => c.Value === companyId);
        return company ? company.Name : companyId;
      }
    },
    { title: '推案型態', dataIndex: 'PromotionType', key: 'PromotionType', sorter: true, allowSearch: true },
    { 
      title: '主委', 
      dataIndex: 'Chairman', 
      key: 'Chairman', 
      sorter: true, 
      allowSearch: true, 
      render: findUserNameById
    },
    { 
      title: '副主委', 
      dataIndex: 'ViceChairman', 
      key: 'ViceChairman', 
      sorter: true, 
      allowSearch: true, 
      render: findUserNameById
    },
    { 
      title: '專案經理', 
      dataIndex: 'ProjectManager', 
      key: 'ProjectManager', 
      sorter: true, 
      allowSearch: true, 
      render: findUserNameById
    },
    { 
      title: '副專案經理', 
      dataIndex: 'DeputyProjectManager', 
      key: 'DeputyProjectManager', 
      sorter: true, 
      allowSearch: true, 
      render: findUserNameById
    },
    { 
      title: '業務', 
      dataIndex: 'BusinessIds', 
      key: 'BusinessIds', 
      sorter: true,
      allowSearch: true,
      render: findUserNamesByIds
    },
    { 
      title: '跑單', 
      dataIndex: 'RunnerIds', 
      key: 'RunnerIds', 
      sorter: true,
      allowSearch: true,
      render: findUserNamesByIds
    },
    { title: '基地位置', dataIndex: 'SiteLocation', key: 'SiteLocation', sorter: true, allowSearch: true },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: SiteListItem) => (
        <Space size="middle">
          <Button
            type="link"
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定要刪除此案場嗎？"
            description="刪除後可能無法恢復。"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button type="link" danger>
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
      sorter: false,
      allowSearch: false,
    },
  ];

  const fetchProjects = async (page = 1, size = pageSize, searchField?: string, searchValue?: string, currentSortField = sortField, currentSortOrder = sortOrder) => {
    setLoading(true);
    try {
      const params: BaseQueryParams = {
         UsingPaging: true,
         PageIndex: page,
         NumberOfPperPage: size
      };
      if (searchField && searchValue) {
        params.SearchTermInfos = [{ SearchField: searchField, SearchValue: searchValue }];
      }
      if (currentSortField && currentSortOrder) {
        params.SortOrderInfos = [{
           SortField: currentSortField,
           SortOrder: currentSortOrder === 'ascend' ? 'asc' : 'desc'
         }];
      }
      
      const response = await siteApi.getSiteList(params);
      if (response.isSuccess && response.body) {
        setProjects(response.body.Detail);
        setTotal(response.body.RecordCount);
        setCurrentPage(response.body.PageIndex);
        setPageSize(response.body.NumberOfPperPage);
        setSortField(currentSortField);
        setSortOrder(currentSortOrder);
      } else {
        messageApi.error(response.message || '獲取案場列表失敗');
        setProjects([]);
        setTotal(0);
      }
    } catch (error) {
      console.error('獲取案場列表失敗:', error);
      messageApi.error('獲取案場列表失敗');
      setProjects([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (sorter: any) => {
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    const newSortField = currentSorter?.field as string || null;
    const newSortOrder = currentSorter?.order as ('ascend' | 'descend' | null) || null;
    
    if (newSortField !== sortField || newSortOrder !== sortOrder) {
      setSortField(newSortField);
      setSortOrder(newSortOrder);
      fetchProjects(1, pageSize, undefined, undefined, newSortField, newSortOrder);
    }
  };

  const handlePageChange = (page: number, size?: number) => {
    const newPageSize = size || pageSize;
    if (page !== currentPage || newPageSize !== pageSize) {
      fetchProjects(page, newPageSize, undefined, undefined, sortField, sortOrder);
    }
  };

  const handleFilter = (searchInfos: { SearchField: string; SearchValue: string }[]) => {
    const searchInfo = searchInfos.length > 0 ? searchInfos[0] : null;
    fetchProjects(1, pageSize, searchInfo?.SearchField, searchInfo?.SearchValue, sortField, sortOrder);
  };

  const handleEdit = (project: SiteListItem) => {
    setSelectedProject(project);
    setIsEditMode(true);
    setIsModalOpen(true);
  };

  const handleDelete = async (project: SiteListItem) => {
    setLoading(true);
    try {
      const response = await siteApi.deleteSite(project.SiteCode);
      if (response.isSuccess) {
        messageApi.success('刪除案場成功');
        const newTotal = total - 1;
        const newTotalPages = Math.ceil(newTotal / pageSize);
        const newCurrentPage = currentPage > newTotalPages ? Math.max(1, newTotalPages) : currentPage;
        fetchProjects(newCurrentPage, pageSize);
      } else {
        messageApi.error(response.message || '刪除案場失敗');
        setLoading(false);
      }
    } catch (error) {
      console.error('刪除案場失敗:', error);
      messageApi.error('刪除案場失敗');
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setSelectedProject(null);
    setIsEditMode(false);
    setIsModalOpen(true);
  };

  const handleFormSubmit = async (formData: CreateSiteDto | UpdateSiteDto) => {
    setLoading(true);
    try {
      const apiCall = isEditMode
        ? siteApi.updateSite(formData as UpdateSiteDto)
        : siteApi.createSite(formData as CreateSiteDto);
      const response = await apiCall;
      if (response.isSuccess) {
        messageApi.success(isEditMode ? '更新案場成功' : '新增案場成功');
        setIsModalOpen(false);
        fetchProjects(isEditMode ? currentPage : 1, pageSize);
      } else {
        messageApi.error(response.message || (isEditMode ? '更新案場失敗' : '新增案場失敗'));
        setLoading(false);
      }
    } catch (error) {
      console.error(isEditMode ? '更新案場失敗:' : '新增案場失敗:', error);
      messageApi.error(isEditMode ? '更新案場失敗' : '新增案場失敗');
      setLoading(false);
    }
  };

  const fetchCompanies = async () => {
    try {
      const response = await userInfoApi.getCompanyDropdownList();
      if (response.isSuccess && response.body) {
        setCompanies(response.body);
      }
    } catch (error) {
      console.error("獲取公司列表失敗:", error);
      messageApi.error('獲取公司列表失敗');
    }
  };

  const fetchUsersForDropdown = async () => {
    try {
      const response = await userInfoApi.getUserInfoDropdownList();
      if (response.isSuccess && response.body) {
        setUsersForDropdown(response.body);
      } else {
        messageApi.error(response.message || '獲取人員列表失敗');
      }
    } catch (error) {
      console.error("獲取人員列表失敗:", error);
      messageApi.error('獲取人員列表失敗');
    }
  };

  useEffect(() => {
    fetchProjects(currentPage, pageSize, undefined, undefined, sortField, sortOrder);
    fetchCompanies();
    fetchUsersForDropdown();
  }, []);

  return (
    <ConfigProvider locale={zhTW}>
      {contextHolder}
      <div className="p-4 bg-white rounded-lg shadow">
        <div className="mb-4">
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新增案場
          </Button>
        </div>

        <DataTable
          columns={columns}
          dataSource={projects}
          loading={loading}
          total={total}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onFilter={handleFilter}
          onSort={handleSort}
          rowKey="SiteCode"
        />

        <ProjectFormModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSubmit={handleFormSubmit}
          title={isEditMode ? "編輯案場" : "新增案場"}
          initialData={selectedProject}
          isEditMode={isEditMode}
          companies={companies}
          usersForDropdown={usersForDropdown}
        />

        <TableSelectModal
          isOpen={isFirstCompanyModalOpen}
          onClose={() => setIsFirstCompanyModalOpen(false)}
          onSelect={(company) => {
            console.log(company);
            setIsFirstCompanyModalOpen(false);
          }}
          title="選擇建設公司"
          data={[]}
          columns={[
            { key: "Value", title: "單位代號" },
            { key: "Name", title: "單位名稱" },
          ]}
          rowKey="Value"
        />
      </div>
    </ConfigProvider>
  );
}
