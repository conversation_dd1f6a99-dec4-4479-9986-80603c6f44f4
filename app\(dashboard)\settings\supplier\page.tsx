'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Form, Input, Select, Button, Modal as AntdModal, Popconfirm, Space, message, Upload, Table } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import DataTable from '../../../components/DataTable';
import TableSelectModal from '../../../components/common/TableSelectModal';
import { supplierApi } from '../../../services/api/supplierApi';
import { userInfoApi } from '../../../services/api/userInfoApi';
import {
  SupplierListItem,
  GetSuppliersParams,
  CreateSupplierDto,
  UpdateSupplierDto,
  SupplierDetail,
  ApiResponse,
  SupplierFileBase,
  GetSuppliersResponse
} from '../../../interfaces/dto/supplier.dto';
import { SearchTermInfo, SortOrderInfo, DropdownItem } from '../../../interfaces/dto/common.dto';
import { SorterResult, ColumnType } from 'antd/es/table/interface';
import { UploadFile } from 'antd/es/upload/interface';

// Define File Type options based on the image provided
const formTypeOptions: DropdownItem[] = [
  { Value: 'BUSINESS_REGISTRATION_CERTIFICATE', Name: '營利事業登記證明／公司變更登記事項' },
  { Value: 'TAX_DECLARATION_401', Name: '一般營業人銷售額與稅額申報書 (401)' },
  { Value: 'COOPERATION_TERMS', Name: '合作廠商特約條款' },
  { Value: 'PAYMENT_AGREEMENT', Name: '匯款同意書' },
  { Value: 'BANK_ACCOUNT_INFO', Name: '銀行帳戶' },
  { Value: 'ID_CARD', Name: '身份證' },
  { Value: 'HEALTH_INSURANCE_CARD', Name: '健保卡' },
  { Value: 'OTHER_FILES', Name: '其他檔案' },
];

export default function SupplierPage() {
  const [form] = Form.useForm<CreateSupplierDto | UpdateSupplierDto>();
  const [suppliers, setSuppliers] = useState<SupplierListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'add' | 'edit'>('add');
  const [editingSupplier, setEditingSupplier] = useState<SupplierDetail | null>(null);
  const [messageApi, contextHolder] = message.useMessage();

  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(null);
  const [searchInfos, setSearchInfos] = useState<SearchTermInfo[] | undefined>(undefined);
  const [usersForDropdown, setUsersForDropdown] = useState<DropdownItem[]>([]);
  const [isUserSelectModalOpen, setIsUserSelectModalOpen] = useState(false);
  const [currentUserSelectionTargetIndex, setCurrentUserSelectionTargetIndex] = useState<number | null>(null);

  const defaultAddFormValues: Partial<CreateSupplierDto> = {
    Name: undefined,
    PersonType: undefined,
    ContactPerson: undefined,
    IdType: undefined,
    IdNumber: undefined,
    CompanyPhone: undefined,
    Address: undefined,
    ContactName: undefined,
    ContactPhone1: undefined,
    ContactPhone2: undefined,
    Email: undefined,
    AccountName: undefined,
    BankName: undefined,
    BankBranch: undefined,
    Files: [],
  };

  const fetchData = useCallback(async () => {
    setLoading(true);
    const apiParams: GetSuppliersParams = {
      UsingPaging: true,
      PageIndex: currentPage,
      NumberOfPperPage: pageSize,
      SortOrderInfos: sortField && sortOrder
        ? [{ SortField: sortField, SortOrder: sortOrder === 'ascend' ? 'asc' : 'desc' }]
        : undefined,
      SearchTermInfos: searchInfos,
    };
    try {
      const response = await supplierApi.getSuppliers(apiParams);
      console.log('API response in fetchData:', response);

      if (response.isSuccess && response.body && response.body.Detail) {
        console.log('Setting suppliers with:', response.body.Detail);
        setSuppliers(response.body.Detail);
        setTotalCount(response.body.RecordCount || 0);
      } else {
        console.log('API call was not successful or body/Detail is missing. Response:', response);
        setSuppliers([]);
        setTotalCount(0);
        if (!response.isSuccess) {
          messageApi.error(response.message || '獲取供應商列表失敗 (API)');
        }
      }
    } catch (error: any) {
      console.error('Error in fetchData:', error);
      messageApi.error(error.message || '獲取供應商列表失敗');
      setSuppliers([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, sortField, sortOrder, searchInfos, messageApi]);

  const fetchUsersForDropdown = useCallback(async () => {
    try {
      const response = await userInfoApi.getUserInfoDropdownList();
      if (response.isSuccess && response.body) {
        setUsersForDropdown(response.body);
      } else {
        messageApi.error(response.message || '獲取更新人列表失敗');
      }
    } catch (error: any) {
      messageApi.error(error.message || '獲取更新人列表時發生錯誤');
      console.error("Failed to fetch users for dropdown:", error);
    }
  }, [messageApi]);

  useEffect(() => {
    fetchData();
    fetchUsersForDropdown();
  }, [fetchData, fetchUsersForDropdown]);

  useEffect(() => {
    if (isModalOpen && modalType === 'add') {
      if (editingSupplier !== null) {
        setEditingSupplier(null);
      }
      form.setFieldsValue(defaultAddFormValues as any);
    }
  }, [isModalOpen, modalType, form, editingSupplier]);

  const columns = [
    { title: '供應商名稱', dataIndex: 'Name', key: 'Name', sorter: true, allowSearch: true },
    { title: '負責人', dataIndex: 'ContactPerson', key: 'ContactPerson', allowSearch: true },
    { title: '公司電話', dataIndex: 'CompanyPhone', key: 'CompanyPhone', allowSearch: true },
    { title: '聯絡窗口', dataIndex: 'ContactName', key: 'ContactName', allowSearch: true },
    { title: '聯絡電話', dataIndex: 'ContactPhone1', key: 'ContactPhone1', allowSearch: true },
    { title: '檔案數', dataIndex: 'FileCount', key: 'FileCount', sorter: true },
    { title: '最後更新者', dataIndex: 'UpdatedUserName', key: 'UpdatedUserName' },
    { title: '最後更新時間', dataIndex: 'UpdatedTime', key: 'UpdatedTime', sorter: true, render: (text: string) => text ? new Date(text).toLocaleDateString() : '-' },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: SupplierListItem) => (
        <Space size="middle">
          <Button type="link" icon={<EditOutlined />} onClick={() => showEditModal(record)}>編輯</Button>
          <Popconfirm
            title="確定刪除此供應商嗎？"
            onConfirm={() => handleDelete(record.SupplierId)}
            okText="確定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>刪除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handlePageChange = (page: number, pageSizeParam: number) => {
    setCurrentPage(page);
    setPageSize(pageSizeParam);
  };

  const handleSortChange = (sorter: SorterResult<SupplierListItem> | SorterResult<SupplierListItem>[]) => {
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    const newSortField = (currentSorter?.field as string) || null;
    const newSortOrder = currentSorter?.order || null;

    if (newSortField !== sortField || newSortOrder !== sortOrder) {
      setSortField(newSortField);
      setSortOrder(newSortOrder);
      if (currentPage !== 1) setCurrentPage(1);
    }
  };
  
  const handleFilterChange = (currentSearchInfos: SearchTermInfo[]) => {
    setSearchInfos(currentSearchInfos.length > 0 ? currentSearchInfos : undefined);
    if (currentPage !== 1) setCurrentPage(1);
  };

  const showAddModal = () => {
    setModalType('add');
    setEditingSupplier(null);
    setIsModalOpen(true);
  };

  const showEditModal = async (record: SupplierListItem) => {
    setModalType('edit');
    form.resetFields();
    try {
      setLoading(true);
      const detailResponse: ApiResponse<SupplierDetail> = await supplierApi.getSupplierById(record.SupplierId);
      if (detailResponse.isSuccess && detailResponse.body) {
        const supplierDetail = detailResponse.body;
        const formFilesData = supplierDetail.Files?.map(apiFile => {
          // apiFile is of type SupplierFileDetail (which extends SupplierFileBase)
          const agentId = apiFile.Agent; // User ID from API
          const selectedUser = usersForDropdown.find(u => u.Value === agentId);
          const agentNameForDisplay = selectedUser ? selectedUser.Name : (apiFile.UpdatedUserName || apiFile.CreatedUserName || '');

          return {
            // Fields from SupplierFileBase, also used in form.add()
            FormType: apiFile.FormType,
            Remark: apiFile.Remark,
            Agent: agentId, 
            AgentName: agentNameForDisplay, // User Name for display, looked up or fallback

            // Fields specific to existing files / API response
            SupplierFileId: apiFile.SupplierFileId,
            FileName: apiFile.FileName,
            FilePath: apiFile.FilePath,

            // Field for Ant Design Upload component
            File: [{ // This represents the 'uploaded' file shown in UI
              uid: String(apiFile.SupplierFileId || Date.now() + Math.random()), // Ensure UID is unique for Upload component
              name: apiFile.FileName,
              status: 'done', // Mark as already uploaded
              url: apiFile.FilePath, // Link for preview if available
            } as UploadFile],
          };
        }) || [];
        
        form.setFieldsValue({
          ...supplierDetail,
          Files: formFilesData,
        });
        setEditingSupplier(supplierDetail); 
      } else {
        messageApi.error(detailResponse.message || '獲取供應商詳細資料失敗');
        form.setFieldsValue({...record, Files: []});
        setEditingSupplier(null);
      }
    } catch (error: any) {
      messageApi.error(error.message || '獲取供應商詳細資料時發生錯誤');
      form.setFieldsValue({...record, Files: []});
      setEditingSupplier(null);
    } finally {
      setLoading(false);
      setIsModalOpen(true);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
  
      const filesToUploadForFormData: File[] = [];
      const processedFileMetadata: SupplierFileBase[] = [];

      if (values.Files && Array.isArray(values.Files)) {
        for (const fileItem of values.Files as SupplierFileBase[]) { 
          const antFile = fileItem.File && fileItem.File[0] ? fileItem.File[0] : undefined;
  
          if (antFile && antFile.originFileObj) {
            const fileToUpload = antFile.originFileObj as File;
            if (!(fileToUpload instanceof File)) {
               messageApi.error(`項目 "${antFile.name}" 的檔案物件無效。`);
               setLoading(false);
               return;
            }
            filesToUploadForFormData.push(fileToUpload);
            
            processedFileMetadata.push({
              FormType: fileItem.FormType,
              Remark: fileItem.Remark,
              Agent: fileItem.Agent,
              AgentName: fileItem.AgentName,
              FileName: fileToUpload.name,
              FilePath: fileToUpload.name,
              SupplierFileId: 0,
              File: undefined,
            });
          } else if (modalType === 'edit' && fileItem.SupplierFileId && fileItem.SupplierFileId > 0) {
            processedFileMetadata.push({
              FormType: fileItem.FormType,
              FileName: fileItem.FileName,
              FilePath: fileItem.FilePath,
              Remark: fileItem.Remark,
              Agent: fileItem.Agent,
              AgentName: fileItem.AgentName,
              SupplierFileId: fileItem.SupplierFileId,
              File: undefined,
            });
          }
        }
      }
  
      const submissionPayloadDto = {
        ...values,
        Files: processedFileMetadata,
      };
  
      let response: ApiResponse<any>;
      if (modalType === 'add') {
        response = await supplierApi.createSupplier(submissionPayloadDto as CreateSupplierDto, filesToUploadForFormData);
      } else if (editingSupplier && editingSupplier.SupplierId) {
        response = await supplierApi.updateSupplier(editingSupplier.SupplierId, submissionPayloadDto as UpdateSupplierDto, filesToUploadForFormData);
      } else {
        setLoading(false);
        throw new Error('無法執行操作：供應商資料不完整');
      }
  
      if (response.isSuccess) {
        messageApi.success(modalType === 'add' ? '供應商新增成功' : '供應商更新成功');
        setIsModalOpen(false);
        fetchData(); 
      } else {
        messageApi.error(response.message || (modalType === 'add' ? '供應商新增失敗' : '供應商更新失敗'));
      }
    } catch (info: any) {
      if (info.errorFields) {
        messageApi.error('表單驗證失敗，請檢查輸入內容。');
      } else {
        console.error('Operation Failed Details:', info);
        messageApi.error(info.message || '操作失敗，請檢查控制台獲取更多資訊。');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (supplierId: number) => {
    setLoading(true);
    try {
      const response = await supplierApi.deleteSupplier(supplierId);
      if (response.isSuccess) {
        messageApi.success('供應商刪除成功');
        fetchData();
      } else {
        messageApi.error(response.message || '供應商刪除失敗');
      }
    } catch (error: any) {
      messageApi.error(error.message || '刪除過程中發生錯誤');
    } finally {
      setLoading(false);
    }
  };

  const supplierFormInitialValues = modalType === 'add' 
    ? { Files: [] }
    : editingSupplier;

  const handleOpenUserSelectModal = (index: number) => {
    setCurrentUserSelectionTargetIndex(index);
    setIsUserSelectModalOpen(true);
  };

  const handleUserSelect = (selectedUser: DropdownItem) => {
    if (currentUserSelectionTargetIndex !== null && selectedUser) {
      const currentFiles = form.getFieldValue('Files') || [];
      const updatedFiles = [...currentFiles];
      if (updatedFiles[currentUserSelectionTargetIndex]) {
        updatedFiles[currentUserSelectionTargetIndex] = {
          ...updatedFiles[currentUserSelectionTargetIndex],
          Agent: selectedUser.Value,
          AgentName: selectedUser.Name,
        };
        form.setFieldsValue({ Files: updatedFiles });
      }
    }
    setIsUserSelectModalOpen(false);
    setCurrentUserSelectionTargetIndex(null);
  };

  const renderSupplierForm = (initialVals?: typeof supplierFormInitialValues) => {
    const filesWatcher = Form.useWatch('Files', form);
    return (
      <Form form={form} layout="vertical" initialValues={initialVals || { Files: [] }}>
        <h3 className="text-md font-semibold mb-3 border-b pb-1">基本資料</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-1 mb-4">
          <Form.Item label="供應商名稱" name="Name" rules={[{ required: true, message: '請輸入供應商名稱' }]}><Input /></Form.Item>
          <Form.Item label="人別" name="PersonType"><Select placeholder="請選擇" options={[{ label: '法人', value: 'L' }, { label: '自然人', value: 'P' }]} /></Form.Item>
          <Form.Item label="負責人" name="ContactPerson"><Input /></Form.Item> 
          <Form.Item label="證號類型" name="IdType"><Select placeholder="請選擇" options={[{ label: '統一編號', value: 'UNIFORM' }, { label: '身分證號', value: 'ID' }, {label: '其他', value: 'OTHER'}]} /></Form.Item>
          <Form.Item label="證號" name="IdNumber"><Input placeholder="請選擇左側證號類型" /></Form.Item>
          <Form.Item label="公司電話" name="CompanyPhone"><Input /></Form.Item>
          <Form.Item label="通訊地址" name="Address" className="md:col-span-2"><Input /></Form.Item>
          <Form.Item label="聯絡窗口" name="ContactName"><Input /></Form.Item>
          <Form.Item label="聯絡電話 1" name="ContactPhone1"><Input /></Form.Item>
          <Form.Item label="聯絡電話 2" name="ContactPhone2"><Input /></Form.Item>
          <Form.Item label="Email" name="Email" rules={[{type: 'email', message: '請輸入有效的Email格式'}]}><Input type="email" /></Form.Item>
        </div>

        <h3 className="text-md font-semibold mb-3 border-b pb-1">B to B 資料</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-1 mb-4">
          <Form.Item label="收款戶名" name="AccountName"><Input /></Form.Item>
          <Form.Item label="所在銀行" name="BankName"><Input /></Form.Item>
          <Form.Item label="分行" name="BankBranch"><Input /></Form.Item>
        </div>

        <h3 className="text-md font-semibold my-3 border-b pb-1">相關檔案</h3>
        <Form.List name="Files">
          {(fields, { add, remove }) => (
            <>
              {fields.map((field, index) => (
                <div key={field.key} className="grid grid-cols-1 md:grid-cols-12 gap-x-4 gap-y-1 items-start mb-2 border p-3 rounded-md relative">
                  <Form.Item
                    label="檔案類型"
                    name={[field.name, 'FormType']}
                    rules={[{ required: true, message: '請選擇檔案類型' }]}
                    className="md:col-span-3"
                  >
                    <Select
                      placeholder="請選擇"
                      options={formTypeOptions}
                      fieldNames={{ label: 'Name', value: 'Value' }}
                      showSearch
                      filterOption={(input, option) =>
                        (option?.Name ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    label="檔案上傳"
                    name={[field.name, 'File']}
                    valuePropName="fileList"
                    getValueFromEvent={(e) => { if (Array.isArray(e)) return e; return e && e.fileList; }}
                    className="md:col-span-3"
                  >
                    <Upload 
                      beforeUpload={() => false}
                      maxCount={1}
                    >
                      <Button icon={<PlusOutlined />}>選擇檔案</Button>
                    </Upload>
                  </Form.Item>
                  <Form.Item
                    label="檔案備註"
                    name={[field.name, 'Remark']}
                    className="md:col-span-3"
                  >
                    <Input.TextArea placeholder="備註" rows={1} />
                  </Form.Item>
                  <Form.Item
                    label="更新人"
                    className="md:col-span-2"
                  >
                    <Button onClick={() => handleOpenUserSelectModal(index)}>
                      {(filesWatcher && filesWatcher[index] && filesWatcher[index].AgentName) || '選擇更新人'}
                    </Button>
                  </Form.Item>
                  <Form.Item name={[field.name, 'Agent']} hidden><Input /></Form.Item>
                  <Form.Item name={[field.name, 'AgentName']} hidden><Input /></Form.Item>
                  <Form.Item name={[field.name, 'FileName']} hidden><Input /></Form.Item>
                  <Form.Item name={[field.name, 'FilePath']} hidden><Input /></Form.Item>
                  <Form.Item name={[field.name, 'SupplierFileId']} hidden><Input /></Form.Item>

                  <div className="md:col-span-1 flex items-center justify-end">
                    <DeleteOutlined onClick={() => remove(field.name)} style={{color: 'red', cursor: 'pointer', fontSize: '16px'}}/>
                  </div>
                </div>
              ))}
              <Form.Item>
                <Button 
                  type="dashed" 
                  onClick={() => add({ 
                    FormType: undefined, 
                    File: [],
                    Remark: '', 
                    Agent: undefined,
                    AgentName: undefined,
                    FileName: undefined,
                    FilePath: undefined,
                    SupplierFileId: undefined,
                  })} 
                  block 
                  icon={<PlusOutlined />}
                >
                  新增檔案
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>
      </Form>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      {contextHolder}
      <div className="mb-4">
        <Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>
          新增供應商
        </Button>
      </div>

      {(() => {
        console.log('Rendering DataTable, suppliers state:', suppliers, 'Total count:', totalCount, 'Loading:', loading);
        return null;
      })()}
      <DataTable<SupplierListItem>
        columns={columns}
        dataSource={suppliers}
        loading={loading}
        rowKey="SupplierId"
        total={totalCount}
        currentPage={currentPage}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onSort={handleSortChange}
        onFilter={handleFilterChange}
      />

      <AntdModal
        title={modalType === 'add' ? '新增供應商' : '編輯供應商'}
        open={isModalOpen}
        onOk={handleSubmit}
        onCancel={handleCancel}
        confirmLoading={loading}
        destroyOnClose 
        width={800}
        styles={{ body: { maxHeight: '60vh', overflowY: 'auto' } }}
      >
        {renderSupplierForm(editingSupplier || undefined)}
      </AntdModal>

      <TableSelectModal
        isOpen={isUserSelectModalOpen}
        onClose={() => setIsUserSelectModalOpen(false)}
        onSelect={handleUserSelect}
        data={usersForDropdown}
        columns={[{ title: '使用者名稱', dataIndex: 'Name', key: 'Name' }, { title: '使用者ID', dataIndex: 'Value', key: 'Value' }] as any}
        rowKey="Value"
        title="選擇更新人"
      />
    </div>
  );
} 