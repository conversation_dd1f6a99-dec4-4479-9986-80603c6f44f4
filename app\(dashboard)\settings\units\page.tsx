'use client';

import React, { useState, useEffect } from 'react';
import { ConfigProvider, Tag, Space, Popconfirm, message, Select, Input, Button } from 'antd';
import { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import zhTW from 'antd/lib/locale/zh_TW';
import Modal from '@/app/components/common/Modal'; // 引入 Modal
import DataTable from '@/app/components/DataTable'; // 引入 DataTable
import { departmentApi, DepartmentListItem, DepartmentListParams, CreateDepartmentDto, UpdateDepartmentDto } from '@/app/services/api/departmentApi'; // 引入 Department API
import { userInfoApi } from '@/app/services/api/userInfoApi'; // 用於獲取公司下拉
import { BaseQueryParams, DropdownItem, SortOrderInfo } from '@/app/interfaces/dto/common.dto'; // 引入通用類型

// --- 修改：DepartmentFormState 使用完整類型 --- 
interface DepartmentFormState {
  CompanyId?: string; // 公司 ID 在新增時是可選的，但在編輯時從 selectedDepartment 載入
  DepartmentId?: string; // 部門 ID 在新增時是可選的，但在編輯時從 selectedDepartment 載入
  Name?: string; // 名稱總是需要的
}
// --- 結束修改 ---

export default function UnitSettingsPage() {
  // --- 狀態管理 ---
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<DepartmentListItem | null>(null);
  const [departments, setDepartments] = useState<DepartmentListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [messageApi, contextHolder] = message.useMessage();

  const [companies, setCompanies] = useState<DropdownItem[]>([]); // 公司下拉列表

  const [formData, setFormData] = useState<DepartmentFormState>({ CompanyId: '', DepartmentId: '', Name: '' }); // 初始化為空字串

  // 排序狀態
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(null);
  // --- 結束狀態管理 ---

  // --- 表格列定義修改 ---
  const columns = [
    { 
      title: '公司名稱',
      dataIndex: 'CompanyName', // <-- 使用 CompanyName
      key: 'CompanyName', // <-- 使用 CompanyName
      sorter: true, 
      allowSearch: true, // <-- 啟用表頭搜索
    },
    { title: '部門代碼', dataIndex: 'DepartmentId', key: 'DepartmentId', sorter: true, allowSearch: true },
    { title: '部門名稱', dataIndex: 'Name', key: 'Name', sorter: true, allowSearch: true },
    { title: '建立時間', dataIndex: 'CreatedTime', key: 'CreatedTime', sorter: true, allowSearch: false, render: (text: string) => new Date(text).toLocaleString('zh-TW') },
    { title: '建立人員', dataIndex: 'CreatedUserName', key: 'CreatedUserName', sorter: true, allowSearch: true },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: DepartmentListItem) => (
        <Space size="middle">
          <Button
            type="link"
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定要刪除此部門嗎？"
            description="刪除後可能無法恢復。"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button type="link" danger>
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
      sorter: false,
      allowSearch: false,
    },
  ];
  // --- 結束表格列定義修改 ---

  // --- API 相關函數修改 ---
  const fetchDepartments = async (page = 1, size = pageSize, searchInfos?: {SearchField: string, SearchValue: string}[], currentSortField = sortField, currentSortOrder = sortOrder) => {
    setLoading(true);
    try {
      const params: DepartmentListParams = {
         UsingPaging: true,
         PageIndex: page,
         NumberOfPperPage: size,
      };
      if (searchInfos && searchInfos.length > 0) {
        params.SearchTermInfos = searchInfos;
      }
      if (currentSortField && currentSortOrder) {
        params.SortOrderInfos = [{
           SortField: currentSortField,
           SortOrder: currentSortOrder === 'ascend' ? 'asc' : 'desc'
         }];
      }
      
      const response = await departmentApi.getDepartmentList(params);
      if (response.isSuccess && response.body) {
        setDepartments(response.body.Detail || []);
        setTotal(response.body.RecordCount || 0);
        setCurrentPage(response.body.PageIndex || 1);
        setPageSize(response.body.NumberOfPperPage || 10);
        setSortField(currentSortField);
        setSortOrder(currentSortOrder);
      } else {
        messageApi.error(response.message || '獲取部門列表失敗');
        setDepartments([]);
        setTotal(0);
      }
    } catch (error) {
      console.error('獲取部門列表失敗:', error);
      messageApi.error('獲取部門列表失敗');
      setDepartments([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  const fetchCompanies = async () => {
    try {
      const response = await userInfoApi.getCompanyDropdownList();
      if (response.isSuccess && response.body) {
        setCompanies(response.body);
      } else {
         messageApi.error(response.message || '獲取公司列表失敗');
      }
    } catch (error) {
      console.error("獲取公司列表失敗:", error);
      messageApi.error('獲取公司列表失敗');
    }
  };
  // --- 結束 API 相關函數修改 ---

  // --- 事件處理函數修改 ---
  const handlePageChange = (page: number, size?: number) => {
    const newPageSize = size || pageSize;
    if (page !== currentPage || newPageSize !== pageSize) {
      fetchDepartments(page, newPageSize, undefined, sortField, sortOrder);
    }
  };

  const handleSort = (sorter: any) => {
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    const newSortField = currentSorter?.field as string || null;
    const newSortOrder = currentSorter?.order as ('ascend' | 'descend' | null) || null;
    
    if (newSortField !== sortField || newSortOrder !== sortOrder) {
      setSortField(newSortField);
      setSortOrder(newSortOrder);
      fetchDepartments(1, pageSize, undefined, newSortField, newSortOrder);
    }
  };

  const handleFilter = (searchInfos: { SearchField: string; SearchValue: string }[]) => {
    fetchDepartments(1, pageSize, searchInfos, sortField, sortOrder);
  };

  const handleAdd = () => {
    setSelectedDepartment(null);
    setFormData({ CompanyId: '', DepartmentId: '', Name: '' }); // 清空表單
    setIsEditMode(false);
    setIsModalOpen(true);
  };

  const handleEdit = (department: DepartmentListItem) => {
    setSelectedDepartment(department);
    setFormData({ ...department }); // 載入編輯資料
    setIsEditMode(true);
    setIsModalOpen(true);
  };

  const handleDelete = async (department: DepartmentListItem) => {
    setLoading(true);
    try {
      const response = await departmentApi.deleteDepartment(department.CompanyId, department.DepartmentId);
      if (response.isSuccess) {
        messageApi.success('刪除部門成功');
        // 重新獲取當前頁數據，或進行前端刪除優化
        const newTotal = total - 1;
        const newTotalPages = Math.ceil(newTotal / pageSize);
        const newCurrentPage = currentPage > newTotalPages ? Math.max(1, newTotalPages) : currentPage;
        fetchDepartments(newCurrentPage, pageSize, undefined, sortField, sortOrder);
      } else {
        messageApi.error(response.message || '刪除部門失敗');
        setLoading(false); // 失敗時停止 loading
      }
    } catch (error) {
      console.error('刪除部門失敗:', error);
      messageApi.error('刪除部門失敗');
      setLoading(false); // 出錯時停止 loading
    }
    // 成功時 loading 會在 fetchDepartments 結束後自動變 false
  };

  const handleModalSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    if (!formData.CompanyId || !formData.DepartmentId || !formData.Name?.trim()) {
      messageApi.error('公司、部門代碼和部門名稱為必填項');
      setLoading(false);
      return;
    }

    try {
      let response;
      if (isEditMode && selectedDepartment) {
        const updateData: UpdateDepartmentDto = {
          CompanyId: selectedDepartment.CompanyId,
          DepartmentId: selectedDepartment.DepartmentId,
          Name: formData.Name.trim()
        };
        response = await departmentApi.updateDepartment(selectedDepartment.CompanyId, selectedDepartment.DepartmentId, updateData);
      } else {
        const createData: CreateDepartmentDto = {
          CompanyId: formData.CompanyId,
          DepartmentId: formData.DepartmentId,
          Name: formData.Name.trim(),
        };
        response = await departmentApi.createDepartment(createData);
      }
      
      if (response.isSuccess) {
        messageApi.success(isEditMode ? '更新部門成功' : '新增部門成功');
        setIsModalOpen(false);
        setFormData({ CompanyId: '', DepartmentId: '', Name: '' }); 
        fetchDepartments(isEditMode ? currentPage : 1, pageSize, undefined, sortField, sortOrder);
      } else {
        messageApi.error(response.message || (isEditMode ? '更新部門失敗' : '新增部門失敗'));
        setLoading(false); 
      }
    } catch (error) {
      console.error(isEditMode ? '更新部門失敗:' : '新增部門失敗:', error);
      messageApi.error(isEditMode ? '更新部門失敗' : '新增部門失敗');
      setLoading(false); 
    }
  };

  const handleModalInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleModalSelectChange = (value: string, fieldName: string) => {
    setFormData(prev => ({ ...prev, [fieldName]: value }));
  };
  // --- 結束事件處理函數修改 ---

  // --- 初始數據加載修改 ---
  useEffect(() => {
    fetchCompanies();
    fetchDepartments(currentPage, pageSize, undefined, sortField, sortOrder);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 初始加載
  // --- 結束初始數據加載修改 ---

  return (
    <ConfigProvider locale={zhTW}>
      {contextHolder}
      <div className="p-4 bg-white rounded-lg shadow">
        {/* --- 修改：將 justify-end 改為 justify-start --- */}
        <div className="mb-4 flex justify-start items-center"> 
          {/* 新增按鈕 */}
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新增單位
          </Button>
        </div>
        {/* --- 結束頂部控制區域修改 --- */}

        {/* --- 數據表格 --- */}
        <DataTable
          columns={columns}
          dataSource={departments}
          loading={loading}
          total={total}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onSort={handleSort}
          onFilter={handleFilter}
          rowKey="DepartmentId" 
        />
        {/* --- 結束數據表格 --- */}

        {/* --- 新增/編輯彈窗 --- */}
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title={isEditMode ? "編輯部門" : "新增部門"}
        >
          <form onSubmit={handleModalSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">公司 <span className="text-red-500">*</span></label>
              <Select
                placeholder="選擇公司"
                className="w-full"
                value={formData.CompanyId || undefined} // 確保 value 為 string 或 undefined
                onChange={(value) => handleModalSelectChange(value, 'CompanyId')}
                options={companies.map(c => ({ value: c.Value, label: c.Name }))}
                disabled={isEditMode}
                size="large"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">部門代碼 <span className="text-red-500">*</span></label>
              <Input
                name="DepartmentId"
                value={formData.DepartmentId || ''}
                onChange={handleModalInputChange}
                required // Input 可以有 required
                disabled={isEditMode}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">部門名稱 <span className="text-red-500">*</span></label>
              <Input
                name="Name"
                value={formData.Name || ''}
                onChange={handleModalInputChange}
                required // Input 可以有 required
              />
            </div>
            
            {/* 提交按鈕區域 */}
            <div className="flex justify-end space-x-4 pt-4 border-t mt-6">
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                disabled={loading} // 提交中禁用
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600 disabled:opacity-50"
                disabled={loading} // 提交中禁用
              >
                {loading ? '提交中...' : '確認'}
              </button>
            </div>
          </form>
        </Modal>
        {/* --- 結束新增/編輯彈窗 --- */}
      </div>
    </ConfigProvider>
  );
} 