'use client';

import React, { useState, useCallback } from 'react';
import { Table, ConfigProvider, Input } from 'antd';
import type { ColumnsType, TablePaginationConfig, ColumnType } from 'antd/es/table';
import type { FilterValue, SorterResult, TableCurrentDataSource } from 'antd/es/table/interface';
import { SearchOutlined } from '@ant-design/icons';
import zhTW from 'antd/locale/zh_TW';
import debounce from 'lodash/debounce';

export interface SearchInfo {
  SearchField: string;
  SearchValue: string;
}

interface ColumnConfig {
  allowSearch?: boolean;
}

export type ExtendedColumnType<T> = ColumnType<T> & ColumnConfig;

interface DataTableProps<T> {
  columns: ExtendedColumnType<T>[];
  dataSource: T[];
  loading?: boolean;
  total: number;
  currentPage: number;
  pageSize: number;
  pageSizeOptions?: string[];
  onPageChange: (page: number, pageSize: number) => void;
  onSort?: (sorter: SorterResult<T> | SorterResult<T>[]) => void;
  onFilter?: (searchInfos: SearchInfo[]) => void;
  onAntdFilterChange?: (filters: Record<string, FilterValue | null>) => void;
  showSizeChanger?: boolean;
  showTotal?: (total: number, range: [number, number]) => React.ReactNode;
  rowKey?: string | ((record: T) => string | number);
  debounceTime?: number;
  className?: string;
}

export default function DataTable<T extends object>({
  columns,
  dataSource,
  loading = false,
  total,
  currentPage,
  pageSize,
  pageSizeOptions = ['10', '25', '50', '100'],
  onPageChange,
  onSort,
  onFilter,
  onAntdFilterChange,
  showSizeChanger = true,
  showTotal = (total, range) => `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
  rowKey = 'id',
  debounceTime = 1000,
  className
}: DataTableProps<T>) {
  const [searchValues, setSearchValues] = useState<Record<string, string>>({});

  const debouncedFilter = useCallback(
    debounce((searchInfos: SearchInfo[]) => {
      console.log('DataTable debouncedFilter 觸發:', searchInfos);
      onFilter?.(searchInfos);
    }, debounceTime),
    [onFilter, debounceTime]
  );

  const columnsWithFilters = columns.map(column => {
    if (column.key && column.allowSearch === true) {
      return {
        ...column,
        filterDropdown: ({ close }: { close: () => void }) => (
          <div className="p-2">
            <Input
              placeholder={`搜尋 ${column.title}`}
              value={searchValues[column.key as string] || ''}
              onChange={e => {
                console.log('DataTable Input onChange 觸發:', column.key, e.target.value);
                const newSearchValues = {
                  ...searchValues,
                  [column.key as string]: e.target.value
                };
                setSearchValues(newSearchValues);
                
                const searchInfos: SearchInfo[] = Object.entries(newSearchValues)
                  .filter(([_, value]) => value)
                  .map(([field, value]) => ({
                    SearchField: field,
                    SearchValue: value
                  }));
                
                console.log('準備呼叫 debouncedFilter:', searchInfos);
                debouncedFilter(searchInfos);
              }}
              onPressEnter={() => {
                const searchInfos: SearchInfo[] = Object.entries(searchValues)
                  .filter(([_, value]) => value)
                  .map(([field, value]) => ({
                    SearchField: field,
                    SearchValue: value
                  }));
                onFilter?.(searchInfos);
                close();
              }}
              style={{ width: 188, marginBottom: 8, display: 'block' }}
            />
          </div>
        ),
        filterIcon: (filtered: boolean) => (
          <SearchOutlined style={{ color: searchValues[column.key as string] ? '#1890ff' : undefined }} />
        )
      };
    }
    return column;
  });

  const handleChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<T> | SorterResult<T>[],
    _: TableCurrentDataSource<T>
  ) => {
    if (pagination.current && pagination.pageSize) {
      onPageChange(pagination.current, pagination.pageSize);
    }
    if (onSort) {
      onSort(sorter);
    }
    if (onAntdFilterChange) {
      onAntdFilterChange(filters);
    }
  };

  return (
    <ConfigProvider locale={zhTW}>
      <div className={`mt-2 rounded-lg overflow-hidden ${className || ''}`}>
        <div className="px-6">
          <Table
            columns={columnsWithFilters}
            dataSource={dataSource}
            loading={loading}
            pagination={{
              total,
              current: currentPage,
              pageSize,
              showSizeChanger,
              showTotal,
              pageSizeOptions,
              position: ['bottomCenter'],
              className: 'flex items-center justify-center py-4'
            }}
            onChange={handleChange}
            scroll={{ x: 'max-content' }}
            rowKey={rowKey}
          />
        </div>
      </div>
    </ConfigProvider>
  );
} 