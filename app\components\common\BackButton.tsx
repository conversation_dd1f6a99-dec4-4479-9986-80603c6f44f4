'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';

interface BackButtonProps {
  /** 返回的目標路徑，如果不提供則使用 router.back() */
  to?: string;
  /** 按鈕文字 */
  text?: string;
  /** 按鈕類型 */
  type?: 'default' | 'primary' | 'dashed' | 'link' | 'text';
  /** 是否顯示圖標 */
  showIcon?: boolean;
  /** 自定義樣式類名 */
  className?: string;
}

export default function BackButton({ 
  to, 
  text = '返回上一頁', 
  type = 'default',
  showIcon = true,
  className = ''
}: BackButtonProps) {
  const router = useRouter();

  const handleBack = () => {
    if (to) {
      router.push(to);
    } else {
      router.back();
    }
  };

  return (
    <Button
      type={type}
      icon={showIcon ? <ArrowLeftOutlined /> : undefined}
      onClick={handleBack}
      className={className}
    >
      {text}
    </Button>
  );
}
