'use client';

import React, { useEffect, useRef } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line, Bar, Pie, Doughnut, Radar, PolarArea, B<PERSON>ble, Scatter } from 'react-chartjs-2';
import { ChartProps, ChartType } from '../../interfaces/dto/chart.dto';
import { Button } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';

// 註冊 ChartJS 組件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend,
  Filler
);

// 默認圖表選項
const defaultOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
    title: {
      display: false,
      text: '',
    },
  },
};

const Chart: React.FC<ChartProps> = ({
  type,
  data,
  options = {},
  width = 400,
  height = 300,
  className = '',
  showDownload = false,
  downloadFileName = 'chart',
}) => {
  // 合併選項
  const mergedOptions = {
    ...defaultOptions,
    ...options,
  };

  // 圖表容器引用
  const chartRef = useRef<HTMLDivElement>(null);

  // 根據類型渲染不同的圖表
  const renderChart = () => {
    switch (type) {
      case ChartType.LINE:
        return <Line data={data} options={mergedOptions} />;
      case ChartType.BAR:
        return <Bar data={data} options={mergedOptions} />;
      case ChartType.PIE:
        return <Pie data={data} options={mergedOptions} />;
      case ChartType.DOUGHNUT:
        return <Doughnut data={data} options={mergedOptions} />;
      case ChartType.RADAR:
        return <Radar data={data} options={mergedOptions} />;
      case ChartType.POLAR_AREA:
        return <PolarArea data={data} options={mergedOptions} />;
      case ChartType.BUBBLE:
        return <Bubble data={data} options={mergedOptions} />;
      case ChartType.SCATTER:
        return <Scatter data={data} options={mergedOptions} />;
      case ChartType.AREA:
        // 面積圖為 Line 圖，帶填充
        return (
          <Line
            data={data}
            options={{
              ...mergedOptions,
              elements: { line: { fill: true } },
            }}
          />
        );
      case ChartType.HORIZONTAL_BAR:
        // 橫向柱狀圖
        return (
          <Bar
            data={data}
            options={{
              ...mergedOptions,
              indexAxis: 'y' as const,
            }}
          />
        );
      default:
        return <div>不支持的圖表類型: {type}</div>;
    }
  };

  return (
    <div>
    <div
      ref={chartRef}
      className={`chart-container ${className}`}
      style={{ width, height }}
    >
      {renderChart()}
      </div>
      {showDownload && (
        <div style={{ textAlign: 'right', marginTop: 8 }}>
          <Button
            size="small"
            icon={<DownloadOutlined />}
            onClick={() => {
              const canvas = chartRef.current?.querySelector('canvas');
              if (canvas) {
                const url = (canvas as HTMLCanvasElement).toDataURL('image/png');
                const link = document.createElement('a');
                link.href = url;
                link.download = `${downloadFileName}.png`;
                link.click();
              }
            }}
          >
            下載圖片
          </Button>
        </div>
      )}
    </div>
  );
};

export default Chart; 