'use client';

import React from 'react';
import Chart from './Chart';
import { ChartProps } from '../../interfaces/dto/chart.dto';

interface ChartCardProps extends ChartProps {
  title: string;
  subtitle?: string;
  footer?: React.ReactNode;
  cardClassName?: string;
  headerClassName?: string;
  bodyClassName?: string;
  footerClassName?: string;
}

const ChartCard: React.FC<ChartCardProps> = ({
  title,
  subtitle,
  footer,
  cardClassName = '',
  headerClassName = '',
  bodyClassName = '',
  footerClassName = '',
  ...chartProps
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden ${cardClassName}`}>
      <div className={`p-4 border-b ${headerClassName}`}>
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
        {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
      </div>
      
      <div className={`p-4 ${bodyClassName}`}>
        <Chart {...chartProps} />
      </div>
      
      {footer && (
        <div className={`p-4 border-t bg-gray-50 ${footerClassName}`}>
          {footer}
        </div>
      )}
    </div>
  );
};

export default ChartCard; 