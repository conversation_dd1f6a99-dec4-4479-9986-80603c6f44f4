'use client';
import React, { useRef } from 'react';
import * as echarts from 'echarts';
import 'echarts-gl';
import * as ecStat from 'echarts-stat';
// 僅在 transform 存在時註冊
if ((ecStat as any).transform?.boxplot) {
  echarts.registerTransform((ecStat as any).transform.boxplot);
}
import ReactECharts from 'echarts-for-react';
import { EChartProps } from '../../interfaces/dto/echart.dto';
import { Button } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';

const EChart: React.FC<EChartProps> = ({ option, style, showDownload = false, downloadFileName = 'echart' }) => {
  const chartRef = useRef<ReactECharts>(null);

  const handleDownload = () => {
    const instance = chartRef.current?.getEchartsInstance();
    if (instance) {
      const url = instance.getDataURL({ type: 'png', pixelRatio: 2, backgroundColor: '#fff' });
      const link = document.createElement('a');
      link.href = url;
      link.download = `${downloadFileName}.png`;
      link.click();
    }
  };

  return (
    <div>
      <ReactECharts echarts={echarts} ref={chartRef} option={option} style={style} />
      {showDownload && (
        <div style={{ textAlign: 'right', marginTop: 8 }}>
          <Button size="small" icon={<DownloadOutlined />} onClick={handleDownload}>
            下載圖片
          </Button>
        </div>
      )}
    </div>
  );
};

export default EChart; 