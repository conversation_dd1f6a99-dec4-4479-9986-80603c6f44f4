'use client';

import React, { useRef, useState } from 'react';
import SignatureCanvas from 'react-signature-canvas';

interface SignaturePadProps {
  onSave: (signature: string) => void;
  width?: number;
  height?: number;
}

const SignaturePad: React.FC<SignaturePadProps> = ({
  onSave,
  width = 500,
  height = 200
}) => {
  const sigCanvas = useRef<any>(null);
  const [isEmpty, setIsEmpty] = useState(true);

  const clear = () => {
    sigCanvas.current?.clear();
    setIsEmpty(true);
  };

  const save = () => {
    if (sigCanvas.current && !sigCanvas.current.isEmpty()) {
      const dataUrl = sigCanvas.current?.getTrimmedCanvas().toDataURL('image/png');
      onSave(dataUrl);
      setIsEmpty(false);
    }
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <div className="border border-gray-300 rounded-lg overflow-hidden">
        <SignatureCanvas
          ref={sigCanvas}
          canvasProps={{
            width,
            height,
            className: 'bg-white'
          }}
          onBegin={() => setIsEmpty(false)}
          onEnd={save}
        />
      </div>
      <div className="flex gap-4">
        <button
          type="button"
          onClick={clear}
          className="px-4 py-2 text-sm bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200"
        >
          清除
        </button>
      </div>
    </div>
  );
};

export default SignaturePad; 