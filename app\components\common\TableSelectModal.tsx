import React, { useState, useEffect, useMemo } from 'react';
import { X } from 'lucide-react';
import { Modal, Input, Button, ConfigProvider, Table } from 'antd';
import zhTW from 'antd/lib/locale/zh_TW';
import { TableRowSelection } from 'antd/es/table/interface';

interface Column {
  key: string;
  title: string;
}

interface TableSelectModalProps<T> {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (items: T | T[]) => void;
  title: string;
  data: T[];
  columns: Column[];
  rowKey: string;
  isMultiSelect?: boolean;
  initialSelectedValues?: string[];
  searchKeys?: string[];
  zIndex?: number;
}

const STABLE_EMPTY_ARRAY: string[] = []; // Define a stable empty array

export default function TableSelectModal<T extends Record<string, any>>({
  isOpen,
  onClose,
  onSelect,
  title,
  data,
  columns,
  rowKey,
  isMultiSelect = false,
  initialSelectedValues = STABLE_EMPTY_ARRAY, // Use the stable empty array as default
  searchKeys = columns.map(col => col.key),
  zIndex
}: TableSelectModalProps<T>) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRowKeysState, setSelectedRowKeysState] = useState<Set<string>>(new Set(initialSelectedValues));

  useEffect(() => {
    if (isOpen) {
      setSelectedRowKeysState(new Set(initialSelectedValues));
      setSearchTerm('');
    } else {
      // Optional: Reset search term when modal closes if needed
      // setSearchTerm(''); 
    }
  }, [isOpen, initialSelectedValues]);

  const filteredData = useMemo(() => {
    if (!searchTerm.trim()) {
      return data;
    }
    const lowerSearchTerm = searchTerm.toLowerCase();
    const result = data.filter(item => {
      const isMatch = searchKeys.some(key => {
        const value = item[key];
        const stringValue = String(value).toLowerCase();
        const includes = stringValue.includes(lowerSearchTerm);
        return value != null && includes;
      });
      return isMatch;
    });
    return result;
  }, [data, searchTerm, searchKeys]);

  const handleConfirm = () => {
    const selectedItems = data.filter(item => selectedRowKeysState.has(String(item[rowKey])));
    onSelect(isMultiSelect ? selectedItems : selectedItems[0]);
    onClose();
  };

  const rowSelection: TableRowSelection<T> = {
    type: isMultiSelect ? 'checkbox' : 'radio',
    selectedRowKeys: Array.from(selectedRowKeysState),
    onChange: (selectedKeys: React.Key[]) => {
      setSelectedRowKeysState(new Set(selectedKeys.map(String)));
    },
  };

  const antdColumns = columns.map(col => ({
    title: col.title,
    dataIndex: col.key,
    key: col.key,
  }));

  return (
    <ConfigProvider locale={zhTW}> 
      <Modal
        title={title}
        open={isOpen}
        onCancel={onClose}
        onOk={isMultiSelect ? handleConfirm : undefined}
        confirmLoading={false}
        width={800}
        zIndex={zIndex ?? 1050}
        destroyOnClose
        getContainer={() => document.body}
        footer={(
          <div className="flex justify-end">
            <Button key="back" onClick={onClose}>
              取消
            </Button>
            {isMultiSelect && (
              <Button 
                key="submit" 
                type="primary" 
                onClick={handleConfirm}
                disabled={selectedRowKeysState.size === 0}
              >
                確認
              </Button>
            )}
          </div>
        )}
        styles={{ body: { maxHeight: '60vh', overflowY: 'auto' } }}
      >
        <div className="mb-4">
          <Input
            placeholder="搜尋..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            allowClear
          />
        </div>

        <Table
          rowKey={rowKey}
          dataSource={filteredData}
          columns={antdColumns}
          rowSelection={isMultiSelect ? rowSelection : undefined}
          pagination={false}
          scroll={{ y: 350 }}
          size="small"
          onRow={(record) => ({
            onClick: () => {
              if (!isMultiSelect) {
                const key = String(record[rowKey]);
                setSelectedRowKeysState(new Set([key]));
                onSelect(record);
                onClose();
              }
            },
          })}
          locale={{ emptyText: '找不到符合條件的資料' }}
        />
      </Modal>
    </ConfigProvider>
  );
} 