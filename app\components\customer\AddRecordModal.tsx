'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Modal, Form, Input, Select, Button, message, ConfigProvider } from 'antd';
import zhTW from 'antd/lib/locale/zh_TW';
import moment from 'moment';
import 'moment/locale/zh-tw';
moment.locale('zh-tw');
import { DropdownItem } from '../../interfaces/dto/common.dto';
import { UserOutlined, PlusOutlined } from '@ant-design/icons';
import TableSelectModal from '../common/TableSelectModal';

// Re-use the types from EditModal if they are defined in a shared place, otherwise define them here
interface NewRecordInput {
  RecordType: string;
  Notes: string;
  CustomerLevel: string;
  RecordedAt?: string; // string for <input type="date">
  HandledBy?: string; 
}

// Define options locally or import if shared
const recordTypeOptions = [
  { value: "來電", label: "來電" },
  { value: "來人", label: "來人" },
  { value: "電訪", label: "電訪" },
  { value: "回訪", label: "回訪" },
];

const customerLevelOptions = [
  { value: "A", label: "A" },
  { value: "B", label: "B" },
  { value: "C", label: "C" },
  { value: "D", label: "D" },
];

const initialNewRecordInput: NewRecordInput = {
  RecordType: '',
  Notes: '',
  CustomerLevel: '',
  RecordedAt: moment().format('YYYY-MM-DD'), // Default to today as string
  HandledBy: undefined, 
};

interface AddRecordModalProps {
  open: boolean;
  onClose: () => void;
  onOk: (recordData: NewRecordInput) => void;
  findUserName: (userId: string | undefined | null) => string; // Pass function to find name
  onSelectUserClick: () => void; // Function to tell parent to open user select modal
  selectedUserId: string | undefined; // Currently selected user ID from parent
}

export default function AddRecordModal({ 
  open, 
  onClose, 
  onOk, 
  findUserName,
  onSelectUserClick,
  selectedUserId
}: AddRecordModalProps) {
  const [form] = Form.useForm<NewRecordInput>();
  const [loading, setLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    if (open) {
      // Reset form and internal state when modal opens
      form.resetFields();
      form.setFieldsValue({ 
        RecordedAt: moment().format('YYYY-MM-DD'),
        HandledBy: selectedUserId // Set initial value from prop if available
      }); 
    } 
  }, [open, form, selectedUserId]); // Add selectedUserId dependency

  const handleOk = async () => {
    if (loading) return;
    try {
      setLoading(true);
      const values = await form.validateFields();
      const finalData: NewRecordInput = {
        ...values,
        HandledBy: selectedUserId, // Use the prop directly
      };
      onOk(finalData);
      onClose(); // Close modal on success
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
      messageApi.error('請檢查表單欄位是否填寫正確。');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <ConfigProvider locale={zhTW}>
      {contextHolder}
      <Modal
        title="新增訪談紀錄"
        open={open}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
        maskClosable={false}
        forceRender // Always render modal children to keep Form connected
        width={600} // Adjust width as needed
      >
        <Form form={form} layout="vertical" initialValues={initialNewRecordInput}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4">
            <Form.Item 
              name="RecordType" 
              label="記錄類型"
              rules={[{ required: true, message: '請選擇記錄類型' }]}
            >
              <Select placeholder="選擇類型" options={recordTypeOptions} />
            </Form.Item>
            <Form.Item 
              name="CustomerLevel" 
              label="客戶等級"
              rules={[{ required: true, message: '請選擇客戶等級' }]}
            >
              <Select placeholder="選擇等級" options={customerLevelOptions} />
            </Form.Item>
            <Form.Item 
              name="RecordedAt" 
              label="記錄時間"
              rules={[{ required: true, message: '請選擇記錄時間' }]}
              className="md:col-span-1"
            >
              <Input
                type="date"
                className="w-full h-10"
              />
            </Form.Item>
            <Form.Item 
              name="HandledBy" // Keep name for potential direct form interaction/validation
              label="接待人員"
              className="md:col-span-1"
            >
              <Button 
                 type="default" 
                 onClick={onSelectUserClick} // Call prop function to open modal in parent
                 className="w-full text-left flex justify-between items-center h-10"
              >
                 {selectedUserId ? (
                     <span className="text-gray-900 line-clamp-1">{findUserName(selectedUserId)}</span>
                 ) : (
                     <span className="text-gray-500 text-xs">選擇人員 (預設登入者)</span>
                 )}
                 <UserOutlined className="text-gray-400"/>
              </Button>
            </Form.Item>
            <Form.Item 
              name="Notes" 
              label="記錄內容"
              rules={[{ required: true, message: '請輸入記錄內容' }]}
              className="md:col-span-2"
            >
              <Input.TextArea rows={4} placeholder="請在此輸入訪談內容..." />
            </Form.Item>
          </div>
        </Form>
      </Modal>
    </ConfigProvider>
  );
} 