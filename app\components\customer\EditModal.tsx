'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Customer, CustomerStatus, CustomerSource, UpdateCustomerDto, CustomerRecord } from '../../interfaces/dto/customer.dto';
import SignaturePad from '../common/SignaturePad';
import { taiwanDistricts } from '../../utils/addressData';
import { X } from 'lucide-react';
import { Button, Modal, Form, Input, Select, DatePicker, message, Table, Tag, Space, ConfigProvider, Row, Col } from 'antd';
import zhTW from 'antd/lib/locale/zh_TW';
import moment, { Moment } from 'moment';
import 'moment/locale/zh-tw';
moment.locale('zh-tw');
import { customerApi } from '../../services/api/customerApi';
import TableSelectModal from '../common/TableSelectModal';
import { DropdownItem } from '../../interfaces/dto/common.dto';
import { userInfoApi } from '../../services/api/userInfoApi';
import { DeleteOutlined, PlusOutlined, CalendarOutlined, UserOutlined } from '@ant-design/icons';
import AddRecordModal from './AddRecordModal';
import { crmOptionApi } from '../../services/api/crmOptionApi';

// Define a type for the form data, based on UpdateCustomerDto and adding UI specific fields
interface EditFormData extends Omit<UpdateCustomerDto, 'Birthday'> {
  // Birthday is now handled directly as string in UpdateCustomerDto
  // Birthday?: Moment | null; // Removed Moment type for Birthday
  LeadSourceArray?: string[];    // For multi-select modal state
  PurchaseConditionsArray?: string[]; // Added for new multi-select
}

// Add type for the new record input structure
interface NewRecordInput {
  RecordType: string;
  Notes: string;
  CustomerLevel: string;
  RecordedAt?: string; // string for <input type=\"date\">
  HandledBy?: string;
}

// Define a type for the records stored in state (newly added)
interface NewRecordState extends NewRecordInput {
  tempKey?: number;
  HandledByName?: string; // Store name for display
}

interface EditModalProps {
  open: boolean;
  onClose: () => void;
  customer: Customer | null;
  onSave: (updatedCustomerData: Partial<UpdateCustomerDto>) => void;
}

// Update lead source options
const leadSourceOptions: DropdownItem[] = [
  { Value: "接待中心", Name: "接待中心" },
  { Value: "戶外看板", Name: "戶外看板" },
  { Value: "手舉牌", Name: "手舉牌" },
  { Value: "公車", Name: "公車" },
  { Value: "A 字板", Name: "A 字板" },
  { Value: "路邊 / 攔車派報", Name: "路邊 / 攔車派報" },
  { Value: "簡訊", Name: "簡訊" },
  { Value: "網路廣告", Name: "網路廣告" },
  { Value: "FB 名單", Name: "FB 名單" },
  { Value: "官網名單", Name: "官網名單" },
  { Value: "美學官網", Name: "美學官網" },
  { Value: "社群媒體 FB", Name: "社群媒體 FB" },
  { Value: "社交媒體 LINE", Name: "社交媒體 LINE" },
  { Value: "業務邀約", Name: "業務邀約" },
  { Value: "親友推薦", Name: "親友推薦" },
  { Value: "已購介紹", Name: "已購介紹" },
  { Value: "活動", Name: "活動" },
  { Value: "其他", Name: "其他" }, 
];

// Define occupation options (same as register page)
const occupationOptions: DropdownItem[] = [
    { Value: "海外經商", Name: "海外經商" },
    { Value: "自營商", Name: "自營商" },
    { Value: "軍公教", Name: "軍公教" },
    { Value: "科技業", Name: "科技業" },
    { Value: "製造業", Name: "製造業" },
    { Value: "貿易業", Name: "貿易業" },
    { Value: "醫療業", Name: "醫療業" },
    { Value: "醫律會計", Name: "醫律會計" },
    { Value: "建築業", Name: "建築業" },
    { Value: "傳播媒體", Name: "傳播媒體" },
    { Value: "交通運輸業", Name: "交通運輸業" },
    { Value: "直銷", Name: "直銷" },
    { Value: "家管", Name: "家管" },
    { Value: "電子商務", Name: "電子商務" },
    { Value: "金融業", Name: "金融業" },
    { Value: "服務業", Name: "服務業" },
    { Value: "自由業", Name: "自由業" },
    { Value: "餐飲業", Name: "餐飲業" },
    { Value: "紡織業", Name: "紡織業" },
    { Value: "化工業", Name: "化工業" },
    { Value: "文化出版", Name: "文化出版" },
    { Value: "觀光旅遊業", Name: "觀光旅遊業" },
    { Value: "退休", Name: "退休" },
  { Value: "其他", Name: "其他" },
];

// Define Record Type options
const recordTypeOptions = [
  { value: "來電", label: "來電" },
  { value: "來人", label: "來人" },
  { value: "電訪", label: "電訪" },
  { value: "回訪", label: "回訪" },
];

// Define Customer Level options
const customerLevelOptions = [
  { value: "A", label: "A" },
  { value: "B", label: "B" },
  { value: "C", label: "C" },
  { value: "D", label: "D" },
];

// Initial state for new record input
const initialNewRecordInput: NewRecordInput = {
  RecordType: '',
  Notes: '',
  CustomerLevel: '',
  RecordedAt: moment().format('YYYY-MM-DD'), // Default to today as string
  HandledBy: undefined,
};

const purchaseConditionOptions: DropdownItem[] = [
  { Value: "地段價值", Name: "地段價值" },
  { Value: "建運因素", Name: "建運因素" }, // Assuming this is correct, might be "建材因素" or "建築因素"?
  { Value: "交通便利", Name: "交通便利" },
  { Value: "生活機能", Name: "生活機能" },
  { Value: "學區考量", Name: "學區考量" },
  { Value: "寧靜環境", Name: "寧靜環境" },
  { Value: "公園綠地", Name: "公園綠地" },
  { Value: "增值潛力", Name: "增值潛力" },
  { Value: "建商品牌", Name: "建商品牌" },
  { Value: "制震結構", Name: "制震結構" },
  { Value: "建材設備", Name: "建材設備" },
  { Value: "公設規劃", Name: "公設規劃" },
  { Value: "座向風水", Name: "座向風水" },
  { Value: "室內格局", Name: "室內格局" },
  { Value: "採光通風", Name: "採光通風" },
  { Value: "物業管理", Name: "物業管理" },
  { Value: "價位因素", Name: "價位因素" },
  { Value: "付款條件", Name: "付款條件" },
  { Value: "投資報酬", Name: "投資報酬" },
  { Value: "其他", Name: "其他" },
];

export default function EditModal({ open, onClose, customer, onSave }: EditModalProps) {
  const [form] = Form.useForm<EditFormData & { Birthday?: string }>(); // Add Birthday string type to form
  const [loading, setLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [availableDistricts, setAvailableDistricts] = useState<string[]>([]);
  const [editData, setEditData] = useState<Partial<EditFormData & { Birthday?: string }>>({ Birthday: '' }); // Ensure Birthday is defined to avoid uncontrolled-to-controlled warning
  const [isLeadSourceModalOpen, setIsLeadSourceModalOpen] = useState(false);
  const [isPurchaseConditionModalOpen, setIsPurchaseConditionModalOpen] = useState(false); // State for the new modal
  const [signatureImage, setSignatureImage] = useState<string | undefined>(undefined);
  const [newRecords, setNewRecords] = useState<NewRecordState[]>([]);
  const [nextTempKey, setNextTempKey] = useState(0);
  const [usersForDropdown, setUsersForDropdown] = useState<DropdownItem[]>([]);
  const [isAddRecordModalOpen, setIsAddRecordModalOpen] = useState(false);

  // Add state for the User Select Modal, now controlled here
  const [isUserSelectModalOpen, setIsUserSelectModalOpen] = useState(false);
  const [selectedUserIdForNewRecord, setSelectedUserIdForNewRecord] = useState<string | undefined>(undefined);

  // 簽名相關狀態
  const [origSignatureImage, setOrigSignatureImage] = useState<string | undefined>(undefined);
  const [isSignatureDisabled, setIsSignatureDisabled] = useState<boolean>(false);

  // CRM 選項狀態
  const [requiredPingAreaOptions, setRequiredPingAreaOptions] = useState<DropdownItem[]>([]);
  const [requiredLayoutOptions, setRequiredLayoutOptions] = useState<DropdownItem[]>([]);
  const [budgetOptions, setBudgetOptions] = useState<DropdownItem[]>([]);

  // Fetch users for dropdown
  const fetchUsers = useCallback(async () => {
    try {
      const response = await userInfoApi.getUserInfoDropdownList();
      if (response.isSuccess && response.body) {
        setUsersForDropdown(response.body);
      } else {
        messageApi.error('無法獲取使用者列表');
      }
    } catch (error) {
      messageApi.error('獲取使用者列表時發生錯誤');
      console.error('Failed to fetch users:', error);
    }
  }, [messageApi]);

  // 載入 CRM 選項
  const loadCrmOptions = useCallback(async (siteCode: string) => {
    try {
      // 根據圖片顯示的正確參數，使用 CrmOptionTypeId
      const pingAreaOptions = await getCrmOptionsBySiteAndTypeId(siteCode, 1); // 需求坪數
      const layoutOptions = await getCrmOptionsBySiteAndTypeId(siteCode, 2); // 需求格局  
      const budgetOptionsData = await getCrmOptionsBySiteAndTypeId(siteCode, 3); // 預算範圍

      setRequiredPingAreaOptions(pingAreaOptions);
      setRequiredLayoutOptions(layoutOptions);
      setBudgetOptions(budgetOptionsData);
    } catch (error) {
      console.error('載入 CRM 選項失敗:', error);
      messageApi.error('載入選項失敗，使用預設選項');
      setDefaultOptions();
    }
  }, [messageApi]);

  // 從 CRM 選項 API 獲取特定類型的選項
  const getCrmOptionsBySiteAndTypeId = async (siteCode: string, crmOptionTypeId: number): Promise<DropdownItem[]> => {
    try {
      const response = await crmOptionApi.getCrmOptionDropdown({
        SiteCode: siteCode,
        CrmOptionTypeId: crmOptionTypeId,
        OnlyActive: true
      });

      if (response && Array.isArray(response)) {
        return response.map(item => ({
          Value: item.Name, // 使用 Name 作為 Value
          Name: item.Name
        }));
      }
      return [];
    } catch (error) {
      console.error(`載入 CrmOptionTypeId ${crmOptionTypeId} 選項失敗:`, error);
      return [];
    }
  };

  // 設定預設選項
  const setDefaultOptions = () => {
    setRequiredPingAreaOptions([
      { Value: "20-30坪", Name: "20-30坪" },
      { Value: "30-40坪", Name: "30-40坪" },
      { Value: "40-50坪", Name: "40-50坪" },
      { Value: "50坪以上", Name: "50坪以上" }
    ]);

    setRequiredLayoutOptions([
      { Value: "2房", Name: "2房" },
      { Value: "3房", Name: "3房" },
      { Value: "4房", Name: "4房" },
      { Value: "5房以上", Name: "5房以上" }
    ]);

    setBudgetOptions([
      { Value: "1000萬以下", Name: "1000萬以下" },
      { Value: "1000-1500萬", Name: "1000-1500萬" },
      { Value: "1500-2000萬", Name: "1500-2000萬" },
      { Value: "2000-2500萬", Name: "2000-2500萬" },
      { Value: "2500萬以上", Name: "2500萬以上" }
    ]);
  };

  useEffect(() => {
    if (open) {
      fetchUsers();
      
      // 載入 CRM 選項
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const userData = JSON.parse(userStr);
          const siteCode = userData?.currentUser?.ServiceUnit;
          if (siteCode) {
            loadCrmOptions(siteCode);
          }
        } catch (error) {
          console.error('解析使用者資料失敗:', error);
          setDefaultOptions();
        }
      } else {
        setDefaultOptions();
      }
      
      if (customer) {
        const cityData = taiwanDistricts.find(c => c.name === customer.City);
        setAvailableDistricts(cityData ? cityData.districts : []);
        const leadSourceArray = customer.LeadSource ? customer.LeadSource.split(',').map(s => s.trim()) : [];
        const purchaseConditionsArray = customer.PurchaseConditions ? customer.PurchaseConditions.split(',').map(s => s.trim()) : [];

        const formattedBirthday = customer.Birthday
          ? moment(customer.Birthday).isValid()
            ? moment(customer.Birthday).format('YYYY-MM-DD')
            : ''
          : '';

        const initialEditData: Partial<EditFormData & { Birthday?: string }> = {
          Name: customer.Name,
          Gender: customer.Gender,
          Birthday: formattedBirthday,
          City: customer.City,
          District: customer.District,
          Address: customer.Address,
          PhoneNumber: customer.PhoneNumber,
          Email: customer.Email,
          Occupation: customer.Occupation,
          LeadSource: customer.LeadSource, // Keep original string for reference, though UI uses array
          RequiredPingArea: customer.RequiredPingArea,
          RequiredLayout: customer.RequiredLayout,
          Budget: customer.Budget,
          PurchaseConditions: customer.PurchaseConditions,
          SiteCode: customer.SiteCode,
          CustomerRecords: customer.CustomerRecords,
          ImageBase64: customer.ImagePath,
          LeadSourceArray: leadSourceArray,
          // No need to set PurchaseConditions string here, array is enough for UI state
          PurchaseConditionsArray: purchaseConditionsArray, 
        };
        setEditData(initialEditData);
        // Set form values, including the new PurchaseConditionsArray for initial display if any
        form.setFieldsValue({
            ...initialEditData,
            // Ensure PurchaseConditionsArray is set for the form to pick up for the modal trigger button display
            PurchaseConditionsArray: purchaseConditionsArray 
        });
        // 初始化簽名狀態
        setOrigSignatureImage(customer.ImagePath);
        setSignatureImage(customer.ImagePath);
        // 若後端已有簽名，則 Disable 畫布，否則保持可編輯
        setIsSignatureDisabled(Boolean(customer.ImagePath));
        setNewRecords([]);
        setNextTempKey(0);
        // Reset user selection state when EditModal opens/customer changes
        setSelectedUserIdForNewRecord(undefined);
      } else {
        form.resetFields();
        setEditData({ PurchaseConditionsArray: [] }); // Ensure array is initialized for new entries
        setAvailableDistricts([]);
        setSignatureImage(undefined);
        setOrigSignatureImage(undefined);
        setIsSignatureDisabled(false);
        setNewRecords([]);
        setNextTempKey(0);
        setUsersForDropdown([]); // Clear user list on close
        setSelectedUserIdForNewRecord(undefined); // Reset user selection
      }
    } else {
      form.resetFields();
      setEditData({ PurchaseConditionsArray: [] });
      setAvailableDistricts([]);
      setSignatureImage(undefined);
      setOrigSignatureImage(undefined);
      setIsSignatureDisabled(false);
      setNewRecords([]);
      setNextTempKey(0);
      setUsersForDropdown([]); // Clear user list on close
      setSelectedUserIdForNewRecord(undefined); // Reset user selection
    }
  }, [open, customer, form, fetchUsers, loadCrmOptions]);

  const handleCityChange = (value: string) => {
    const cityData = taiwanDistricts.find(c => c.name === value);
    setAvailableDistricts(cityData ? cityData.districts : []);
    form.setFieldsValue({ District: undefined });
    setEditData(prev => ({ ...prev, City: value, District: undefined }));
  };

  const handleSignatureSave = (signatureData: string) => {
    setSignatureImage(signatureData);
    setEditData(prev => ({ ...prev, ImageBase64: signatureData }));
  };

  const getDropdownDisplayNames = (selectedValues: string[] | undefined): string => {
    if (!selectedValues || selectedValues.length === 0) return '請選擇';
    return selectedValues.join(', ');
  };

  // Update handler for input changes
  // Let Antd Form handle most state updates via Form.Item name
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    // Update editData only if needed for non-form logic (e.g., complex dependencies)
    // setEditData(prev => ({...prev, [name]: value}));
  };

  // Modify handleAddNewRecord to accept data from the modal
  const handleAddNewRecord = (recordData: NewRecordInput) => {
    // Use the selectedUserIdForNewRecord state here if HandledBy isn't passed directly
    // Or ensure AddRecordModal includes it in recordData
    const finalRecordData = {
      ...recordData,
      HandledBy: selectedUserIdForNewRecord ?? recordData.HandledBy, // Prioritize state if needed
    };

    const selectedUserName = findUserName(finalRecordData.HandledBy);
    const recordToAdd: NewRecordState = {
      ...finalRecordData,
      tempKey: nextTempKey,
      HandledByName: selectedUserName,
    };
    setNewRecords(prev => [...prev, recordToAdd]);
    setNextTempKey(prev => prev + 1);
    setIsAddRecordModalOpen(false); // Close the add modal
    // Optionally reset selected user for the *next* new record
    // setSelectedUserIdForNewRecord(undefined); 
  };

  // Handler to remove a newly added record from the temporary list
  const handleRemoveNewRecord = (tempKeyToRemove: number) => {
    setNewRecords(prev => prev.filter(record => record.tempKey !== tempKeyToRemove));
  };

  // Helper to find user name (used in table and button)
  const findUserName = useCallback((userId: string | undefined | null): string => {
    if (!userId) return '';
    return usersForDropdown.find(u => u.Value === userId)?.Name || userId;
  }, [usersForDropdown]);

  // Handler to open the user selection modal
  const handleOpenUserSelectModal = () => {
    setIsUserSelectModalOpen(true);
  };

  // Handler for selecting a user in the modal
  const handleUserSelect = (selectedUser: DropdownItem) => {
    if (selectedUser) {
      setSelectedUserIdForNewRecord(selectedUser.Value);
    }
    setIsUserSelectModalOpen(false); // Close the user select modal
  };

  // Update handleSave to use string date and add loading check
  const handleSave = async () => {
    if (loading) {
      return;
    }

    try {
      setLoading(true);
      const values = await form.validateFields();

      if (!customer?.CustomerId) {
        messageApi.error('缺少客戶 ID，無法更新');
        setLoading(false);
        return;
      }

      const leadSourceString = editData.LeadSourceArray?.join(',') || '';
      // Get PurchaseConditions string from array
      const purchaseConditionsString = editData.PurchaseConditionsArray?.join(',') || '';

      const newRecordsPayload: CustomerRecord[] = newRecords.map(record => ({
        CustomerRecordId: undefined,
        RecordType: record.RecordType,
        Notes: record.Notes,
        CustomerLevel: record.CustomerLevel,
        RecordedAt: record.RecordedAt ? record.RecordedAt : undefined,
        HandledBy: record.HandledBy ? record.HandledBy : undefined,
      }));

      const updatePayload: UpdateCustomerDto = {
        Name: values.Name,
        Gender: values.Gender,
        Birthday: values.Birthday || undefined,
        City: values.City,
        District: values.District,
        Address: values.Address,
        PhoneNumber: values.PhoneNumber,
        Email: values.Email || undefined,
        Occupation: values.Occupation || undefined,
        LeadSource: leadSourceString,
        PurchaseConditions: purchaseConditionsString, // Add to payload
        RequiredPingArea: values.RequiredPingArea,
        RequiredLayout: values.RequiredLayout,
        Budget: values.Budget,
        ImageBase64: editData.ImageBase64,
        SiteCode: values.SiteCode || customer.SiteCode,
        CustomerRecords: newRecordsPayload.length > 0 ? newRecordsPayload : undefined,
      };

      // Clean undefined fields before logging and sending
      Object.keys(updatePayload).forEach(key => {
        if (updatePayload[key as keyof UpdateCustomerDto] === undefined) {
          delete updatePayload[key as keyof UpdateCustomerDto];
        }
      });
      if (updatePayload.CustomerRecords) {
        updatePayload.CustomerRecords = updatePayload.CustomerRecords.map(record => {
          const cleanRecord = { ...record };
          if (cleanRecord.HandledBy === undefined) delete cleanRecord.HandledBy;
          if (cleanRecord.RecordedAt === undefined) delete cleanRecord.RecordedAt;
          return cleanRecord;
        })
      }
      // Ensure CustomerRecords is removed if it became an empty array after cleaning
      if (updatePayload.CustomerRecords && updatePayload.CustomerRecords.length === 0) {
        delete updatePayload.CustomerRecords;
      }

      onSave(updatePayload);

    } catch (error: any) {
      if (error.errorFields && error.errorFields.length > 0) {
        messageApi.error('請檢查表單欄位是否填寫正確。' + error.errorFields.map((ef: any) => ef.errors.join(', ')).join('; '));
      } else {
        messageApi.error('更新過程中發生錯誤。' + (error.message ? `: ${error.message}` : ''));
      }
    } finally {
      setLoading(false);
    }
  };

  // Update RecordedAt rendering for string date
  const recordTableColumns = [
    {
      title: '記錄類型',
      dataIndex: 'RecordType',
      key: 'RecordType',
      width: 100,
      render: (text: string) => <Tag>{text || '-'}</Tag>
    },
    {
      title: '記錄內容',
      dataIndex: 'Notes',
      key: 'Notes',
      render: (text: string) => <div className="whitespace-pre-wrap">{text || '-'}</div>
    },
    {
      title: '記錄時間',
      dataIndex: 'RecordedAt',
      key: 'RecordedAt',
      width: 150,
      render: (recordedAt: string | undefined) => {
        const dateText = recordedAt || '-';
        return <Space><CalendarOutlined /> {dateText}</Space>;
      }
    },
    {
      title: '客戶等級',
      dataIndex: 'CustomerLevel',
      key: 'CustomerLevel',
      width: 100,
      render: (text: string) => <Tag>{text || '-'}</Tag>
    },
    {
      title: '*接待人員',
      dataIndex: 'HandledBy',
      key: 'HandledBy',
      width: 120,
      render: (handledById: string | undefined, record: CustomerRecord | NewRecordState) => {
        const name = handledById ? findUserName(handledById) : ('HandledByName' in record ? record.HandledByName : '');
        return <Space><UserOutlined /> {name || '-'}</Space>;
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, record: NewRecordState) => (
        record.tempKey !== undefined ? (
          <Button
            type="text"
            danger
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => handleRemoveNewRecord(record.tempKey!)}
            aria-label="移除此筆紀錄"
          />
        ) : null
      ),
    },
  ];

  return (
    <ConfigProvider locale={zhTW}>
      <Modal
        forceRender
        title={(
          <div className="flex items-center gap-4">
            <span className="text-lg font-semibold">編輯客戶資料</span>
            <span className="text-sm text-gray-600">
              (案場代碼: {editData.SiteCode ?? customer?.SiteCode ?? '-'})
            </span>
          </div>
        )}
        open={open}
        onCancel={onClose}
        onOk={handleSave}
        confirmLoading={loading}
        width={1000}
        maskClosable={false}
        styles={{ body: { maxHeight: '75vh', overflowY: 'auto' } }}
      >
        {contextHolder}
        <Form
          key={customer?.CustomerId || ''}
          form={form}
          layout="vertical"
          initialValues={editData}
          className="pr-4"
        >
          {/* Basic Info Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-x-4">
            <Form.Item name="Name" label="姓名" rules={[{ required: true, message: '請輸入姓名' }]}>
              <Input />
            </Form.Item>
            <Form.Item name="Gender" label="性別">
              <Select placeholder="請選擇">
                <Select.Option value="男">男</Select.Option>
                <Select.Option value="女">女</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item name="Birthday" label="出生日期">
              <Input
                type="date"
                className="w-full h-10"
              />
            </Form.Item>
            <Form.Item name="City" label="縣市">
              <Select placeholder="請選擇" onChange={handleCityChange}>
                {taiwanDistricts.map((city) => (
                  <Select.Option key={city.name} value={city.name}>
                    {city.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="District" label="區域">
              <Select placeholder="請選擇" disabled={availableDistricts.length === 0}>
                {availableDistricts.map((district) => (
                  <Select.Option key={district} value={district}>
                    {district}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="Address" label="地址" className="md:col-span-1">
              <Input />
            </Form.Item>
            <Form.Item name="PhoneNumber" label="聯絡電話" rules={[{ required: true, message: '請輸入聯絡電話' }]}>
              <Input type="tel" />
            </Form.Item>
            <Form.Item name="Email" label="電子信箱">
              <Input type="email" />
            </Form.Item>
            <Form.Item name="Occupation" label="職業">
              <Select placeholder="請選擇職業" allowClear>
                {occupationOptions.map(option => (
                  <Select.Option key={option.Value} value={option.Value}>{option.Name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="您從何處得知本案訊息 (可複選)" className="md:col-span-3">
              <Button
                type="default"
                onClick={() => setIsLeadSourceModalOpen(true)}
                className="w-full text-left flex justify-between items-center"
              >
                <span className={`line-clamp-1 ${editData.LeadSourceArray && editData.LeadSourceArray.length > 0 ? 'text-gray-900' : 'text-gray-500'}`}>
                  {getDropdownDisplayNames(editData.LeadSourceArray)}
                </span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </Button>
            </Form.Item>
            <Form.Item name="RequiredPingArea" label="需求坪數">
              <Select placeholder="請選擇" allowClear>
                {requiredPingAreaOptions.map(option => (
                  <Select.Option key={option.Value} value={option.Value}>{option.Name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="RequiredLayout" label="需求格局">
              <Select placeholder="請選擇" allowClear>
                {requiredLayoutOptions.map(option => (
                  <Select.Option key={option.Value} value={option.Value}>{option.Name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="Budget" label="預算範圍">
              <Select placeholder="請選擇" allowClear>
                {budgetOptions.map(option => (
                  <Select.Option key={option.Value} value={option.Value}>{option.Name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="在意購屋條件 (可複選)" className="md:col-span-3">
              <Button 
                type="default" 
                onClick={() => setIsPurchaseConditionModalOpen(true)} 
                className="w-full text-left flex justify-between items-center"
              >
                <span className={`line-clamp-1 ${editData.PurchaseConditionsArray && editData.PurchaseConditionsArray.length > 0 ? 'text-gray-900' : 'text-gray-500'}`}>
                    {getDropdownDisplayNames(editData.PurchaseConditionsArray)}
                </span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </Button>
            </Form.Item>
            <Form.Item label="簽名" className="md:col-span-3">
              <div className="border rounded p-2 min-h-[150px]">
                {/* 簽名狀態 */}
                {isSignatureDisabled ? (
                  // 已有簽名，顯示圖片與重新簽名按鈕
                  <>
                    {signatureImage && (
                      <img
                        src={signatureImage.startsWith('data:image') ? signatureImage : `data:image/png;base64,${signatureImage}`}
                        alt="客戶簽名"
                        className="max-h-32 mx-auto mb-2"
                      />
                    )}
                    <div className="text-center mt-2">
                      <Button
                        type="link"
                        onClick={() => {
                          // 進入重簽模式：清除圖片並啟用畫布
                          setIsSignatureDisabled(false);
                          setSignatureImage(undefined);
                        }}
                      >
                        重新簽名
                      </Button>
              </div>
                  </>
                ) : (
                  // 重簽中，顯示畫布與取消按鈕
                  <>
                    <SignaturePad onSave={handleSignatureSave} />
                    <div className="text-center mt-2">
                      <Button
                        type="link"
                        onClick={() => {
                          // 取消重簽：恢復原始圖片並禁用畫布
                          setIsSignatureDisabled(true);
                          setSignatureImage(origSignatureImage);
                        }}
                      >
                        取消
                      </Button>
              </div>
                  </>
                )}
              </div>
            </Form.Item>
            <Form.Item name="SiteCode" label="案場代碼">
              <Input disabled />
            </Form.Item>
          </div>
        </Form>

        {/* Interview Records Section */}
        <div className="mt-6 pt-4 border-t">
          {/* Title */}
          <h3 className="text-lg font-semibold mb-2">客戶訪談紀錄</h3>
          {/* Add button below title, left aligned */}
          <div className="mb-3">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsAddRecordModalOpen(true)}
              size="small"
            >
              新增紀錄
            </Button>
          </div>

          {/* Combined Table for Existing and New Records */}
          <Table
            dataSource={[...(customer?.CustomerRecords ?? [] as CustomerRecord[]), ...newRecords]} // Combine existing and new, assert type for existing
            columns={recordTableColumns}
            rowKey={(record: CustomerRecord | NewRecordState) =>
              'CustomerRecordId' in record && record.CustomerRecordId ? record.CustomerRecordId : `new-${(record as NewRecordState).tempKey}`
            } // Use ID or temp key safely
            pagination={false} // Disable pagination within the modal table
            size="small"
            className="mb-4"
            locale={{ emptyText: '暫無訪談紀錄' }}
            scroll={{ y: 200 }} // Add scroll if content overflows
          />
      </div>

        <TableSelectModal
          isOpen={isLeadSourceModalOpen}
          onClose={() => setIsLeadSourceModalOpen(false)}
          onSelect={(selectedItems: DropdownItem[]) => {
            const selectedValues = selectedItems.map(item => item.Value);
            form.setFieldsValue({ LeadSource: selectedValues.join(','), LeadSourceArray: selectedValues });
            setEditData(prev => ({ ...prev, LeadSource: selectedValues.join(','), LeadSourceArray: selectedValues }));
            setIsLeadSourceModalOpen(false);
          }}
          title="選擇訊息來源"
          data={leadSourceOptions}
          columns={[{ key: "Name", title: "來源" }]}
          rowKey="Value"
          isMultiSelect={true}
          initialSelectedValues={editData.LeadSourceArray || []}
          searchKeys={["Name"]}
        />

        {/* New TableSelectModal for Purchase Conditions */}
        <TableSelectModal
          isOpen={isPurchaseConditionModalOpen}
          onClose={() => setIsPurchaseConditionModalOpen(false)}
          onSelect={(selectedItems: DropdownItem[]) => {
            const selectedValues = selectedItems.map(item => item.Value);
            // Only update the array in the form and local state. String is generated on save.
            form.setFieldsValue({ PurchaseConditionsArray: selectedValues });
            setEditData(prev => ({ ...prev, PurchaseConditionsArray: selectedValues }));
            setIsPurchaseConditionModalOpen(false);
          }}
          title="選擇在意購屋條件"
          data={purchaseConditionOptions}
          columns={[{ key: "Name", title: "條件" }]}
          rowKey="Value"
          isMultiSelect={true}
          initialSelectedValues={editData.PurchaseConditionsArray || []}
          searchKeys={["Name"]}
        />

        {/* Render the Add Record Modal */}
        <AddRecordModal
          open={isAddRecordModalOpen}
          onClose={() => setIsAddRecordModalOpen(false)}
          onOk={handleAddNewRecord}
          findUserName={findUserName} // Pass the findUserName function
          onSelectUserClick={handleOpenUserSelectModal}
          selectedUserId={selectedUserIdForNewRecord}
        />

        {/* User Select Modal (Rendered at EditModal level) */}
        <TableSelectModal
          isOpen={isUserSelectModalOpen}
          onClose={() => setIsUserSelectModalOpen(false)}
          onSelect={(selectedItem) => { // Single select
            handleUserSelect(selectedItem as DropdownItem);
          }}
          title="選擇接待人員"
          data={usersForDropdown}
          columns={[
            { key: "Value", title: "帳號" },
            { key: "Name", title: "姓名" },
          ]}
          searchKeys={["Value", "Name"]}
          rowKey="Value"
          isMultiSelect={false} // Single selection for HandledBy
          initialSelectedValues={selectedUserIdForNewRecord ? [selectedUserIdForNewRecord] : []}
          zIndex={1300} // Pass an even higher zIndex (above AddRecordModal's 1200)
        />
      </Modal>
    </ConfigProvider>
  );
} 