'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight } from 'lucide-react';

// 定義 MenuTree 介面
interface MenuTree {
  Name: string;
  Id: string;
  Selected: boolean;
  Children: MenuTree[];
}

interface BreadcrumbItem {
  name: string;
  href: string;
}

// 從 localStorage 獲取選單數據
const getMenuTrees = (): MenuTree[] => {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return [];
    
    const userData = JSON.parse(userStr);
    if (!userData.currentUser?.MenuTrees) return [];
    
    return userData.currentUser.MenuTrees;
  } catch (error) {
    console.error('Error parsing menu trees:', error);
    return [];
  }
};

// --- 新增：優化輔助函數，建立查找映射 ---
const buildMenuMap = (menus: MenuTree[]): { [id: string]: MenuTree } => {
  const map: { [id: string]: MenuTree } = {};
  if (!menus) return map; // 添加保護
  for (const menu of menus) {
    map[menu.Id] = menu;
  }
  return map;
};

// 靜態路由映射 - 不依賴菜單權限
const staticRouteMap: { [key: string]: string } = {
  '/settings': '基本設定',
  '/settings/categories': '下拉選單設定',
  '/settings/categories/large': '大分類管理',
  '/settings/categories/medium': '中分類管理',
  '/settings/categories/small': '小分類管理',
  '/settings/categories/crm-options': 'CRM選項管理',
  // 可以繼續添加其他靜態路由
};

// --- 修改：根據路徑查找麵包屑項目 (混合版) ---
const findBreadcrumbItems = (path: string, menuTrees: MenuTree[]): BreadcrumbItem[] => {
  // 首先檢查是否為靜態路由
  if (path.startsWith('/settings/categories')) {
    return buildStaticBreadcrumbs(path);
  }

  // 對於其他路由，使用原有的菜單匹配邏輯
  return buildMenuBasedBreadcrumbs(path, menuTrees);
};

// 建立靜態面包屑
const buildStaticBreadcrumbs = (path: string): BreadcrumbItem[] => {
  const parts = path.split('/').filter(Boolean);
  const items: BreadcrumbItem[] = [];
  let currentPath = '';

  for (const part of parts) {
    currentPath += `/${part}`;
    const name = staticRouteMap[currentPath];
    if (name) {
      items.push({ name, href: currentPath });
    }
  }

  return items;
};

// 建立基於菜單的面包屑（原有邏輯）
const buildMenuBasedBreadcrumbs = (path: string, menuTrees: MenuTree[]): BreadcrumbItem[] => {
  // 路徑映射 - 主要用於報表
  const segmentMap: { [key: string]: string } = {
    ReportGeneration: 'reports',
    DailyReport: 'daily',
    WeeklyReport: 'weekly',
    BusinessReport: 'management',
    MediaBenefitAnalysis: 'media-benefit-analysis',
  };

  const parts = path.split('/').filter(Boolean);
  const items: BreadcrumbItem[] = [];
  let currentPath = '';
  let currentLevelMenus = menuTrees;

  for (const part of parts) {
    currentPath += `/${part}`;

    const matchingMenu = currentLevelMenus.find(menu => {
      const segment = segmentMap[menu.Id] || menu.Id;
      return segment === part;
    });

    if (!matchingMenu) {
      break;
    }

    items.push({ name: matchingMenu.Name, href: currentPath });
    currentLevelMenus = matchingMenu.Children || [];
  }

  return items;
};
// --- 結束修改 ---

export default function Breadcrumb() {
  const pathname = usePathname();
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);

  useEffect(() => {
    const menuTrees = getMenuTrees();
    const items = findBreadcrumbItems(pathname, menuTrees);
    setBreadcrumbs(items);
  }, [pathname]);

  if (breadcrumbs.length <= 1) return null;

  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-600">
      {breadcrumbs.map((item, index) => (
        <React.Fragment key={item.href}>
          {index > 0 && (
            <ChevronRight className="w-4 h-4 text-gray-400" />
          )}
          {index === breadcrumbs.length - 1 ? (
            <span className="text-gray-900">{item.name}</span>
          ) : (
            <Link
              href={item.href}
              className="hover:text-blue-600 transition-colors"
            >
              {item.name}
            </Link>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
} 