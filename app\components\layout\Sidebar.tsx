'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  ChevronDown,
  ChevronRight,
  Menu,
  Settings,
  Calculator,
  Users,
  ShoppingCart,
  FileText,
  Shield,
  Building2,
  User,
  UserCog,
  ScrollText,
  X,
  LucideIcon,
  BarChart,
  ClipboardList,
  DollarSign,
  Home,
  Award,
  Layers,
  File,
  CreditCard,
  MapPin
} from 'lucide-react';

// 定義 MenuTree 介面
interface MenuTree {
  Name: string;
  Id: string;
  Selected: boolean;
  Children: MenuTree[];
}

// 定義導航項目介面
interface NavigationItem {
  name: string;
  href: string;
  icon: LucideIcon;
  selected: boolean;
  children?: NavigationItem[];
}

// 圖標映射
const iconMap: { [key: string]: LucideIcon } = {
  'settings': Settings,
  'budget': Calculator,
  'customers': Users,
  'sales': ShoppingCart,
  'administration': FileText,
  'charts': BarChart,
  'permissions': Shield,
  'projects': Building2,
  'owners': User,
  'units': Home,
  'budget-categories': ClipboardList,
  'contract-categories': File,
  'asset-categories': Layers,
  'contracts': ClipboardList,
  'control': DollarSign,
  'real-estate': Building2,
  'available-houses': Home,
  'available-parking': MapPin,
  'service-fee': CreditCard,
  'bonus': Award,
  'performance': BarChart,
  'assets': ClipboardList,
  'asset-transfer': FileText,
  'customer-mining': Users,
  'accounts': UserCog,
  'roles': Users,
  'workflow': ScrollText,
  // 添加其他圖標映射
};

// 將 MenuTree 轉換為 NavigationItem
const convertMenuTreeToNavItem = (menuTree: MenuTree, parentPath: string = ''): NavigationItem => {
  // 路徑映射 - 包含報表和分類管理
  const segmentMap: { [key: string]: string } = {
    // 報表路徑映射
    ReportGeneration: 'reports',
    DailyReport: 'daily',
    WeeklyReport: 'weekly',
    BusinessReport: 'management',
    MediaBenefitAnalysis: 'media-benefit-analysis',
    // 分類管理路徑映射
    settings: 'settings',
    categories: 'categories',
    large: 'large',
    medium: 'medium',
    small: 'small',
    'crm-options': 'crm-options',
  };
  const segment = segmentMap[menuTree.Id] || menuTree.Id;
  let path = parentPath ? `${parentPath}/${segment}` : `/${segment}`;
  // 特殊處理 CRM 客戶路由
  if (menuTree.Id === 'customers-crm') {
    path = '/customers';
  }

  // 遞迴轉換前先過濾子項目
  const filteredChildren = menuTree.Children?.filter(child => child.Selected);

  return {
    name: menuTree.Name,
    href: path,
    icon: iconMap[menuTree.Id] || Settings, // 使用默認圖標如果沒有找到映射
    selected: menuTree.Selected, // 注意：這裡的 selected 來自原始 MenuTree，用於可能的樣式，但過濾是在上面完成的
    children: filteredChildren && filteredChildren.length > 0
      ? filteredChildren.map(child => convertMenuTreeToNavItem(child, path))
      : undefined
  };
};

// 從 localStorage 獲取選單數據
const getNavigationItems = (): NavigationItem[] => {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return [];
    
    const userData = JSON.parse(userStr);
    if (!userData.currentUser?.MenuTrees) return [];
    
    return userData.currentUser.MenuTrees
      .filter((menu: MenuTree) => menu.Selected)
      .map((menu: MenuTree) => convertMenuTreeToNavItem(menu));
  } catch (error) {
    console.error('Error parsing navigation items:', error);
    return [];
  }
};

type IconWrapperProps = {
  icon: LucideIcon;
};

interface SidebarProps {
  onCollapse?: (collapsed: boolean) => void;
}

export default function Sidebar({ onCollapse }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState<{ top: number, left: number } | null>(null);
  const [navigationItems, setNavigationItems] = useState<NavigationItem[]>([]);
  const itemRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pathname = usePathname();

  // 初始化導航項目
  useEffect(() => {
    setNavigationItems(getNavigationItems());
  }, []);

  // 當收合狀態改變時通知父組件
  useEffect(() => {
    onCollapse?.(isCollapsed);
  }, [isCollapsed, onCollapse]);

  // 監聽螢幕寬度變化
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 清理定時器
  useEffect(() => {
    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, []);

  const toggleExpand = (href: string) => {
    setExpandedItems(prev =>
      prev.includes(href)
        ? prev.filter(item => item !== href)
        : [...prev, href]
    );
  };

  const isExpanded = (href: string) => expandedItems.includes(href);
  const isActive = (href: string) => pathname === href;
  const isHovered = (href: string) => hoveredItem === href;

  const IconWrapper = ({ icon: Icon }: IconWrapperProps) => (
    <Icon size={isCollapsed ? 24 : 20} className="min-w-[20px]" />
  );

  // 子菜單圖標包裝器
  const SubMenuIconWrapper = ({ icon: Icon }: IconWrapperProps) => (
    <Icon size={20} className="min-w-[20px]" />
  );



  const handleMouseEnter = (href: string) => {
    if (isCollapsed) {
      // 清除任何現有的隱藏定時器
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
        hideTimeoutRef.current = null;
      }
      
      setHoveredItem(href);
      const element = itemRefs.current[href];
      if (element) {
        const rect = element.getBoundingClientRect();
        setMenuPosition({
          top: rect.top,
          left: rect.right + 2
        });
      }
    }
  };

  const handleMouseLeave = () => {
    if (isCollapsed) {
      // 設置延遲隱藏，給用戶時間移動到子菜單
      hideTimeoutRef.current = setTimeout(() => {
        setHoveredItem(null);
        setMenuPosition(null);
      }, 300); // 300毫秒的延遲
    }
  };

  const cancelHideTimeout = () => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = null;
    }
  };

  return (
    <>
      {/* 移動端遮罩 */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* 移動端選單按鈕 */}
      <button
        onClick={() => setIsMobileOpen(true)}
        className="fixed top-4 left-4 z-50 lg:hidden bg-gray-800 text-white p-2 rounded-md hover:bg-gray-700 transition-colors"
      >
        <Menu size={24} />
      </button>

      {/* 側邊欄 */}
      <div className={`fixed inset-y-0 left-0 z-50 transform ${
        isMobileOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:translate-x-0 transition-transform duration-300 ease-in-out h-full`}>
        <nav className={`bg-gray-800 h-full ${
          isCollapsed ? 'w-16' : 'w-64'
        } relative flex flex-col overflow-hidden transition-all duration-300`}>
          {/* 移動端關閉按鈕 */}
          <button
            onClick={() => setIsMobileOpen(false)}
            className="lg:hidden absolute top-4 right-4 text-white z-10 hover:text-gray-300 transition-colors"
          >
            <X size={24} />
          </button>

          {/* 標題區域 */}
          <div className={`text-white font-bold p-4 flex items-center ${
            isCollapsed ? 'justify-center' : 'justify-between'
          } flex-shrink-0 relative border-b border-gray-700`}>
            <span className={isCollapsed ? 'text-sm' : 'text-xl'}>
              {isCollapsed ? '美學' : '美學生活'}
            </span>
            
            {/* 展開狀態下的收合按鈕 */}
            {!isCollapsed && (
              <button
                onClick={() => setIsCollapsed(true)}
                className="hidden lg:flex items-center justify-center text-white hover:text-gray-300 transition-colors p-1.5 hover:bg-gray-700 rounded-md"
                aria-label="收合側邊欄"
                title="收合側邊欄"
              >
                <Menu size={18} />
              </button>
            )}
          </div>

          {/* 收合狀態下的展開按鈕 */}
          {isCollapsed && (
            <button
              onClick={() => setIsCollapsed(false)}
              className="hidden lg:flex w-full items-center justify-center text-white hover:text-gray-300 transition-colors py-4 hover:bg-gray-700 border-b border-gray-700"
              aria-label="展開側邊欄"
              title="展開側邊欄"
            >
              <ChevronRight size={22} />
            </button>
          )}

          <div className="flex-1 overflow-y-auto overflow-x-hidden">
            <ul className={`${isCollapsed ? 'py-3 space-y-4' : 'space-y-2 p-4'}`}>
              {navigationItems.map((item) => (
                <li key={item.href} className={isCollapsed ? 'px-0 relative' : ''}>
                  {item.children ? (
                    <div 
                      className="relative"
                      ref={(el) => {
                        itemRefs.current[item.href] = el;
                      }}
                      onMouseEnter={() => handleMouseEnter(item.href)}
                      onMouseLeave={handleMouseLeave}
                    >
                      <button
                        onClick={() => isCollapsed ? null : toggleExpand(item.href)}
                        className={`w-full text-left text-gray-300 hover:text-white hover:bg-gray-700 ${
                          isCollapsed ? 'py-3 flex justify-center' : 'px-3 py-2 rounded-md flex items-center justify-between'
                        } ${
                          isActive(item.href) ? 'bg-gray-700 text-white' : ''
                        }`}
                      >
                        <span className={`flex items-center gap-2 ${isCollapsed ? 'justify-center' : ''}`}>
                          <IconWrapper icon={item.icon} />
                          <span className={isCollapsed ? 'hidden' : 'block'}>{item.name}</span>
                        </span>
                        {!isCollapsed && (
                          isExpanded(item.href) ? <ChevronDown size={16} /> : <ChevronRight size={16} />
                        )}
                      </button>
                      
                      {/* 收合狀態下的懸停子菜單 */}
                      {isCollapsed && isHovered(item.href) && menuPosition && (
                        <div 
                          className="fixed bg-white rounded-md shadow-lg overflow-hidden z-50 min-w-[240px]"
                          style={{ top: `${menuPosition.top}px`, left: `${menuPosition.left}px` }}
                          onMouseEnter={cancelHideTimeout}
                          onMouseLeave={handleMouseLeave}
                        >
                          <div className="py-3 px-4 bg-gray-100 border-b border-gray-200">
                            <span className="text-gray-800 font-medium">{item.name}</span>
                          </div>
                          <ul className="py-1">
                            {item.children.map((child) => (
                              <li key={child.href} className="border-b border-gray-100 last:border-b-0">
                                <Link
                                  href={child.href}
                                  className={`flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 ${
                                    isActive(child.href) ? 'bg-gray-50 text-blue-600' : ''
                                  }`}
                                  onClick={() => {
                                    setHoveredItem(null);
                                    setIsMobileOpen(false);
                                  }}
                                >
                                  <div className="text-gray-600 mr-2">
                                    <SubMenuIconWrapper icon={child.icon} />
                                  </div>
                                  <span>{child.name}</span>
                                </Link>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className={`text-gray-300 hover:text-white hover:bg-gray-700 ${
                        isCollapsed ? 'py-3 flex justify-center' : 'px-3 py-2 rounded-md flex items-center gap-2'
                      } ${
                        isActive(item.href) ? 'bg-gray-700 text-white' : ''
                      }`}
                      onClick={() => setIsMobileOpen(false)}
                    >
                      <IconWrapper icon={item.icon} />
                      <span className={isCollapsed ? 'hidden' : 'block'}>{item.name}</span>
                    </Link>
                  )}
                  
                  {item.children && isExpanded(item.href) && !isCollapsed && (
                    <ul className="ml-4 mt-2 space-y-2">
                      {item.children.map((child) => (
                        <li key={child.href}>
                          <Link
                            href={child.href}
                            className={`text-gray-300 hover:text-white hover:bg-gray-700 px-3 py-2 rounded-md flex items-center gap-2 ${
                              isActive(child.href) ? 'bg-gray-700 text-white' : ''
                            }`}
                            onClick={() => setIsMobileOpen(false)}
                          >
                            <IconWrapper icon={child.icon} />
                            {child.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </nav>
      </div>

      {/* 主要內容區域的 padding */}
      <div className={`lg:pl-${isCollapsed ? '16' : '64'}`} />
    </>
  );
} 