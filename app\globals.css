@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局輸入框樣式 */
input, select, textarea {
  color: #000000 !important; /* 黑色文字 */
}

/* 確保輸入框的佔位符文字顏色較淺 */
input::placeholder, 
select::placeholder, 
textarea::placeholder {
  color: #9ca3af !important; /* 淺灰色 */
}

/* 確保禁用狀態的輸入框文字顏色 */
input:disabled, 
select:disabled, 
textarea:disabled {
  color: #6b7280 !important; /* 中灰色 */
  background-color: #f3f4f6 !important; /* 淺灰色背景 */
}

/* 標題和文字樣式 */
h1, h2, h3, h4, h5, h6 {
  color: #111827 !important; /* 深黑色 */
  font-weight: 600 !important;
}

/* 特別加強 h2 的顯示效果 */
h2 {
  font-weight: 700 !important;
}

/* 標籤文字樣式 */
label {
  color: #1f2937 !important; /* 深灰黑色 */
  font-weight: 500 !important;
}

/* 段落文字樣式 */
p {
  color: #1f2937 !important; /* 深灰黑色 */
}

/* 表格內容和列表項目 */
td, th, li {
  color: #1f2937 !important; /* 深灰黑色 */
}

/* 全局文字顏色覆蓋 */
.text-gray-500, .text-gray-600, .text-gray-700 {
  color: #1f2937 !important; /* 深灰黑色 */
}

/* 確保表單標籤足夠明顯 */
.block.text-sm {
  color: #1f2937 !important; /* 深灰黑色 */
  font-weight: 500 !important;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

/* 自定義滾動條樣式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* 側邊欄特定的滾動條樣式 */
nav .overflow-y-auto::-webkit-scrollbar {
  width: 3px;
}

nav .overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.2);
}

nav .overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.2);
}

nav .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.4);
}

/* 確保側邊欄內容可以滾動 */
.sidebar-content {
  max-height: calc(100vh - 64px);
  overflow-y: auto;
}
