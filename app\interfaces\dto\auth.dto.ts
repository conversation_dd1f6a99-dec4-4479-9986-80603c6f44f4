export interface LoginRequest {
  UserInfoId: string;
  UserPW: string;
  Captcha: string;
}

export interface UserInfo {
  UserInfoId: string;
  UserName: string;
  Email: string;
  UserPW: string;
  IP: string;
  MenuTrees: any[];
  Response: Record<string, any>;
}

export interface LoginResponse {
  code: string;
  message: string;
  isSuccess: boolean;
  body: {
    UserInfoId: string;
    UserPW: string;
    Name: string;
    IP: string;
    Email: string;
    MenuTrees: Array<{
      Name: string;
      Id: string;
      Selected: boolean;
      Children: string[];
    }>;
    JWTToken: string;
    JWTTokenExpireTime: string;
    CompanyId: string;
    Gender: string;
    BirthDate: string;
    TelephoneNumber: string;
    MobileNumber: string;
    RegisteredAddress: string;
    MailingAddress: string;
    EmergencyContactName: string;
    EmergencyContactPhone: string;
    EmergencyContactRelation: string;
    ServiceUnit: string;
    HireDate: string;
    Status: boolean;
    LastLogoutTime: string;
    LoginFailedCount: number;
    IsInside: boolean;
    IsM365: boolean;
    IsEmailNotificationEnabled: boolean;
    Response: Record<string, any>;
  };
}

export interface CurrentUser {
  UserId: string;
  UserName: string;
  Grade: string;
  MobileNumber: string;
  IsAdmin: boolean;
  IsM365: boolean;
  ConstructionSites: Array<{
    ConstructionSiteId: string;
    ConstructionSiteName: string;
    ConstructionSiteRole: string | null;
  }>;
  CurrentRole: string;
  IP: string;
  IsEnableCountdown: boolean;
  JWTToken: string;
  JWTTokenExpireTime: string;
  Permission: string[];
  PermissionTree: Array<{
    Name: string;
    Id: string;
    Selected: boolean;
    IsActive: boolean;
    Crud: any;
    Children: any[];
  }>;
  Roles: any[];
  UserEmail: string;
}

export interface CaptchaResponse {
  blob: Blob;
}

// 用於本地存儲的用戶數據結構
export interface StoredUserData {
  isLogin: boolean;
  error: string | null;
  currentUser: {
    UserInfoId: string;
    UserPW: string;
    Name: string;
    IP: string;
    Email: string;
    MenuTrees: Array<{
      Name: string;
      Id: string;
      Selected: boolean;
      Children: string[];
    }>;
    JWTToken: string;
    JWTTokenExpireTime: string;
    CompanyId: string;
    Gender: string;
    BirthDate: string;
    TelephoneNumber: string;
    MobileNumber: string;
    RegisteredAddress: string;
    MailingAddress: string;
    EmergencyContactName: string;
    EmergencyContactPhone: string;
    EmergencyContactRelation: string;
    ServiceUnit: string;
    HireDate: string;
    Status: boolean;
    LastLogoutTime: string;
    LoginFailedCount: number;
    IsInside: boolean;
    IsM365: boolean;
    IsEmailNotificationEnabled: boolean;
    Response: Record<string, any>;
  };
} 