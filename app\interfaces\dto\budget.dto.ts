// 預算類型枚舉
export enum BudgetType {
  MARKETING = 'MARKETING',   // 行銷預算
  COMMISSION = 'COMMISSION', // 佣金預算
  OPERATION = 'OPERATION',   // 營運預算
  OTHERS = 'OTHERS'         // 其他預算
}

// 預算狀態枚舉
export enum BudgetStatus {
  DRAFT = 'DRAFT',         // 草稿
  PENDING = 'PENDING',     // 待審核
  APPROVED = 'APPROVED',   // 已核准
  REJECTED = 'REJECTED'    // 已拒絕
}

// 基本預算接口
export interface Budget {
  id: number;
  projectId: number;
  type: BudgetType;
  status: BudgetStatus;
  title: string;
  amount: number;
  usedAmount: number;      // 已使用金額
  remainingAmount: number; // 剩餘金額
  startDate: string;
  endDate: string;
  description?: string;
  approvedBy?: number;     // 核准人ID
  approvedAt?: string;     // 核准時間
  createdBy: number;
  createdAt: string;
  updatedAt: string;
}

// 創建預算的請求數據接口
export interface CreateBudgetDto {
  projectId: number;
  type: BudgetType;
  title: string;
  amount: number;
  startDate: string;
  endDate: string;
  description?: string;
}

// 更新預算的請求數據接口
export interface UpdateBudgetDto extends Partial<CreateBudgetDto> {}

// 查詢預算的過濾條件接口
export interface BudgetQueryDto {
  projectId?: number;
  type?: BudgetType;
  status?: BudgetStatus;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 預算列表響應接口
export interface BudgetListResponse {
  items: Budget[];
  total: number;
  page: number;
  pageSize: number;
}

// 預算支出記錄接口
export interface BudgetExpense {
  id: number;
  budgetId: number;
  amount: number;
  date: string;
  description: string;
  receipt?: string;        // 收據檔案路徑
  createdBy: number;
  createdAt: string;
}

// 創建預算支出的請求數據接口
export interface CreateExpenseDto {
  budgetId: number;
  amount: number;
  date: string;
  description: string;
  receipt?: string;
}

// 預算統計數據接口
export interface BudgetStatistics {
  totalBudget: number;     // 總預算
  totalExpense: number;    // 總支出
  remainingBudget: number; // 剩餘預算
  expenseByType: {        // 各類型支出統計
    [key in BudgetType]: number;
  };
  periodStart: string;
  periodEnd: string;
} 