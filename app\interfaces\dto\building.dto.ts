// 建築相關 DTO 定義
import type { BaseQueryParams, SortOrderInfo, SearchTermInfo } from './common.dto';

// 列表項目
export interface BuildingListItem {
  BuildingId: number;
  SiteCode: string;
  BuildingName: string;
  TotalAboveGroundFloors: number;
  TotalBelowGroundFloors: number;
  BuildingType: string;
  CompletionDate: string;
  CreatedTime: string;
}

// 列表響應
export interface BuildingListResponse {
  UsingPaging: boolean;
  NumberOfPperPage: number;
  PageIndex: number;
  SortOrderInfos?: SortOrderInfo[];
  SearchTermInfos?: SearchTermInfo[];
  Detail: BuildingListItem[];
  TotalPages: number;
  RecordCount: number;
}

// 查詢參數
export interface BuildingQueryDto extends BaseQueryParams {
  SiteCode?: string;
  BuildingName?: string;
  BuildingType?: string;
}

// 詳細資訊
export interface BuildingDetail {
  BuildingId: number;
  SiteCode: string;
  BuildingName: string;
  TotalAboveGroundFloors: number;
  TotalBelowGroundFloors: number;
  BuildingType: string;
  CompletionDate: string;
  Remarks?: string;
  CreatedTime: string;
  UpdatedTime?: string;
  CreatedUserInfoId?: string;
  UpdatedUserInfoId?: string;
  CreatedUserName?: string;
  UpdatedUserName?: string;
}

// 創建 DTO
export interface CreateBuildingDto {
  SiteCode: string;
  BuildingName: string;
  TotalAboveGroundFloors: number;
  TotalBelowGroundFloors: number;
  BuildingType: string;
  CompletionDate: string;
  Remarks?: string;
}

// 更新 DTO
export interface UpdateBuildingDto extends CreateBuildingDto {} 