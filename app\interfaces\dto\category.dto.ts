import { BaseQueryParams } from './common.dto';

// 基礎分類接口
export interface BaseCategoryItem {
  Name: string;
  Description?: string;
  SortOrder: number;
  IsActive: boolean;
  CreateTime: string;
}

export interface BaseCategoryDetail extends BaseCategoryItem {
  CreatedUserInfoId: string;
  CreatedUserName: string;
  UpdateTime: string;
  UpdatedUserInfoId: string;
  UpdatedUserName: string;
}

// 大分類相關接口
export interface LargeCategoryListItem extends BaseCategoryItem {
  LargeCategoryId: number;
}

export interface LargeCategoryDetail extends BaseCategoryDetail {
  LargeCategoryId: number;
}

export interface CreateLargeCategoryDto {
  Name: string;
  Description?: string;
  SortOrder: number;
  IsActive?: boolean;
}

export interface UpdateLargeCategoryDto extends CreateLargeCategoryDto {}

export interface LargeCategoryQueryDto extends BaseQueryParams {
  Name?: string;
  IsActive?: boolean | null;
}

export interface LargeCategoryListResponse {
  TotalCount?: number;
  RecordCount: number;
  PageIndex: number;
  PageSize: number;
  TotalPages: number;
  Detail: LargeCategoryListItem[];
  Details?: LargeCategoryListItem[]; // 向後相容
}

// 中分類相關接口
export interface MediumCategoryListItem extends BaseCategoryItem {
  MediumCategoryId: number;
  LargeCategoryId: number;
  LargeCategoryName: string;
}

export interface MediumCategoryDetail extends BaseCategoryDetail {
  MediumCategoryId: number;
  LargeCategoryId: number;
  LargeCategoryName: string;
}

export interface CreateMediumCategoryDto {
  LargeCategoryId: number;
  Name: string;
  Description?: string;
  SortOrder: number;
  IsActive?: boolean;
}

export interface UpdateMediumCategoryDto extends CreateMediumCategoryDto {}

export interface MediumCategoryQueryDto extends BaseQueryParams {
  LargeCategoryId?: number;
  Name?: string;
  IsActive?: boolean | null;
}

export interface MediumCategoryListResponse {
  TotalCount?: number;
  RecordCount: number;
  PageIndex: number;
  PageSize: number;
  TotalPages: number;
  Detail: MediumCategoryListItem[];
  Details?: MediumCategoryListItem[]; // 向後相容
}

// 小分類相關接口
export interface SmallCategoryListItem extends BaseCategoryItem {
  SmallCategoryId: number;
  MediumCategoryId: number;
  MediumCategoryName: string;
  LargeCategoryId: number;
  LargeCategoryName: string;
}

export interface SmallCategoryDetail extends BaseCategoryDetail {
  SmallCategoryId: number;
  MediumCategoryId: number;
  MediumCategoryName: string;
  LargeCategoryId: number;
  LargeCategoryName: string;
}

export interface CreateSmallCategoryDto {
  MediumCategoryId: number;
  Name: string;
  Description?: string;
  SortOrder: number;
  IsActive?: boolean;
}

export interface UpdateSmallCategoryDto extends CreateSmallCategoryDto {}

export interface SmallCategoryQueryDto extends BaseQueryParams {
  LargeCategoryId?: number;
  MediumCategoryId?: number;
  Name?: string;
  IsActive?: boolean | null;
}

export interface SmallCategoryListResponse {
  TotalCount?: number;
  RecordCount: number;
  PageIndex: number;
  PageSize: number;
  TotalPages: number;
  Detail: SmallCategoryListItem[];
  Details?: SmallCategoryListItem[]; // 向後相容
}

// API 回應格式
export interface CreateCategoryResponse {
  LargeCategoryId?: number;
  MediumCategoryId?: number;
  SmallCategoryId?: number;
} 