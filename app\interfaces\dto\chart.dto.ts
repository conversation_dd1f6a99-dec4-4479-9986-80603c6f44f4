// 圖表類型枚舉
export enum ChartType {
  LINE = 'line',
  BAR = 'bar',
  PIE = 'pie',
  DOUGHNUT = 'doughnut',
  RADAR = 'radar',
  POLAR_AREA = 'polarArea',
  BUBBLE = 'bubble',
  SCATTER = 'scatter',
  AREA = 'area',
  HORIZONTAL_BAR = 'horizontalBar'
}

// 基本圖表數據接口
export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

// 圖表數據集接口
export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  fill?: boolean;
  tension?: number;
  // 其他可能的屬性...
}

// 圖表選項接口
export interface ChartOptions {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: {
    title?: {
      display?: boolean;
      text?: string;
      font?: {
        size?: number;
      };
    };
    legend?: {
      display?: boolean;
      position?: 'top' | 'bottom' | 'left' | 'right';
    };
    tooltip?: {
      enabled?: boolean;
    };
  };
  scales?: {
    x?: {
      title?: {
        display?: boolean;
        text?: string;
      };
      grid?: {
        display?: boolean;
      };
    };
    y?: {
      title?: {
        display?: boolean;
        text?: string;
      };
      grid?: {
        display?: boolean;
      };
      beginAtZero?: boolean;
    };
  };
}

// 圖表屬性接口
export interface ChartProps {
  type: ChartType;
  data: ChartData;
  options?: ChartOptions;
  width?: number;
  height?: number;
  className?: string;
  showDownload?: boolean;
  downloadFileName?: string;
}

// 模擬數據生成函數接口
export interface ChartDataGenerator {
  generateLineChartData: (months?: number) => ChartData;
  generateBarChartData: (categories?: number) => ChartData;
  generatePieChartData: (slices?: number) => ChartData;
  generateDoughnutChartData: (slices?: number) => ChartData;
  generateRadarChartData: (points?: number) => ChartData;
} 