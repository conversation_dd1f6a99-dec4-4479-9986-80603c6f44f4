// 排序信息
export interface SortOrderInfo {
  SortField: string;
  SortOrder: 'asc' | 'desc';
}

// 搜索条件
export interface SearchTermInfo {
  SearchField: string;
  SearchValue: string;
}

// 基础查询参数
export interface BaseQueryParams {
  UsingPaging: boolean;
  NumberOfPperPage: number;
  PageIndex: number;
  SortOrderInfos?: SortOrderInfo[];
  SearchTermInfos?: SearchTermInfo[];
}

// 下拉選單項目介面 (通用)
export interface DropdownItem {
  Name: string;
  Value: string;
}

// 通用 API 回應介面
export interface ApiResponse<T> {
  body: T;
  message: string;
  code: string;
  isSuccess: boolean;
}

// 分頁結果介面 (通用)
export interface PagedResult<T> {
  UsingPaging?: boolean;
  NumberOfPperPage: number;
  PageIndex: number;
  SortOrderInfos?: SortOrderInfo[];
  SearchTermInfos?: SearchTermInfo[];
  Detail: T[];
  TotalPages?: number;
  RecordCount: number;
} 