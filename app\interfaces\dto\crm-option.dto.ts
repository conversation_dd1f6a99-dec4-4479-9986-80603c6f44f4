import { BaseQueryParams } from './common.dto';

// CRM選項類型下拉選單項目
export interface CrmOptionTypeDropdownItem {
  Name: string;
  Value: number;
  Description: string;
}

// CRM選項下拉選單項目
export interface CrmOptionDropdownItem {
  Name: string;
  Value: number;
  SortOrder: number;
}

// 基礎CRM選項接口
export interface BaseCrmOptionItem {
  SiteCrmOptionId: number;
  SiteCode: string;
  SiteName: string;
  CrmOptionTypeId: number;
  CrmOptionTypeName: string;
  OptionValue: string;
  SortOrder: number;
  IsActive: boolean;
  CreateTime: string;
}

// CRM選項詳細資料接口
export interface CrmOptionDetail extends BaseCrmOptionItem {
  UpdateTime: string;
  CreatedUserInfoId: string;
  CreatedUserName: string;
  UpdatedUserInfoId: string;
  UpdatedUserName: string;
}

// 查詢CRM選項參數
export interface GetCrmOptionsParams {
  SiteCode?: string;
  CrmOptionTypeId?: number;
  OptionValue?: string;
  IsActive?: boolean | null;
  PageIndex: number;
  PageSize: number;
}

// CRM選項列表回應
export interface CrmOptionListResponse {
  TotalCount: number;
  PageIndex: number;
  PageSize: number;
  TotalPages: number;
  Details: BaseCrmOptionItem[];
}

// 創建CRM選項DTO
export interface CreateCrmOptionDto {
  SiteCode: string;
  CrmOptionTypeId: number;
  OptionValue: string;
  SortOrder: number;
  IsActive?: boolean;
}

// 更新CRM選項DTO
export interface UpdateCrmOptionDto {
  OptionValue: string;
  SortOrder: number;
  IsActive: boolean;
}

// 創建CRM選項回應
export interface CreateCrmOptionResponse {
  SiteCrmOptionId: number;
}

// CRM選項下拉選單請求參數
export interface GetCrmOptionDropdownParams {
  SiteCode: string;
  CrmOptionTypeId: number;
  OnlyActive?: boolean;
}

// 擴展的查詢參數（用於前端表格）
export interface CrmOptionQueryDto extends BaseQueryParams {
  SiteCode?: string;
  CrmOptionTypeId?: number;
  OptionValue?: string;
  IsActive?: boolean | null;
}
