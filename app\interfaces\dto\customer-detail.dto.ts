import { CustomerStatus } from './customer.dto';

export interface CustomerDetail {
  id: number;
  name: string;
  gender: string;
  birthDate: string;
  age: number;
  city: string;
  district: string;
  address: string;
  phone: string;
  email: string;
  occupation: string;
  source: string;
  requiredSize: string;
  requiredLayout: string;
  budget: string;
  signature: string;
  createdAt: string;
  status: CustomerStatus;
  assignedTo: string;
  importantConditions?: string;
  purpose?: string;
  plannedTime?: string;
  notes?: string;
  followUps: FollowUp[];
}

export interface FollowUp {
  id: number;
  content: string;
  createdAt: string;
  createdBy: string;
}

export interface EditModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: CustomerDetail | null;
  onSave: (editedCustomer: CustomerDetail) => void;
} 