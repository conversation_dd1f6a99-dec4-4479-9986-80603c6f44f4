import { BaseQueryParams, SortOrderInfo, SearchTermInfo } from './common.dto';

// 客戶狀態枚舉
export enum CustomerStatus {
  New = 'New',           // 新客戶
  InProgress = 'InProgress', // 跟進中
  Qualified = 'Qualified',   // 合格客戶
  Contracted = 'Contracted', // 已簽約
  Lost = 'Lost',         // 流失客戶
}

// 客戶來源枚舉
export enum CustomerSource {
  Website = 'Website',       // 網站
  Referral = 'Referral',     // 推薦
  Exhibition = 'Exhibition', // 展覽
  SocialMedia = 'SocialMedia', // 社群媒體
  Phone = 'Phone',         // 電話諮詢
  Email = 'Email',         // 電子郵件
  Other = 'Other',         // 其他
}

// 新增：客戶記錄介面
export interface CustomerRecord {
  CustomerRecordId?: number;
  RecordType: string;
  Notes: string;
  RecordedAt?: string;
  CustomerLevel: string;
  HandledBy?: string;
}

// 更新 Customer 介面 (用於 GetCustomer/{id} 詳細資料)
export interface Customer {
  CustomerId: string;
  Name: string;
  Gender?: string; // 添加 Gender
  Birthday?: string; // 添加 Birthday (日期字串)
  City?: string; // 添加 City
  District?: string; // 添加 District
  Address?: string; // Address -> Address (保持)
  PhoneNumber: string;
  Email?: string;
  FullAddress?: string; // 保持 FullAddress (如果 API 有返回)
  Occupation?: string; // 添加 Occupation
  LeadSource?: string; // 添加 LeadSource
  PurchaseConditions?: string; // Added field
  RequiredPingArea?: string; // 添加 RequiredPingArea
  RequiredLayout?: string; // 添加 RequiredLayout
  Budget?: string; // 添加 Budget
  SiteCode?: string;
  ImagePath?: string; // 添加 ImagePath
  CreatedTime: string; // CreateTime -> CreatedTime
  CreatedUserInfoId?: string; // CreateUserInfoId -> CreatedUserInfoId
  UpdatedTime?: string; // 添加 UpdatedTime
  UpdatedUserInfoId?: string; // UpdateUserInfoId -> UpdatedUserInfoId
  CustomerRecords?: CustomerRecord[]; // 添加 CustomerRecords
  CreatedUserName?: string;
  UpdatedUserName?: string;
  // 移除之前假設的字段 Status, Source, AssignedTo, ProjectId, Rating, Tags, Remarks
}

// 更新 CreateCustomerDto (用於 POST /CreateCustomer)
export interface CreateCustomerDto {
  Name: string;
  Gender?: string;
  Birthday?: string; // 日期字串 YYYY-MM-DD
  City?: string;
  District?: string;
  Address?: string;
  PhoneNumber: string;
  Email?: string;
  Occupation?: string;
  LeadSource?: string;
  PurchaseConditions?: string;
  RequiredPingArea?: string;
  RequiredLayout?: string;
  Budget?: string;
  Note?: string;
  ImageBase64?: string; // 對應 signature
  SiteCode?: string; // 假設需要 SiteCode
  CustomerRecords?: CustomerRecord[]; // 通常新增時可能是空陣列
}

// 新增：UpdateCustomerDto (用於 POST /UpdateCustomer/{id})
export interface UpdateCustomerDto {
  Name: string;
  Gender?: string;
  Birthday?: string; // 日期字串 YYYY-MM-DD
  City?: string;
  District?: string;
  Address?: string;
  PhoneNumber: string;
  Email?: string;
  Occupation?: string;
  LeadSource?: string;
  PurchaseConditions?: string; // Added field
  RequiredPingArea?: string;
  RequiredLayout?: string;
  Budget?: string;
  Note?: string;
  ImageBase64?: string;
  SiteCode?: string;
  CustomerRecords?: CustomerRecord[]; // 包含客戶記錄的更新
}

// CustomerQueryDto 保持不變 (包含 SortOrderInfos, SearchTermInfos)
export interface CustomerQueryDto {
  page?: number;
  pageSize?: number;
  SortOrderInfos?: SortOrderInfo[];
  SearchTermInfos?: SearchTermInfo[];
}

// CustomerListResponse 保持不變 (包含 body.Detail, body.RecordCount)
interface CustomerListResponseBody {
  Detail: Customer[];
  TotalPages: number;
  RecordCount: number;
  UsingPaging: boolean;
  NumberOfPperPage: number;
  PageIndex: number;
  SortOrderInfos: any[];
  SearchTermInfos: any[];
}
export interface CustomerListResponse {
  body: CustomerListResponseBody;
  message: string;
  code: string;
  isSuccess: boolean;
}

// 新增：GetCustomer/{id} 的完整響應結構
export interface GetCustomerResponse {
  body: Customer; // body 直接包含 Customer 對象
  message: string;
  code: string;
  isSuccess: boolean;
}

// 客戶跟進記錄接口
export interface CustomerFollowUp {
  FollowUpId: number;
  CustomerId: number;
  Content: string;
  FollowUpType: FollowUpType;
  NextFollowUpDate?: string;
  Result?: string;
  ContactMethod: ContactMethod;
  CreateTime: string;
  CreatedBy: number;
  CreatedByName?: string;
}

export enum FollowUpType {
  FirstContact = 'FirstContact',
  Negotiation = 'Negotiation',
  Proposal = 'Proposal',
  Quotation = 'Quotation',
  ContractReview = 'ContractReview',
  AfterSales = 'AfterSales',
  Other = 'Other',
}

export enum ContactMethod {
  Phone = 'Phone',
  Email = 'Email',
  Meeting = 'Meeting',
  SocialMedia = 'SocialMedia',
  Video = 'Video',
  Other = 'Other',
}

// 創建跟進記錄的請求數據接口
export interface CreateFollowUpDto {
  customerId: number;
  content: string;
  type: string;
  nextFollowUpDate?: string;
} 