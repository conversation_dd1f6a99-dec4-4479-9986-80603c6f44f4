import type { BaseQueryParams, SortOrderInfo, SearchTermInfo } from './common.dto';

// 樓層列表項目
export interface FloorListItem {
  FloorId: number;
  BuildingId: number;
  SiteCode: string;
  FloorLabel: string;
  FloorLevel: number;
  FloorType: string;
  FloorHeight: number;
  CreatedTime: string;
}

// 樓層列表響應
export interface FloorListResponse {
  UsingPaging: boolean;
  NumberOfPperPage: number;
  PageIndex: number;
  SortOrderInfos?: SortOrderInfo[];
  SearchTermInfos?: SearchTermInfo[];
  Detail: FloorListItem[];
  TotalPages: number;
  RecordCount: number;
}

// 樓層查詢參數
export interface FloorQueryDto extends BaseQueryParams {
  BuildingId?: number;
  FloorType?: string;
}

// 創建樓層 DTO
export interface CreateFloorDto {
  BuildingId: number;
  SiteCode: string;
  FloorLabel: string;
  FloorLevel: number;
  FloorType: string;
  FloorHeight: number;
  Remarks?: string;
}

// 更新樓層 DTO
export interface UpdateFloorDto extends CreateFloorDto {
  FloorId: number;
}

// 樓層詳細資訊
export interface FloorDetail extends UpdateFloorDto {
  CreatedTime: string;
  UpdatedTime?: string;
  CreatedUserInfoId?: string;
  UpdatedUserInfoId?: string;
  CreatedUserName?: string;
  UpdatedUserName?: string;
  Remarks?: string;
} 