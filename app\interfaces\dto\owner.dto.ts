// 基本業主接口
export interface Owner {
  id: number;
  name: string;
  phone: string;
  address: string;
  email?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// 創建業主的請求數據接口
export interface CreateOwnerDto {
  name: string;
  phone: string;
  address: string;
  email?: string;
  notes?: string;
}

// 更新業主的請求數據接口
export interface UpdateOwnerDto extends Partial<CreateOwnerDto> {}

// 查詢業主的過濾條件接口
export interface OwnerQueryDto {
  keyword?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 業主列表響應接口
export interface OwnerListResponse {
  items: Owner[];
  total: number;
  page: number;
  pageSize: number;
} 