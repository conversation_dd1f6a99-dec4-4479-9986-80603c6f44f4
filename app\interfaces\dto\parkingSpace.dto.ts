import type { BaseQueryParams, PagedResult, SortOrderInfo, SearchTermInfo } from './common.dto';

/**
 * 車位查詢輸入參數 (對應 ParkingSpaceQueryInput)
 */
export interface ParkingSpaceQueryInput {
  // 分頁設定
  usingPaging: boolean;
  numberOfPerPage: number;
  pageIndex: number;
  
  // 篩選條件
  siteCode?: string;
  buildingId?: number;
  floorId?: number;
  spaceNumber?: string;       // 車位編號 (模糊查詢)
  spaceType?: string;         // 車位類型
  location?: string;          // 位置描述 (模糊查詢)
  
  // 排序設定
  sortOrderInfos?: SortOrderInfo[];
  searchTermInfos?: SearchTermInfo[];
}

/**
 * 車位列表輸出 (對應 ParkingSpaceListOutput)
 */
export interface ParkingSpaceListOutput {
  parkingSpaceId: number;     // 車位ID
  siteCode: string;           // 案場編號
  siteName?: string;          // 案場名稱
  buildingId: number;         // 建築物ID
  buildingName?: string;      // 建築物名稱
  floorId: number;            // 樓層ID
  floorLabel?: string;        // 樓層標示
  spaceNumber: string;        // 車位編號
  spaceType: string;          // 車位類型
  dimensions?: string;        // 車位尺寸
  location?: string;          // 位置描述
  remarks?: string;           // 備註
  createdTime: string;        // 建立時間 (ISO 8601)
  updatedTime: string;        // 更新時間 (ISO 8601)
}

/**
 * 建立車位輸入參數 (對應 ParkingSpaceCreateInput)
 */
export interface ParkingSpaceCreateInput {
  siteCode: string;           // 案場編號 (必填)
  buildingId: number;         // 建築物ID (必填)
  floorId: number;            // 樓層ID (必填)
  spaceNumber: string;        // 車位編號 (必填)
  spaceType: string;          // 車位類型 (必填)
  dimensions?: string;        // 車位尺寸 (可選)
  location?: string;          // 位置描述 (可選)
  remarks?: string;           // 備註 (可選)
}

/**
 * 更新車位輸入參數 (對應 ParkingSpaceUpdateInput)
 */
export interface ParkingSpaceUpdateInput {
  spaceNumber?: string;       // 車位編號
  spaceType?: string;         // 車位類型
  dimensions?: string;        // 車位尺寸
  location?: string;          // 位置描述
  remarks?: string;           // 備註
}

/**
 * 車位詳細資訊輸出 (對應 ParkingSpaceOutput)
 */
export interface ParkingSpaceOutput extends ParkingSpaceListOutput {
  createdUserInfoId?: string; // 建立者ID
  updatedUserInfoId?: string; // 更新者ID
}

/**
 * 分頁列表輸出 (對應 PagedListOutput)
 */
export interface PagedListOutput<T> {
  // 分頁資訊
  usingPaging: boolean;
  numberOfPerPage: number;
  pageIndex: number;
  totalPages: number;
  recordCount: number;
  
  // 資料列表
  details: T[];
  
  // 排序和搜尋資訊
  sortOrderInfos: SortOrderInfo[];
  searchTermInfos: SearchTermInfo[];
}

/**
 * 車位建立結果
 */
export interface ParkingSpaceCreateResult {
  parkingSpaceId: number;     // 新建立的車位ID
  success: boolean;           // 是否成功
  message?: string;           // 回應訊息
}

/**
 * 操作結果
 */
export interface OperationResult {
  success: boolean;           // 是否成功
  message?: string;           // 回應訊息
}

// === 向後相容的舊介面 (保留給現有代碼使用) ===

/**
 * @deprecated 請使用 ParkingSpaceListOutput
 */
export interface ParkingSpaceListItemDto extends ParkingSpaceListOutput {
  ParkingSpaceId: number;
  SiteCode: string;
  BuildingId: number;
  FloorId: number;
  SpaceNumber: string;
  SpaceType: string;
  ListPrice: number | null;
  Status: string;
  AssociatedUnitId: number | null;
  CreatedTime: string;
  FloorLabel: string | null;
  BuildingName: string | null;
  AssociatedUnitNumber: string | null;
  Dimensions?: string | null;
  MinimumPrice?: number | null;
  Remarks?: string | null;
}

/**
 * @deprecated 請使用 ParkingSpaceQueryInput
 */
export interface ParkingSpaceQueryDto extends BaseQueryParams {
  FloorId?: number | null;
  SpaceNumber?: string | null;
  SpaceType?: string | null;
  Status?: string | null;
  IsAssociated?: boolean | null;
  SiteCode?: string | null;
  BuildingId?: number | null;
}

/**
 * @deprecated 請使用 ParkingSpaceCreateInput
 */
export interface CreateParkingSpaceDto extends ParkingSpaceCreateInput {
  SiteCode: string;
  BuildingId: number;
  FloorId: number;
  SpaceNumber: string;
  SpaceType: string;
  Dimensions?: string | null;
  ListPrice?: number | null;
  MinimumPrice?: number | null;
  Status: string;
  AssociatedUnitId?: number | null;
  Remarks?: string | null;
}

/**
 * @deprecated 請使用 ParkingSpaceUpdateInput
 */
export interface UpdateParkingSpaceDto extends Partial<CreateParkingSpaceDto> {}

/**
 * @deprecated 請使用 ParkingSpaceOutput
 */
export interface ParkingSpaceDetailDto extends ParkingSpaceListItemDto {}

/**
 * @deprecated 請使用 PagedListOutput<ParkingSpaceListOutput>
 */
export interface ParkingSpaceListPagedResult extends PagedResult<ParkingSpaceListItemDto> {} 