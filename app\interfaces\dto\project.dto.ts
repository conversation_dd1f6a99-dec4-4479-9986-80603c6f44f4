// 基本案場接口
export interface Project {
  id: number;
  name: string;
  address: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

// 創建案場的請求數據接口
export interface CreateProjectDto {
  name: string;
  address: string;
  description: string;
}

// 更新案場的請求數據接口
export interface UpdateProjectDto extends Partial<CreateProjectDto> {}

// 查詢案場的過濾條件接口
export interface ProjectQueryDto {
  keyword?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 案場列表響應接口
export interface ProjectListResponse {
  items: Project[];
  total: number;
  page: number;
  pageSize: number;
} 