import { BaseQueryParams, ApiResponse } from './common.dto';

// 定義狀態類型
export type ReviewTaskStatus = 'enable' | 'disable' | 'deleted';

// 狀態顯示文字映射
export const reviewTaskStatusMap: Record<ReviewTaskStatus, string> = {
  enable: '啟用',
  disable: '停用',
  deleted: '已刪除'
};

// 審核流程步驟
export interface ReviewTaskStep {
  Name: string;
  ApproverIds: string[];
  TimeLimit: number;
  SystemNotification: boolean;
  EmailNotification: boolean;
  SmsNotification: boolean;
}

// 審核人員
export interface ReviewApproverOutput {
  approverId: number;
  userInfoId: string;
}

// 基礎審核流程資訊
export interface ReviewTaskOutput {
  TaskId: number;
  Name: string;
  Description: string;
  TotalStep: number;
  CreatedTime: string;
  CreatedUserInfoId: string;
  UpdatedTime?: string;
  UpdatedUserInfoId?: string;
  Status: ReviewTaskStatus;
}

// 審核流程列表項目
export interface ReviewTaskListItem {
  taskId: number;
  name: string;
  description: string;
  status: ReviewTaskStatus;
  totalStep: number;
  currentStep: number;
  createdTime: string;
  updatedTime?: string;
  createdUserInfoId: string;
  updatedUserInfoId?: string;
}

// 審核流程詳細資訊
export interface ReviewTaskDetailOutput {
  TaskId: number;
  Name: string;
  Description: string;
  TotalStep: number;
  CreatedTime: string;
  CreatedUserInfoId: string;
  UpdatedTime?: string;
  UpdatedUserInfoId?: string;
  Status: ReviewTaskStatus;
  Deadline?: string;
  Steps: ReviewTaskStep[];
}

// 審核歷史記錄
export interface ReviewHistoryOutput {
  historyId: number;
  taskId: number;
  stepId?: number;
  stepName: string;
  userInfoId: string;
  userName: string;
  action: '同意' | '拒絕' | '退回';
  comment: string;
  timestamp?: Date;
}

// 下一步驟資訊
export interface ReviewNextStepOutput {
  stepId: number;
  stepName: string;
  sequence: number;
  approvers: ReviewApproverOutput[];
  timeLimit: number;
  notifyMethod: string[];
}

// API 響應類型
export type ReviewTaskResponse = ApiResponse<ReviewTaskOutput>;
export type ReviewTaskDetailResponse = ApiResponse<ReviewTaskDetailOutput>;
export type ReviewTaskListResponse = ApiResponse<{ Detail: ReviewTaskOutput[]; RecordCount: number; TotalPages: number }>;
export type ReviewHistoryResponse = ApiResponse<{ histories: ReviewHistoryOutput[] }>;
export type ReviewNextStepResponse = ApiResponse<{ nextStep: ReviewNextStepOutput | null }>;

// 請求 DTO
export interface CreateReviewTaskDto {
  Name: string;
  Description: string;
  Status?: ReviewTaskStatus;
  Deadline?: Date;
  Steps: ReviewTaskStep[];
}

export interface AddReviewHistoryDto {
  taskId: number;
  stepId?: number;
  action: '同意' | '拒絕' | '退回';
  comment: string;
}

// 審核流程查詢參數
export interface ReviewTaskQueryParams extends BaseQueryParams {
  name?: string;
  creatorUserInfoId?: string;
  status?: ReviewTaskStatus;
  startDate?: Date;
  endDate?: Date;
}

export interface ReviewUser {
  id: number;
  username: string;
  name: string;
  email: string;
  role: string;
  department: string;
  isActive: boolean;
}

export interface ReviewUserInfo {
  UserInfoId: string;
  UserName: string;
  DeptId: string;
  DeptName: string;
  GradeCode: string;
  GradeName: string;
}

export interface ReviewDepartment {
  Dept: {
    DeptName: string;
  };
  Users: ReviewUserInfo[];
}

export interface ReviewUserResponse {
  body: ReviewDepartment[];
  message: string;
  code: string;
  isSuccess: boolean;
} 