import type { BaseQueryParams } from './common.dto';

// 銷售狀態類型 (更新後的五種狀態)
export type UnitSalesStatus = '可售' | '保留' | '售' | '足' | '簽';

// 單位資訊 (根據真實API)
export interface SalesControlUnitInfo {
  UnitId: number;
  UnitNumber: string; // A, B, C, etc.
  Status: string;
  PurchaseInfo?: string; // 可選，因為API可能不返回
  SalePrice?: number; // 可選
  Area: number; // 坪數
  UnitType: string; // 房型
  DisplayValue?: string; // 可選，用於摘要行顯示
  FloorLabel: string;
  BuildingName: string;
  SaleDate?: string; // 售出日期
  CustomerName?: string; // 客戶姓名
  PurchasedParkingSpaces?: string; // 車位資訊
  RequestDate?: string; // 請的日期
  ReceiveDate?: string; // 領的日期
  PriceRegistrationSubmissionDate?: string; // 實價登錄日期
}

// 樓層資訊 (根據真實API)
export interface SalesControlFloorInfo {
  FloorNumber: string; // 25F, 20F, ..., 店面
  FloorId: number;
  FloorLevel: number;
  FloorLabel: string;
  Units: SalesControlUnitInfo[];
  Summary: {
    Available: number; // 可售
    Sold: number; // 已售
    Reserved: number; // 保留
    SalesRate: string | number; // 去化百分比 - 後端可能傳回數字或字串
  };
}

// 銷控表資料結構 (根據真實API)
export interface SalesControlData {
  SiteCode: string; // 案場代碼
  BuildingId?: number; // 建築物ID（可選，當查詢全部時沒有）
  BuildingName?: string; // 建築物名稱（可選）
  Floors: SalesControlFloorInfo[];
  ColumnSummary: Array<{
    Column: string; // A, B, C, etc.
    Available: number;
    Sold: number;
    Reserved: number; // 新增保留數量
    SalesRate: string | number; // 去化百分比 - 後端可能傳回數字或字串
  }>;
  TotalSummary: {
    TotalAvailable: number;
    TotalSold: number;
    TotalReserved: number; // 新增總保留數量
    OverallSalesRate: string | number; // 去化百分比 - 後端可能傳回數字或字串
  };
}

// 銷控表列表回應
export interface SalesControlListResponse {
  Detail: SalesControlData[];
  TotalPages: number;
  RecordCount: number;
  NumberOfPperPage: number;
  PageIndex: number;
  UsingPaging: boolean;
  SortOrderInfos?: { SortField: string; SortOrder: string }[];
  SearchTermInfos?: { SearchField: string; SearchValue: string }[];
}

// 取得銷控表參數 (根據真實API)
export interface GetSalesControlParams {
  SiteCode: string;
  BuildingId?: number; // 改為可選參數，當選擇"全部"時不需要傳遞
}

// 案場銷售統計摘要
export interface SalesStatistics {
  SiteCode: string;
  TotalUnits: number;
  AvailableUnits: number;
  SoldUnits: number;
  ReservedUnits: number;
  SalesRate: number;
}

// 更新單位狀態 DTO (根據真實API)
export interface UpdateUnitStatusDto {
  Status: string;
  PurchaseInfo: string;
  SalePrice: number;
  Area: number;
  UnitType: string;
  OrderId: number;
  Remarks: string;
  CustomerName?: string; // 客戶姓名
  SaleDate?: string; // 售出日期
  PurchasedParkingSpaces?: string; // 車位資訊
}

// 批次更新單位狀態 DTO
export interface BatchUpdateUnitsDto {
  SiteCode: string;
  Units: UpdateUnitStatusDto[];
} 