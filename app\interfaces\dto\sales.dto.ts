// 銷售狀態枚舉
export enum SaleStatus {
  RESERVED = 'RESERVED',     // 已預訂
  CONTRACT = 'CONTRACT',     // 已簽約
  PAID = 'PAID',            // 已付訂
  COMPLETED = 'COMPLETED',  // 已完成
  CANCELLED = 'CANCELLED'   // 已取消
}

// 基本銷售訂單接口
export interface Sale {
  id: number;
  projectId: number;
  customerId: number;
  salesPersonId: number;
  unitNumber: string;      // 房號
  price: number;          // 成交價格
  status: SaleStatus;
  reservationDate?: string;// 預訂日期
  contractDate?: string;   // 簽約日期
  depositAmount?: number;  // 訂金金額
  depositDate?: string;    // 訂金日期
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// 創建銷售訂單的請求數據接口
export interface CreateSaleDto {
  projectId: number;
  customerId: number;
  salesPersonId: number;
  unitNumber: string;
  price: number;
  status: SaleStatus;
  reservationDate?: string;
  contractDate?: string;
  depositAmount?: number;
  depositDate?: string;
  notes?: string;
}

// 更新銷售訂單的請求數據接口
export interface UpdateSaleDto extends Partial<CreateSaleDto> {}

// 查詢銷售訂單的過濾條件接口
export interface SaleQueryDto {
  projectId?: number;
  customerId?: number;
  salesPersonId?: number;
  status?: SaleStatus;
  startDate?: string;
  endDate?: string;
  minPrice?: number;
  maxPrice?: number;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 銷售訂單列表響應接口
export interface SaleListResponse {
  items: Sale[];
  total: number;
  page: number;
  pageSize: number;
}

// 銷售佣金計算接口
export interface CommissionCalculation {
  saleId: number;
  salesPersonId: number;
  baseAmount: number;     // 基本佣金
  bonusAmount: number;    // 獎金
  totalAmount: number;    // 總佣金
  calculatedAt: string;
}

// 銷售統計數據接口
export interface SalesStatistics {
  totalSales: number;     // 總銷售額
  totalDeals: number;     // 成交數量
  averagePrice: number;   // 平均成交價
  periodStart: string;    // 統計週期開始
  periodEnd: string;      // 統計週期結束
} 