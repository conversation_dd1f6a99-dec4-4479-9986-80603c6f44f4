import { BaseQueryParams, SortOrderInfo, SearchTermInfo } from './common.dto';
import { UploadFile } from 'antd/es/upload/interface'; // Import UploadFile type

// 基本的供應商檔案介面 (用於新增/更新)
export interface SupplierFileBase {
  FormType: string;
  FileName?: string; // Optional, set after upload
  FilePath?: string; // Optional, set after upload
  Remark?: string;
  Agent?: string; // User ID from UserInfoApi
  AgentName?: string; // User Name for display, not sent to backend directly
  SupplierFileId?: number; // 在新增時可能為 0 或後端生成，更新時需要
  
  // This field is for managing the Ant Design Upload component's state in the form.
  // It will hold the file object before it's uploaded or represent an existing file for display.
  // It is NOT part of the DTO sent to the backend for supplier creation/update in this exact form.
  // The actual FileName and FilePath are extracted/set after upload.
  File?: UploadFile[]; // Stores Antd Upload component's fileList (typically one file due to maxCount=1)
}

// 供應商詳細資料中的檔案介面 (包含審計欄位)
export interface SupplierFileDetail extends SupplierFileBase {
  SupplierId: number;
  CreatedUserInfoId?: string;
  CreatedUserName?: string;
  UpdatedUserInfoId?: string;
  UpdatedUserName?: string;
  UpdatedTime: string; // ISO Date string
  CreatedTime: string; // ISO Date string
}

// 供應商列表項目介面
export interface SupplierListItem {
  SupplierId: number;
  Name: string;
  PersonType: string;
  ContactPerson: string;
  IdNumber?: string; // 根據列表 output，IdNumber 可能不存在
  CompanyPhone?: string; // 根據列表 output，CompanyPhone 可能不存在
  ContactName: string; // 根據列表 output
  ContactPhone1?: string; // 根據列表 output，ContactPhone1 可能不存在 (列表為 ContactPhone1)
  FileCount: number;
  UpdatedTime: string; // ISO Date string
  UpdatedUserName?: string; // 根據列表 output，UpdatedUserName 可能不存在
}

// 獲取供應商列表的請求參數介面
export interface GetSuppliersParams extends BaseQueryParams {
  Name?: string;
  PersonType?: string;
  IdNumber?: string;
  ContactName?: string;
  ContactPhone?: string; // 依照 GetSuppliers input
}

// 獲取供應商列表的回應介面
export interface GetSuppliersResponse {
  UsingPaging?: boolean; // 依照 GetSuppliers output，這些是可選的
  NumberOfPperPage?: number;
  PageIndex?: number;
  SortOrderInfos?: SortOrderInfo[];
  SearchTermInfos?: SearchTermInfo[];
  Detail: SupplierListItem[];
  TotalPages: number;
  RecordCount: number;
}

// 新增供應商的請求資料傳輸物件 (DTO) 介面
export interface CreateSupplierDto {
  Name: string;
  PersonType: string;
  ContactPerson: string; // 聯絡人
  IdType?: string; // 證件類型
  IdNumber?: string; // 證件號碼
  CompanyPhone?: string; // 公司電話
  Address?: string; // 地址
  ContactName: string; // 接洽人姓名 (與 ContactPerson 區別?)
  ContactPhone1: string; // 主要聯絡電話
  ContactPhone2?: string; // 次要聯絡電話
  Email?: string;
  BankName?: string; // 銀行名稱
  BankBranch?: string; // 分行名稱
  AccountName?: string; // 帳戶名稱
  Files?: SupplierFileBase[]; // <-- 改回 SupplierFileBase[]
}

// 供應商詳細資訊介面
export interface SupplierDetail {
  SupplierId: number;
  Name: string;
  PersonType: string;
  ContactPerson: string;
  IdType?: string;
  IdNumber?: string;
  CompanyPhone?: string;
  Address?: string;
  ContactName: string;
  ContactPhone1: string;
  ContactPhone2?: string;
  Email?: string;
  BankName?: string;
  BankBranch?: string;
  AccountName?: string;
  CreatedUserInfoId?: string;
  CreatedUserName?: string;
  UpdatedUserInfoId?: string;
  UpdatedUserName?: string;
  UpdatedTime: string; // ISO Date string
  CreatedTime: string; // ISO Date string
  Files?: SupplierFileDetail[];
}

// 更新供應商的請求資料傳輸物件 (DTO) 介面
// 通常與 CreateSupplierDto 相似，但 SupplierId 在 URL 中
export interface UpdateSupplierDto {
  Name: string;
  PersonType: string;
  ContactPerson: string;
  IdType?: string;
  IdNumber?: string;
  CompanyPhone?: string;
  Address?: string;
  ContactName: string;
  ContactPhone1: string;
  ContactPhone2?: string;
  Email?: string;
  BankName?: string;
  BankBranch?: string;
  AccountName?: string;
  Files?: SupplierFileBase[]; // <-- 改回 SupplierFileBase[]
}

// API 泛型回應 (若後端僅回傳成功與否及訊息)
export interface ApiResponse<T = any> {
  isSuccess: boolean;
  message: string;
  body?: T; // 可選的 body，根據實際 API 回應調整
  code?: string; // 可選的錯誤碼或狀態碼
} 