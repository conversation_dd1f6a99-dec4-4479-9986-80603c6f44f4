// 統一銷售管理相關的 DTO 定義

import { BaseQueryParams, SortOrderInfo, SearchTermInfo, ApiResponse, PagedResult } from './common.dto';

// 查詢參數介面 (繼承基礎查詢參數)
export interface UnifiedSalesQueryParams extends BaseQueryParams {
  itemType?: '房屋' | '車位'; // 物件類別
  siteCode?: string; // 案場編號
  buildingId?: number; // 建築物ID
  floorId?: number; // 樓層ID
  number?: string; // 編號/戶號 (模糊查詢)
  status?: '可售' | '保留' | '已預訂' | '已售'; // 狀態
}

// 統一銷售項目輸出介面
export interface UnifiedSalesItem {
  id: number; // 物件ID (房屋為UnitId，車位為ParkingSpaceId)
  itemType: '房屋' | '車位'; // 物件類別
  siteCode: string; // 案場編號
  siteName?: string; // 案場名稱
  buildingId: number; // 建築物ID
  buildingName?: string; // 建築物名稱
  floorId: number; // 樓層ID
  floorLabel?: string; // 樓層標示
  number: string; // 編號 (房屋為戶號，車位為車位編號)
  type: string; // 類型 (房屋為UnitType，車位為SpaceType)
  area?: number; // 面積 (僅房屋有，車位為null)
  layout?: string; // 格局/尺寸 (房屋為格局，車位為尺寸)
  listPrice?: number; // 表價
  minimumPrice?: number; // 底價
  transactionPrice?: number; // 成交價
  status: string; // 狀態
  remarks?: string; // 備註
  createdTime: string; // 建立時間 (ISO 8601)
  updatedTime: string; // 更新時間 (ISO 8601)
}

// 統一銷售列表回應 (使用通用的 PagedResult)
export interface UnifiedSalesListResponse extends PagedResult<UnifiedSalesItem> {}

// 房屋銷售統計
export interface UnitSalesStatistics {
  siteCode: string;
  availableCount: number; // 可售數量
  reservedCount: number; // 保留數量
  bookedCount: number; // 已預訂數量
  soldCount: number; // 已售數量
  totalCount: number; // 總數量
  salesRate: number; // 銷售率 (%)
  totalAvailableListPrice: number; // 可售總表價
  totalReservedListPrice: number; // 保留總表價
}

// 車位銷售統計
export interface ParkingSpaceSalesStatistics {
  siteCode: string;
  availableCount: number; // 可售數量
  reservedCount: number; // 保留數量
  ownerReservedCount: number; // 地主保留數量
  bookedCount: number; // 已預訂數量
  soldCount: number; // 已售數量
  totalCount: number; // 總數量
  salesRate: number; // 銷售率 (%)
  totalAvailableListPrice: number; // 可售總表價
  totalReservedListPrice: number; // 保留總表價
}

// 統一銷售統計
export interface UnifiedSalesStatistics {
  siteCode: string; // 案場編號
  unitStatistics: UnitSalesStatistics; // 房屋統計
  parkingSpaceStatistics: ParkingSpaceSalesStatistics; // 車位統計
  totalAvailableCount: number; // 總可售數量
  totalReservedCount: number; // 總保留數量
  totalSoldCount: number; // 總已售數量
  generatedTime: string; // 統計產生時間
}

// 房屋列表項目 (用於表格顯示)
export interface HouseListItem extends UnifiedSalesItem {
  // 可以添加額外的顯示用欄位
  customerName?: string; // 客戶名稱
  saleDate?: string; // 售日期
  reserveDate?: string; // 定日期
  contractDate?: string; // 簽日期
  totalAmount?: number; // 房地車總金額
  salesPersonCommission?: string; // 業務業績比率
}

// 車位列表項目 (用於表格顯示)
export interface ParkingListItem extends UnifiedSalesItem {
  // 可以添加額外的顯示用欄位
  houseOrderNumber?: string; // 房屋訂單編號
  customerName?: string; // 客戶名稱
  saleDate?: string; // 售日期
  reserveDate?: string; // 定日期
  contractDate?: string; // 簽日期
  executionPrice?: number; // 執行價
  executionStatus?: string; // 執行狀況
  orderNumber?: string; // 訂單編號
} 