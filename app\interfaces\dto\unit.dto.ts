import type { BaseQueryParams, PagedResult, SortOrderInfo, SearchTermInfo } from './common.dto';

// 房屋單位列表項目
export interface UnitListItemDto {
  UnitId: number;
  SiteCode: string;
  BuildingId?: number;
  BuildingName?: string;
  FloorId?: number;
  FloorLabel?: string;
  UnitNumber: string;
  Layout?: string;
  UnitType?: string;
  TotalArea?: number;
  ListPrice?: number;
  Status?: string;
  CreatedTime: string;
  CreatedUserInfoId?: string;
  UpdatedTime?: string;
  UpdatedUserInfoId?: string;
  CreatedUserName?: string;
  UpdatedUserName?: string;
  [key: string]: any;
}

// 查詢參數
export interface UnitQueryDto extends BaseQueryParams {
  SiteCode?: string;
  BuildingId?: number;
  FloorId?: number;
  Status?: string;
}

// 新增/編輯 DTO
export interface CreateUnitDto {
  FloorId: number;
  BuildingId: number;
  SiteCode: string;
  UnitNumber: string;
  UnitType: string;
  Layout: string;
  Orientation?: string;
  MainArea?: number;
  AuxiliaryArea?: number;
  PublicAreaShare?: number;
  TotalArea?: number;
  ListPrice?: number;
  MinimumPrice?: number;
  Status: string;
  IsPublicAreaIncluded?: boolean;
  AssociatedParkingSpaceIds?: string;
  Remarks?: string;
}

export interface UpdateUnitDto {
  FloorId?: number;
  BuildingId?: number;
  SiteCode?: string;
  UnitNumber?: string;
  UnitType?: string;
  Layout?: string;
  Orientation?: string;
  MainArea?: number;
  AuxiliaryArea?: number;
  PublicAreaShare?: number;
  TotalArea?: number;
  ListPrice?: number;
  MinimumPrice?: number;
  TransactionPrice?: number;
  Status?: string;
  IsPublicAreaIncluded?: boolean;
  AssociatedParkingSpaceIds?: string;
  Remarks?: string;
}

export interface UnitDetailDto {
  UnitId: number;
  FloorId: number;
  BuildingId: number;
  SiteCode: string;
  UnitNumber: string;
  UnitType: string;
  Layout: string;
  Orientation?: string;
  MainArea?: number;
  AuxiliaryArea?: number;
  PublicAreaShare?: number;
  TotalArea?: number;
  ListPrice?: number;
  MinimumPrice?: number;
  TransactionPrice?: number;
  Status: string;
  IsPublicAreaIncluded?: boolean;
  AssociatedParkingSpaceIds?: string;
  Remarks?: string;
  CreatedTime: string;
  UpdatedTime?: string;
  CreatedUserInfoId?: string;
  UpdatedUserInfoId?: string;
  CreatedUserName?: string;
  UpdatedUserName?: string;
  FloorLabel?: string;
  BuildingName?: string;
}

export type UnitListPagedResult = PagedResult<UnitListItemDto>; 