// 用戶角色枚舉
export enum UserRole {
  ADMIN = 'ADMIN',           // 系統管理員
  MANAGER = 'MANAGER',       // 主管
  SALES = 'SALES',          // 銷售人員
  OPERATION = 'OPERATION',   // 營運人員
  FINANCE = 'FINANCE',       // 財務人員
  USER = 'USER'
}

// 用戶狀態枚舉
export enum UserStatus {
  ACTIVE = 'ACTIVE',        // 啟用
  INACTIVE = 'INACTIVE',    // 停用
  PENDING = 'PENDING'       // 待審核
}

// 基本用戶接口
export interface User {
  id: number;
  username: string;
  email: string;
  name: string;
  role: UserRole;
  status: UserStatus;
  phone?: string;
  department?: string;
  position?: string;
  avatar?: string;         // 頭像URL
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// 使用者下拉選單
export interface UserDropdownDto {
  DepartmentId?: string;
  JobTitleId?: string;
  RoleGroupId?: string;
  Name?: string;
}

// 創建用戶的請求數據接口
export interface CreateUserDto {
  username: string;
  email: string;
  password: string;
  name: string;
  role: UserRole;
  phone?: string;
  department?: string;
  position?: string;
  avatar?: string;
}

// 更新用戶的請求數據接口
export interface UpdateUserDto extends Partial<Omit<CreateUserDto, 'password'>> {}

// 更改密碼的請求數據接口
export interface ChangePasswordDto {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 重置密碼的請求數據接口
export interface ResetPasswordDto {
  email: string;
}

// 查詢用戶的過濾條件接口
export interface UserQueryDto {
  keyword?: string;
  role?: UserRole;
  status?: UserStatus;
  department?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 用戶列表響應接口
export interface UserListResponse {
  items: User[];
  total: number;
  page: number;
  pageSize: number;
}

// 用戶登入請求接口
export interface LoginDto {
  username: string;
  password: string;
  remember?: boolean;
}

// 登入響應接口
export interface LoginResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// 用戶權限接口
export interface UserPermission {
  id: number;
  userId: number;
  resource: string;      // 資源名稱
  action: string;        // 操作類型（create, read, update, delete）
  conditions?: any;      // 權限條件
  createdAt: string;
} 