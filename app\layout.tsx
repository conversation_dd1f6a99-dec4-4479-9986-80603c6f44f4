'use client';

import { Inter } from 'next/font/google';
import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import 'antd/dist/reset.css';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

// Stagewise toolbar configuration
const stagewiseConfig = {
	plugins: []
};

// Development-only Stagewise Toolbar component
function StagewiseDevToolbar() {
	useEffect(() => {
		if (process.env.NODE_ENV === 'development') {
			import('@stagewise/toolbar-next').then(({ StagewiseToolbar }) => {
				const toolbarElement = document.createElement('div');
				toolbarElement.id = 'stagewise-toolbar-root';
				document.body.appendChild(toolbarElement);
				
				const { createRoot } = require('react-dom/client');
				const { createElement } = require('react');
				const root = createRoot(toolbarElement);
				root.render(createElement(StagewiseToolbar, { config: stagewiseConfig }));
			}).catch(() => {
				// Silently fail if toolbar package is not available
			});
		}
	}, []);

	return null;
}

export default function RootLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const router = useRouter();
	const pathname = usePathname();

	useEffect(() => {
		// 檢查是否已登入
		const token = localStorage.getItem('accessToken');
		const isAuthPage = pathname === '/login';

		if (!token && !isAuthPage) {
			// 如果沒有 token 且不在登入頁面，則重定向到登入頁面
			router.push('/login');
		} else if (token && isAuthPage) {
			// 如果有 token 且在登入頁面，則重定向到首頁
			router.push('/customers');
		}
	}, [router, pathname]);

	return (
		<html lang="zh-TW">
			<body className={inter.className}>
				{children}
				<StagewiseDevToolbar />
			</body>
		</html>
	);
}
