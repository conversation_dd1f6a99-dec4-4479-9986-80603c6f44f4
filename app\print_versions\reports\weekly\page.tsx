'use client';

import React, { useState, useEffect } from 'react';
import { Spin } from 'antd';
// Import WeeklyReportContent and its ReportDataShape type from the new location
import WeeklyReportContent, { ReportDataShape } from '../../../(dashboard)/reports/weekly/WeeklyReportContent';

// Removed all helper functions (getUnitCellStyle, getParkingCellStyle, etc.)
// Removed all column definitions as they are now in WeeklyReportContent
// Removed service imports (getWeeklyReportData and individual types) as data comes from localStorage

export default function WeeklyReportPrintPage() {
  const [loading, setLoading] = useState(true);
  const [retrievedReportData, setRetrievedReportData] = useState<ReportDataShape | null>(null);
  const [printTriggered, setPrintTriggered] = useState(false);

  useEffect(() => {
    setLoading(true);
    const dataString = localStorage.getItem('weeklyReportDataForPrint');
    if (dataString) {
      try {
        const data = JSON.parse(dataString) as ReportDataShape;
        setRetrievedReportData(data);
      } catch (error) {
        console.error("Failed to parse report data from localStorage:", error);
        setRetrievedReportData(null); // Ensure data is null if parsing fails
      }
    } else {
      console.warn('No report data found in localStorage.');
    }
    setLoading(false); 
  }, []);

  useEffect(() => {
    // Trigger print only once after data is loaded and not already triggered
    if (!loading && retrievedReportData && !printTriggered) {
      const printAttempt = () => {
        // Check if all content, especially ECharts, might be ready
        // This timeout is a pragmatic approach. For more robust solution, one might need to check for specific elements rendering.
        setTimeout(() => {
          window.print();
          setPrintTriggered(true); // Mark as triggered to prevent re-triggering
          // Optionally, remove the item from localStorage after printing to clean up
          // localStorage.removeItem('weeklyReportDataForPrint');
        }, 1000); // Increased timeout to allow charts to render
      };

      // A simple check for EChart instances, might need refinement
      const charts = document.querySelectorAll('[data-echarts-instance]');
      if (charts.length > 0) { // Check if ECharts are present
        let allChartsRendered = true;
        // This is a very basic check. A more robust solution might involve listening to chart 'rendered' events if available.
        charts.forEach(chart => {
          if (chart.innerHTML === '') { // Or some other check to see if it's truly rendered
            allChartsRendered = false;
          }
        });

        if (allChartsRendered) {
          printAttempt();
        } else {
          // Fallback if charts are not immediately ready, or retry logic could be added
          console.log('Waiting for charts to render...');
          // Re-check after a delay, or rely on the main timeout
          setTimeout(printAttempt, 1500); // Additional delay for charts
        }
      } else {
        printAttempt(); // No ECharts, print directly
      }
    }
  }, [loading, retrievedReportData, printTriggered]);

  if (loading) {
    return (
      <Spin size="large" tip="正在準備列印內容..." fullscreen />
    );
  }

  if (!retrievedReportData) {
    return <div className="p-4 text-center text-red-500 print:hidden">無法從 localStorage 載入報表資料進行列印。請嘗試從主報表頁重新操作。</div>;
  }

  // Static report title and date for the print page
  // If these need to be dynamic, they should come from retrievedReportData
  const reportTitle = "第17週 週報表"; // Example: or retrievedReportData.reportTitle if available
  const reportDateRange = "2024/07/22 - 2024/07/28"; // Example: or retrievedReportData.dateRange if available

  return (
    <>
      <style jsx global>{`
        @media print {
          @page {
            size: A3;
            margin: 15mm; /* Adjusted margin for A3 */
          }
          body {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            background-color: #fff !important;
          }
          /* Hide elements not for printing on this page */
          .no-print-on-print-page {
            display: none !important;
          }
          /* Ensure layout is optimized for printing */
          .ant-layout-content,
          .report-content-wrapper,
          .weekly-report-content-wrapper, /* Class from WeeklyReportContent */
          main {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            max-width: 100% !important;
            box-shadow: none !important;
            background-color: #fff !important;
            overflow: visible !important;
          }
          html, body {
             overflow: visible !important;
             height: auto !important;
             background: #fff !important;
          }
          /* Table print styles from WeeklyReportContent will apply if they are global enough */
          /* Or specific print styles for tables and charts can be reinforced here if needed */
          .ant-table table {
            font-size: 9pt !important; /* Slightly smaller for A3 if lots of data */
          }
          .echarts-for-react,
          [data-echarts-instance] {
            page-break-inside: avoid !important;
          }
           .weekly-report-container section {
             page-break-inside: avoid !important; 
             margin-bottom: 10px !important; 
          }
        }
      `}</style>
      {/* Report Header for Print View */}
      <div className="text-center mb-6 pt-5 px-4">
        <h1 className="text-2xl font-bold text-black">{reportTitle}</h1>
        <p className="text-sm text-gray-700 text-black">{reportDateRange}</p>
      </div>

      {/* Render the WeeklyReportContent component with data from localStorage */}
      {/* Passing showEchartDownload={false} to hide download buttons on print page */}
      <div className="px-4 weekly-report-container"> {/* Added weekly-report-container for consistent print styles */}
        <WeeklyReportContent reportData={retrievedReportData} showEchartDownload={false} />
      </div>
    </>
  );
} 
