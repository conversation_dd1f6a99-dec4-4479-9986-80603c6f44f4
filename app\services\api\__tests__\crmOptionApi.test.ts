import { crmOptionApi } from '../crmOptionApi';
import { CreateCrmOptionDto, UpdateCrmOptionDto, GetCrmOptionDropdownParams } from '../../../interfaces/dto/crm-option.dto';

// Mock the BaseApi
jest.mock('../baseApi');

describe('CrmOptionApi', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getCrmOptions', () => {
    it('should call POST /GetCrmOptionList with correct parameters', async () => {
      const mockResponse = {
        TotalCount: 10,
        PageIndex: 1,
        PageSize: 10,
        TotalPages: 1,
        Details: []
      };

      const postSpy = jest.spyOn(crmOptionApi as any, 'post').mockResolvedValue(mockResponse);

      const query = {
        UsingPaging: true,
        PageIndex: 1,
        NumberOfPperPage: 10,
        SiteCode: 'A001',
        CrmOptionTypeId: 1
      };

      const result = await crmOptionApi.getCrmOptions(query);

      expect(postSpy).toHaveBeenCalledWith('/GetCrmOptionList', {
        SiteCode: 'A001',
        CrmOptionTypeId: 1,
        OptionValue: undefined,
        IsActive: undefined,
        PageIndex: 1,
        PageSize: 10
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createCrmOption', () => {
    it('should call POST /CreateCrmOption with correct data', async () => {
      const mockResponse = { SiteCrmOptionId: 123 };
      const postSpy = jest.spyOn(crmOptionApi as any, 'post').mockResolvedValue(mockResponse);

      const createData: CreateCrmOptionDto = {
        SiteCode: 'A001',
        CrmOptionTypeId: 1,
        OptionValue: '20-30坪',
        SortOrder: 1,
        IsActive: true
      };

      const result = await crmOptionApi.createCrmOption(createData);

      expect(postSpy).toHaveBeenCalledWith('/CreateCrmOption', createData);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateCrmOption', () => {
    it('should call PUT /UpdateCrmOption/{id} with correct data', async () => {
      const putSpy = jest.spyOn(crmOptionApi as any, 'put').mockResolvedValue(undefined);

      const updateData: UpdateCrmOptionDto = {
        OptionValue: '25-35坪',
        SortOrder: 2,
        IsActive: true
      };

      await crmOptionApi.updateCrmOption(123, updateData);

      expect(putSpy).toHaveBeenCalledWith('/UpdateCrmOption/123', updateData);
    });
  });

  describe('deleteCrmOption', () => {
    it('should call DELETE /DeleteCrmOption/{id}', async () => {
      const deleteSpy = jest.spyOn(crmOptionApi as any, 'delete').mockResolvedValue(undefined);

      await crmOptionApi.deleteCrmOption(123);

      expect(deleteSpy).toHaveBeenCalledWith('/DeleteCrmOption/123');
    });
  });

  describe('getCrmOptionDropdown', () => {
    it('should call POST /GetCrmOptionDropdown with correct parameters', async () => {
      const mockResponse = [
        { Name: '20-30坪', Value: 1, SortOrder: 1 },
        { Name: '30-40坪', Value: 2, SortOrder: 2 }
      ];

      const postSpy = jest.spyOn(crmOptionApi as any, 'post').mockResolvedValue(mockResponse);

      const params: GetCrmOptionDropdownParams = {
        SiteCode: 'A001',
        CrmOptionTypeId: 1,
        OnlyActive: true
      };

      const result = await crmOptionApi.getCrmOptionDropdown(params);

      expect(postSpy).toHaveBeenCalledWith('/GetCrmOptionDropdown', params);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getCrmOptionTypeDropdown', () => {
    it('should call GET /GetCrmOptionTypeDropdown', async () => {
      const mockResponse = [
        { Name: '需求坪數', Value: 1, Description: '客戶需求的房屋坪數範圍' },
        { Name: '需求格局', Value: 2, Description: '客戶需求的房屋格局類型' }
      ];

      const getSpy = jest.spyOn(crmOptionApi as any, 'get').mockResolvedValue(mockResponse);

      const result = await crmOptionApi.getCrmOptionTypeDropdown();

      expect(getSpy).toHaveBeenCalledWith('/GetCrmOptionTypeDropdown');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getCrmOptionById', () => {
    it('should call GET /GetCrmOptionById/{id}', async () => {
      const mockResponse = {
        SiteCrmOptionId: 123,
        SiteCode: 'A001',
        SiteName: '信義帝寶',
        CrmOptionTypeId: 1,
        CrmOptionTypeName: '需求坪數',
        OptionValue: '20-30坪',
        SortOrder: 1,
        IsActive: true,
        CreateTime: '2024-01-01T10:00:00',
        UpdateTime: '2024-01-01T10:00:00',
        CreatedUserInfoId: 'user001',
        CreatedUserName: '張三',
        UpdatedUserInfoId: 'user001',
        UpdatedUserName: '張三'
      };

      const getSpy = jest.spyOn(crmOptionApi as any, 'get').mockResolvedValue(mockResponse);

      const result = await crmOptionApi.getCrmOptionById(123);

      expect(getSpy).toHaveBeenCalledWith('/GetCrmOptionById/123');
      expect(result).toEqual(mockResponse);
    });
  });
});
