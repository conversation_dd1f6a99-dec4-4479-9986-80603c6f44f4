import { BaseApi } from './baseApi';
import { LoginRequest, LoginResponse } from '../../interfaces/dto/auth.dto';

class AuthApi extends BaseApi {
  constructor() {
    super('');  // 因為是根路徑的API，所以這裡不需要前綴
  }

  // 取得驗證碼
  async getCaptcha(): Promise<Blob> {
    return this.get<Blob>('/Authorization', {
      responseType: 'blob',
      withCredentials: true
    });
  }

  // 登入
  async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await this.post<LoginResponse>('/UserInfo/UserLogin', data, {
      withCredentials: true
    });
    
    // 保存Token和用戶信息
    if (response.isSuccess && response.body) {
      // 檢查token是否已經包含"Bearer"前綴
      const token = response.body.JWTToken;
      localStorage.setItem('accessToken', token);
      localStorage.setItem('user', JSON.stringify(response.body));
    }
    
    return response;
  }

  // 登出
  async logout(): Promise<void> {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('user');
  }
}

// 導出單例實例
export const authApi = new AuthApi(); 