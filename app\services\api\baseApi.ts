import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

// Helper function to join URL parts safely
const joinUrlParts = (...parts: string[]): string => {
  const cleanedParts = parts.map((part, index) => {
    let cleaned = part;
    if (index > 0) { // Remove leading slash from second part onwards
      cleaned = cleaned.startsWith('/') ? cleaned.substring(1) : cleaned;
    }
    if (index < parts.length - 1) { // Remove trailing slash from parts before the last one
       cleaned = cleaned.endsWith('/') ? cleaned.slice(0, -1) : cleaned;
    }
    return cleaned;
  });
  return cleanedParts.filter(part => part).join('/'); // Join with single slash
};

export class BaseApi {
  protected api: AxiosInstance;
  protected apiPrefix: string; // Store the cleaned prefix
  protected baseApiUrl: string = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api'; // Configurable base URL

  constructor(prefix: string = '') {
    // Clean and store the prefix (remove leading/trailing slashes)
    this.apiPrefix = prefix.replace(/^\/+|\/+$/g, '');

    this.api = axios.create({
      baseURL: this.baseApiUrl, // Use the fixed base URL
      withCredentials: true
    });

    // 請求攔截器：添加認證Token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('accessToken');
        if (token) {
          // 檢查token是否已經包含"Bearer"前綴
          const tokenValue = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
          config.headers.Authorization = tokenValue;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 響應攔截器：處理401錯誤
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token過期或無效，重定向到登入頁面
          localStorage.removeItem('accessToken');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Helper to build the full URL
  protected buildUrl(url: string): string {
     // Ensure relative URL starts with /
     const relativeUrl = url.startsWith('/') ? url : `/${url}`;
     // Join prefix and relative url
     const fullPath = this.apiPrefix ? `/${this.apiPrefix}${relativeUrl}` : relativeUrl;
     return fullPath;
    // Alternative using joinUrlParts (might be overkill if base is fixed)
    // return joinUrlParts(this.apiPrefix, url.startsWith('/') ? url : `/${url}`); 
  }

  protected get<T>(url: string, config?: AxiosRequestConfig) {
    return this.api.get<T>(this.buildUrl(url), config).then(response => response.data);
  }

  protected post<T>(url: string, data?: any, config?: AxiosRequestConfig) {
    return this.api.post<T>(this.buildUrl(url), data, config).then(response => response.data);
  }

  protected put<T>(url: string, data?: any, config?: AxiosRequestConfig) {
    return this.api.put<T>(this.buildUrl(url), data, config).then(response => response.data);
  }

  protected delete<T>(url: string, config?: AxiosRequestConfig) {
    return this.api.delete<T>(this.buildUrl(url), config).then(response => response.data);
  }

  // 使用特定前綴調用API
  protected callWithPrefix<T>(method: 'get' | 'post' | 'put' | 'delete', prefix: string, url: string, data?: any, config?: AxiosRequestConfig) {
    const instance = axios.create({
      baseURL: `${this.baseApiUrl}${prefix}`,
      withCredentials: true
    });
    
    // 複製請求攔截器
    instance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('accessToken');
        if (token) {
          const tokenValue = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
          config.headers.Authorization = tokenValue;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 複製響應攔截器
    instance.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('accessToken');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );

    if (method === 'get') {
      return instance.get<T>(url, config).then(response => response.data);
    } else if (method === 'post') {
      return instance.post<T>(url, data, config).then(response => response.data);
    } else if (method === 'put') {
      return instance.put<T>(url, data, config).then(response => response.data);
    } else if (method === 'delete') {
      return instance.delete<T>(url, config).then(response => response.data);
    }
  }
} 