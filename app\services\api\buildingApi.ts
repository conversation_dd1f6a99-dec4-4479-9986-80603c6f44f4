import { BaseApi } from './baseApi';
import type {
  BuildingListItem,
  BuildingListResponse,
  BuildingDetail,
  CreateBuildingDto,
  UpdateBuildingDto,
  BuildingQueryDto
} from '../../interfaces/dto/building.dto';
import type { ApiResponse, DropdownItem } from '@/app/interfaces/dto/common.dto';

class BuildingApi extends BaseApi {
  constructor() {
    super('Buildings');
  }

  // 取得建築物列表（分頁、排序、篩選）
  async getBuildings(query: BuildingQueryDto): Promise<ApiResponse<BuildingListResponse>> {
    return this.post<ApiResponse<BuildingListResponse>>('/GetBuildings', query);
  }

  // 根據ID取得建築物詳細資訊
  async getBuilding(id: number): Promise<BuildingDetail> {
    return this.get<BuildingDetail>(`/GetBuilding/${id}`);
  }

  // 新增建築物
  async createBuilding(data: CreateBuildingDto): Promise<any> {
    return this.post<any>('/CreateBuilding', data);
  }

  // 更新建築物
  async updateBuilding(id: number, data: UpdateBuildingDto): Promise<any> {
    return this.put<any>(`/UpdateBuilding/${id}`, data);
  }

  // 刪除建築物
  async deleteBuilding(id: number): Promise<any> {
    return this.delete<any>(`/DeleteBuilding/${id}`);
  }

  // 新增：取得通用下拉選單 (建築/樓層等，依參數決定)
  async getCommonDropdownList(params: { type: 'site' | 'building' | 'floor'; siteCode?: string; buildingId?: number }): Promise<ApiResponse<DropdownItem[]>> {
    return this.post<ApiResponse<DropdownItem[]>>('GetCommonDropdownList', params);
  }

}

export const buildingApi = new BuildingApi(); 