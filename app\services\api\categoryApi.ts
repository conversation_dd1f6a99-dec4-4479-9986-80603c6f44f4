import { BaseApi } from './baseApi';
import {
  // 大分類
  LargeCategoryListResponse,
  LargeCategoryDetail,
  CreateLargeCategoryDto,
  UpdateLargeCategoryDto,
  LargeCategoryQueryDto,
  CreateCategoryResponse,
  // 中分類
  MediumCategoryListResponse,
  MediumCategoryDetail,
  CreateMediumCategoryDto,
  UpdateMediumCategoryDto,
  MediumCategoryQueryDto,
  // 小分類
  SmallCategoryListResponse,
  SmallCategoryDetail,
  CreateSmallCategoryDto,
  UpdateSmallCategoryDto,
  SmallCategoryQueryDto,
} from '../../interfaces/dto/category.dto';

class CategoryApi extends BaseApi {
  constructor() {
    super(''); // 根據 API 文檔，直接使用根路徑
  }

  // ==================== 大分類 API ====================
  
  /**
   * 查詢大分類列表 (分頁)
   * POST /api/LargeCategories/GetLargeCategories
   */
  async getLargeCategories(query: LargeCategoryQueryDto): Promise<LargeCategoryListResponse> {
    // 直接使用 LargeCategoryQueryDto 格式，它已經是正確的 API 格式
    return this.post<LargeCategoryListResponse>('/LargeCategories/GetLargeCategories', query);
  }

  /**
   * 取得大分類詳細資料
   * GET /api/LargeCategories/GetLargeCategory/{largeCategoryId}
   */
  async getLargeCategoryById(id: number): Promise<LargeCategoryDetail> {
    const response = await this.get<LargeCategoryDetail>(`/LargeCategories/GetLargeCategory/${id}`);

    // 檢查回應結構 - API 回應可能包裝在 body 屬性中
    const actualData = (response as any).body || response;
    return actualData;
  }

  /**
   * 新增大分類
   * POST /api/LargeCategories/CreateLargeCategory
   */
  async createLargeCategory(data: CreateLargeCategoryDto): Promise<CreateCategoryResponse> {
    return this.post<CreateCategoryResponse>('/LargeCategories/CreateLargeCategory', data);
  }

  /**
   * 更新大分類
   * POST /api/LargeCategories/UpdateLargeCategory/{largeCategoryId}
   */
  async updateLargeCategory(id: number, data: UpdateLargeCategoryDto): Promise<void> {
    return this.post<void>(`/LargeCategories/UpdateLargeCategory/${id}`, data);
  }

  /**
   * 刪除大分類
   * DELETE /api/LargeCategories/DeleteLargeCategory/{largeCategoryId}
   */
  async deleteLargeCategory(id: number): Promise<void> {
    return this.delete<void>(`/LargeCategories/DeleteLargeCategory/${id}`);
  }

  // ==================== 中分類 API ====================
  
  /**
   * 查詢中分類列表 (分頁)
   * POST /api/MediumCategories/GetMediumCategories
   */
  async getMediumCategories(query: MediumCategoryQueryDto): Promise<MediumCategoryListResponse> {
    // 直接使用 MediumCategoryQueryDto 格式，它已經是正確的 API 格式
    return this.post<MediumCategoryListResponse>('/MediumCategories/GetMediumCategories', query);
  }

  /**
   * 取得中分類詳細資料
   * GET /api/MediumCategories/GetMediumCategory/{mediumCategoryId}
   */
  async getMediumCategoryById(id: number): Promise<MediumCategoryDetail> {
    const response = await this.get<MediumCategoryDetail>(`/MediumCategories/GetMediumCategory/${id}`);

    // 檢查回應結構 - API 回應可能包裝在 body 屬性中
    const actualData = (response as any).body || response;
    return actualData;
  }

  /**
   * 新增中分類
   * POST /api/MediumCategories/CreateMediumCategory
   */
  async createMediumCategory(data: CreateMediumCategoryDto): Promise<CreateCategoryResponse> {
    return this.post<CreateCategoryResponse>('/MediumCategories/CreateMediumCategory', data);
  }

  /**
   * 更新中分類
   * POST /api/MediumCategories/UpdateMediumCategory/{mediumCategoryId}
   */
  async updateMediumCategory(id: number, data: UpdateMediumCategoryDto): Promise<void> {
    return this.post<void>(`/MediumCategories/UpdateMediumCategory/${id}`, data);
  }

  /**
   * 刪除中分類
   * DELETE /api/MediumCategories/DeleteMediumCategory/{mediumCategoryId}
   */
  async deleteMediumCategory(id: number): Promise<void> {
    return this.delete<void>(`/MediumCategories/DeleteMediumCategory/${id}`);
  }

  // ==================== 小分類 API ====================
  
  /**
   * 查詢小分類列表 (分頁)
   * POST /api/SmallCategories/GetSmallCategories
   */
  async getSmallCategories(query: SmallCategoryQueryDto): Promise<SmallCategoryListResponse> {
    // 直接使用 SmallCategoryQueryDto 格式，它已經是正確的 API 格式
    return this.post<SmallCategoryListResponse>('/SmallCategories/GetSmallCategories', query);
  }

  /**
   * 取得小分類詳細資料
   * GET /api/SmallCategories/GetSmallCategory/{smallCategoryId}
   */
  async getSmallCategoryById(id: number): Promise<SmallCategoryDetail> {
    const response = await this.get<SmallCategoryDetail>(`/SmallCategories/GetSmallCategory/${id}`);

    // 檢查回應結構 - API 回應可能包裝在 body 屬性中
    const actualData = (response as any).body || response;
    return actualData;
  }

  /**
   * 新增小分類
   * POST /api/SmallCategories/CreateSmallCategory
   */
  async createSmallCategory(data: CreateSmallCategoryDto): Promise<CreateCategoryResponse> {
    return this.post<CreateCategoryResponse>('/SmallCategories/CreateSmallCategory', data);
  }

  /**
   * 更新小分類
   * POST /api/SmallCategories/UpdateSmallCategory/{smallCategoryId}
   */
  async updateSmallCategory(id: number, data: UpdateSmallCategoryDto): Promise<void> {
    return this.post<void>(`/SmallCategories/UpdateSmallCategory/${id}`, data);
  }

  /**
   * 刪除小分類
   * DELETE /api/SmallCategories/DeleteSmallCategory/{smallCategoryId}
   */
  async deleteSmallCategory(id: number): Promise<void> {
    return this.delete<void>(`/SmallCategories/DeleteSmallCategory/${id}`);
  }

  // ==================== 輔助方法 ====================
  
  /**
   * 取得大分類下拉選單
   * GET /api/LargeCategories/GetLargeCategoryDropdown
   */
  async getLargeCategoryDropdown(): Promise<{ Value: number; Name: string }[]> {
    const response = await this.get<{ LargeCategoryId: number; Name: string; SortOrder: number }[]>('/LargeCategories/GetLargeCategoryDropdown');
    
    // 檢查回應結構 - API 回應可能包裝在 body 屬性中
    const actualData = (response as any).body || response;
    const dataArray = Array.isArray(actualData) ? actualData : [];
    
    return dataArray.map((item: any) => ({
      Value: item.LargeCategoryId,
      Name: item.Name
    }));
  }

  /**
   * 取得中分類下拉選單
   * GET /api/MediumCategories/GetMediumCategoryDropdown
   */
  async getMediumCategoryDropdown(largeCategoryId?: number): Promise<{ Value: number; Name: string }[]> {
    const url = largeCategoryId 
      ? `/MediumCategories/GetMediumCategoryDropdown?largeCategoryId=${largeCategoryId}`
      : '/MediumCategories/GetMediumCategoryDropdown';
    
    const response = await this.get<{ MediumCategoryId: number; LargeCategoryId: number; LargeCategoryName: string; Name: string; SortOrder: number }[]>(url);
    
    // 檢查回應結構 - API 回應可能包裝在 body 屬性中
    const actualData = (response as any).body || response;
    const dataArray = Array.isArray(actualData) ? actualData : [];
    
    return dataArray.map((item: any) => ({
      Value: item.MediumCategoryId,
      Name: item.Name
    }));
  }

  /**
   * 取得小分類下拉選單
   * GET /api/SmallCategories/GetSmallCategoryDropdown
   */
  async getSmallCategoryDropdown(largeCategoryId?: number, mediumCategoryId?: number): Promise<{ Value: number; Name: string }[]> {
    const params = new URLSearchParams();
    if (largeCategoryId) params.append('largeCategoryId', largeCategoryId.toString());
    if (mediumCategoryId) params.append('mediumCategoryId', mediumCategoryId.toString());
    
    const url = params.toString() 
      ? `/SmallCategories/GetSmallCategoryDropdown?${params.toString()}`
      : '/SmallCategories/GetSmallCategoryDropdown';
    
    const response = await this.get<{ SmallCategoryId: number; MediumCategoryId: number; MediumCategoryName: string; LargeCategoryId: number; LargeCategoryName: string; Name: string; SortOrder: number }[]>(url);
    
    // 檢查回應結構 - API 回應可能包裝在 body 屬性中
    const actualData = (response as any).body || response;
    const dataArray = Array.isArray(actualData) ? actualData : [];
    
    return dataArray.map((item: any) => ({
      Value: item.SmallCategoryId,
      Name: item.Name
    }));
  }
}

export const categoryApi = new CategoryApi();