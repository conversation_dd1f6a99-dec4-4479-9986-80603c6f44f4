import { BaseApi } from './baseApi';
import type { BaseQueryParams, DropdownItem, ApiResponse } from '../../interfaces/dto/common.dto';
import { 
  Customer, 
  CreateCustomerDto, 
  CustomerListResponse, 
  CustomerFollowUp, 
  GetCustomerResponse, 
  UpdateCustomerDto
} from '../../interfaces/dto/customer.dto';

/**
 * 客戶API服務
 */
class CustomerApi extends BaseApi {
  constructor() {
    super('Customers');
  }

  /**
   * 取得客戶下拉選單列表
   */
  async getCustomerDropdownList(): Promise<DropdownItem[]> {
    return this.get<DropdownItem[]>('/GetCustomerDropdown');
  }

  /**
   * 取得客戶列表（分頁）
   * @param params BaseQueryParams (包含分頁、排序、過濾資訊)
   */
  async getCustomerList(params: BaseQueryParams): Promise<CustomerListResponse> {
    // 直接將傳入的 BaseQueryParams 作為請求體
    return this.post<CustomerListResponse>('/GetCustomers', params);
  }

  /**
   * 根據ID取得客戶詳情
   * @param id 客戶ID (注意：後端是字串還是數字？DTO 中是 string)
   */
  async getCustomerById(id: string | number): Promise<Customer> {
    // 修改路徑為 GetCustomer
    const response = await this.get<GetCustomerResponse>(`/GetCustomer/${id}`);
    if (!response.isSuccess || !response.body) {
      throw new Error(response.message || 'Failed to fetch customer detail');
    }
    return response.body; // 直接返回 body 中的客戶數據
  }

  /**
   * 創建新客戶
   * @param customer 客戶資料 (CreateCustomerDto)
   */
  async createCustomer(customer: CreateCustomerDto): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>('/CreateCustomer', customer);
  }

  /**
   * 更新客戶資料
   * @param id 客戶ID
   * @param customer 客戶資料 (UpdateCustomerDto)
   */
  async updateCustomer(id: string | number, customer: UpdateCustomerDto): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>(`/UpdateCustomer/${id}`, customer);
  }

  /**
   * 刪除客戶
   * @param id 客戶ID
   */
  async deleteCustomer(id: number): Promise<boolean> {
    return this.delete<boolean>(`/DeleteCustomer/${id}`);
  }

  /**
   * 新增客戶跟進記錄
   * @param customerId 客戶ID
   * @param followUp 跟進記錄資料
   */
  async addCustomerFollowUp(customerId: number, followUp: Omit<CustomerFollowUp, 'id' | 'customerId' | 'createdAt' | 'createdByName'>): Promise<CustomerFollowUp> {
    return this.post<CustomerFollowUp>(`/${customerId}/AddFollowUp`, followUp);
  }

  /**
   * 取得客戶跟進記錄
   * @param customerId 客戶ID
   */
  async getCustomerFollowUps(customerId: number): Promise<CustomerFollowUp[]> {
    return this.get<CustomerFollowUp[]>(`/${customerId}/GetFollowUps`);
  }
}

// 導出單例實例
export const customerApi = new CustomerApi(); 