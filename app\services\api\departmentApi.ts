import { BaseApi } from './baseApi';
// 確保 common.dto.ts 中有 ApiResponse 和 SortOrderInfo 的定義
// 如果沒有，需要從其他 api 檔案複製或定義
import type { BaseQueryParams, DropdownItem, SortOrderInfo, ApiResponse } from '../../interfaces/dto/common.dto';

// --- Department API DTOs and Interfaces ---

// 部門列表項目 (對應 GetDepartmentList 回應的 Detail)
export interface DepartmentListItem {
  CompanyId: string;
  CompanyName?: string;
  DepartmentId: string;
  Name: string;
  CreatedTime: string; // ISO Date String
  CreatedUserId: string;
  CreatedUserName: string;
  UpdatedTime: string; // ISO Date String
  UpdatedUserId: string;
  UpdatedUserName: string;
}

// 取得部門列表的請求參數 (擴展 BaseQueryParams)
export interface DepartmentListParams extends BaseQueryParams {
  CompanyId?: string; // 可選，用於篩選特定公司下的部門
  Name?: string;      // 可選，用於名稱搜尋 (如果後端支援)
}

// 取得部門列表的完整回應結構
export interface DepartmentListResponse {
  UsingPaging?: boolean;
  NumberOfPperPage?: number;
  PageIndex?: number;
  SortOrderInfos?: SortOrderInfo[];
  SearchTermInfos?: { SearchField: string; SearchValue: string }[]; // 假設後端也用這個結構
  Detail: DepartmentListItem[];
  TotalPages?: number;
  RecordCount?: number;
}

// 建立部門 DTO
export interface CreateDepartmentDto {
  CompanyId: string;
  DepartmentId: string;
  Name: string;
}

// 更新部門 DTO (根據後端錯誤，body 也需要 ID)
export interface UpdateDepartmentDto {
  CompanyId: string;
  DepartmentId: string;
  Name: string; 
}

// --- End Department API DTOs and Interfaces ---

class DepartmentApi extends BaseApi {
  constructor() {
    super('/Department'); // API 的基礎路徑
  }

  // 獲取部門列表 (POST /api/Department/GetDepartmentList/GetList)
  async getDepartmentList(params: DepartmentListParams): Promise<ApiResponse<DepartmentListResponse>> {
    return this.post<ApiResponse<DepartmentListResponse>>('/GetDepartmentList/GetList', params);
  }

  // 根據公司ID和部門ID獲取部門 (GET /api/Department/GetDepartmentById/{companyId}/{departmentId})
  async getDepartmentById(companyId: string, departmentId: string): Promise<ApiResponse<DepartmentListItem>> {
    return this.get<ApiResponse<DepartmentListItem>>(`/GetDepartmentById/${companyId}/${departmentId}`);
  }

  // 建立部門 (POST /api/Department/CreateDepartment/Create)
  async createDepartment(data: CreateDepartmentDto): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>('/CreateDepartment/Create', data);
  }

  // 更新部門 (PUT /api/Department/UpdateDepartment/{companyId}/{departmentId})
  async updateDepartment(companyId: string, departmentId: string, data: UpdateDepartmentDto): Promise<ApiResponse<any>> {
    return this.put<ApiResponse<any>>(`/UpdateDepartment/${companyId}/${departmentId}`, data);
  }

  // 刪除部門 (DELETE /api/Department/DeleteDepartment/{companyId}/{departmentId})
  async deleteDepartment(companyId: string, departmentId: string): Promise<ApiResponse<any>> {
    return this.delete<ApiResponse<any>>(`/DeleteDepartment/${companyId}/${departmentId}`);
  }

  // 備註：部門下拉選單功能可能已存在於 userInfoApi 或需要另外實作
}

// 導出單例實例
export const departmentApi = new DepartmentApi();