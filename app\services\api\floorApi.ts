import { BaseApi } from './baseApi';
import type { ApiResponse, DropdownItem } from '../../interfaces/dto/common.dto';
import type {
  FloorListItem,
  FloorListResponse,
  FloorDetail,
  CreateFloorDto,
  UpdateFloorDto,
  FloorQueryDto
} from '../../interfaces/dto/floor.dto';

class FloorApi extends BaseApi {
  constructor() {
    super('Floors');
  }

  // 取得樓層列表（分頁、排序、篩選）
  async getFloors(query: FloorQueryDto): Promise<ApiResponse<FloorListResponse>> {
    return this.post<ApiResponse<FloorListResponse>>('/GetFloors', query);
  }

  // 根據ID取得樓層詳細資訊
  async getFloor(id: number): Promise<ApiResponse<FloorDetail>> {
    return this.get<ApiResponse<FloorDetail>>(`/GetFloor/${id}`);
  }

  // 新增樓層
  async createFloor(data: CreateFloorDto): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>('/CreateFloor', data);
  }

  // 更新樓層
  async updateFloor(id: number, data: UpdateFloorDto): Promise<ApiResponse<any>> {
    return this.put<ApiResponse<any>>(`/UpdateFloor/${id}`, data);
  }

  // 刪除樓層
  async deleteFloor(id: number): Promise<ApiResponse<any>> {
    return this.delete<ApiResponse<any>>(`/DeleteFloor/${id}`);
  }

  // 根據建築ID取得樓層下拉選單
  async getFloorDropdownList(buildingId: number): Promise<ApiResponse<DropdownItem[]>> {
    return this.get<ApiResponse<DropdownItem[]>>('Dropdown', { params: { buildingId } });
  }
}

export const floorApi = new FloorApi(); 