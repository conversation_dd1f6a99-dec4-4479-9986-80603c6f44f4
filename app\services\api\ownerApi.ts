import { BaseApi } from './baseApi';
import type { ApiResponse, BaseQueryParams } from '../../interfaces/dto/common.dto';

// --- DTOs --- //

/** 建立業主 Request Body */
export interface CreateOwnerDto {
  CompanyName: string;
  ResponsiblePerson: string;
  CompanyPhone: string;
  CompanyAddress: string;
  MailingAddress: string;
  ContactPerson: string;
  Email: string;
  PersonType: string; // 建議後續定義更明確的類型或枚舉
  IdentificationNumber: string;
  ContactPhone1: string;
  ContactPhone2?: string; // 假設 ContactPhone2 是選填
}

/** 更新業主 Request Body (假設結構類似 Create，需包含 OwnerId) */
export interface UpdateOwnerDto extends CreateOwnerDto {
  OwnerId: number;
}

/** 取得業主列表 Response Item */
export interface OwnerListItemDto {
  OwnerId: number;
  CompanyName: string;
  ResponsiblePerson: string;
  ContactPerson: string;
  ContactPhone1: string;
  PersonType: string;
  IdentificationNumber: string;
}

// 新增 OwnerListResponse 介面
export interface OwnerListResponse {
  Detail: OwnerListItemDto[];
  TotalPages: number;
  RecordCount: number;
  NumberOfPperPage: number;
  PageIndex: number;
  UsingPaging: boolean;
  SortOrderInfos?: { SortField: string; SortOrder: string }[];
  SearchTermInfos?: { SearchField: string; SearchValue: string }[];
}

/** 取得業主列表 Request Params (繼承 BaseQueryParams 並加上特定篩選欄位) */
export interface GetOwnerListParamsDto extends BaseQueryParams {
  CompanyName?: string;
  ResponsiblePerson?: string;
  IdentificationNumber?: string;
  PersonType?: string;
}

/** 取得業主列表 Response */
export type GetOwnerListResponseDto = OwnerListResponse;

/** 取得單一業主詳細資料 Response (假設結構類似 Update) */
// 注意：API 文件未提供此結構，暫定同 UpdateOwnerDto，實際需依後端為準
export type GetOwnerResponseDto = ApiResponse<UpdateOwnerDto>;

// --- API Service --- //

class OwnerApi extends BaseApi {
  constructor() {
    super('/Owner'); // 設定 API 路徑前綴
  }

  /**
   * 建立新業主
   * POST /api/Owner/CreateOwner
   */
  public createOwner(data: CreateOwnerDto): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>('/CreateOwner', data);
  }

  /**
   * 更新業主資料
   * PUT /api/Owner/UpdateOwner/{ownerId}
   */
  public updateOwner(ownerId: number, data: Omit<UpdateOwnerDto, 'OwnerId'>): Promise<ApiResponse<any>> {
    return this.put<ApiResponse<any>>(`/UpdateOwner/${ownerId}`, data);
  }

  /**
   * 刪除業主資料
   * DELETE /api/Owner/DeleteOwner/{ownerId}
   */
  public deleteOwner(ownerId: number): Promise<ApiResponse<any>> {
    return this.delete<ApiResponse<any>>(`/DeleteOwner/${ownerId}`);
  }

  /**
   * 取得業主列表 (分頁)
   * POST /api/Owner/GetOwnerList/list
   */
  public getOwnerList(params: GetOwnerListParamsDto): Promise<ApiResponse<OwnerListResponse>> {
    return this.post<ApiResponse<OwnerListResponse>>('/GetOwnerList', params);
  }

  /**
   * 取得單一業主詳細資料
   * GET /api/Owner/GetOwner/{ownerId}
   */
  public getOwner(ownerId: number): Promise<ApiResponse<UpdateOwnerDto>> {
    return this.get<ApiResponse<UpdateOwnerDto>>(`/GetOwner/${ownerId}`);
  }

  // 若有下拉選單需求，可仿照其他 Api 加上 getOwnerDropdownList 等方法
}

export const ownerApi = new OwnerApi(); 