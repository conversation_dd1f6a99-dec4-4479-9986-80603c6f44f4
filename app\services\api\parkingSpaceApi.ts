import type { ApiResponse } from '@/app/interfaces/dto/common.dto';
import type { 
    ParkingSpaceQueryInput,
    ParkingSpaceListOutput,
    ParkingSpaceCreateInput,
    ParkingSpaceUpdateInput,
    ParkingSpaceOutput,
    PagedListOutput,
    ParkingSpaceCreateResult,
    OperationResult,
    ParkingSpaceListItemDto, 
    ParkingSpaceQueryDto, 
    CreateParkingSpaceDto, 
    UpdateParkingSpaceDto, 
    ParkingSpaceDetailDto,
    ParkingSpaceListPagedResult
} from '@/app/interfaces/dto/parkingSpace.dto';
import { BaseApi } from './baseApi';

class ParkingSpace<PERSON>pi extends BaseApi {
  constructor() {
    super('ParkingSpaces'); // API prefix: /api/ParkingSpaces
  }

  /**
   * 查詢車位列表 (新版本)
   * POST /api/ParkingSpaces/GetParkingSpaces
   */
  public getParkingSpaceList(params: ParkingSpaceQueryInput): Promise<ApiResponse<PagedListOutput<ParkingSpaceListOutput>>> {
    return this.post<ApiResponse<PagedListOutput<ParkingSpaceListOutput>>>('/GetParkingSpaces', params);
  }

  /**
   * 建立車位 (新版本)
   * POST /api/ParkingSpaces/CreateParkingSpace
   */
  public createParkingSpaceNew(data: ParkingSpaceCreateInput): Promise<ParkingSpaceCreateResult> {
    return this.post<ParkingSpaceCreateResult>('/CreateParkingSpace', data);
  }

  /**
   * 更新車位 (新版本)
   * PUT /api/ParkingSpaces/{parkingSpaceId}
   */
  public updateParkingSpaceNew(parkingSpaceId: number, data: ParkingSpaceUpdateInput): Promise<OperationResult> {
    return this.put<OperationResult>(`/${parkingSpaceId}`, data);
  }

  /**
   * 刪除車位 (新版本)
   * DELETE /api/ParkingSpaces/{parkingSpaceId}
   */
  public deleteParkingSpaceNew(parkingSpaceId: number): Promise<OperationResult> {
    return this.delete<OperationResult>(`/${parkingSpaceId}`);
  }

  /**
   * 查詢單一車位詳情 (新版本)
   * GET /api/ParkingSpaces/{parkingSpaceId}
   */
  public getParkingSpaceDetail(parkingSpaceId: number): Promise<ParkingSpaceOutput> {
    return this.get<ParkingSpaceOutput>(`/${parkingSpaceId}`);
  }

  /**
   * @deprecated 請使用 getParkingSpaceList
   */
  public getParkingSpaces(params: ParkingSpaceQueryDto): Promise<ApiResponse<ParkingSpaceListPagedResult>> {
    return this.post<ApiResponse<ParkingSpaceListPagedResult>>('GetParkingSpaces', params);
  }

  /**
   * @deprecated 請使用 getParkingSpaceDetail
   */
  public getParkingSpace(parkingSpaceId: number): Promise<ApiResponse<ParkingSpaceDetailDto>> {
    return this.get<ApiResponse<ParkingSpaceDetailDto>>(`GetParkingSpace/${parkingSpaceId}`);
  }

  /**
   * @deprecated 請使用 createParkingSpaceNew
   */
  public createParkingSpace(data: CreateParkingSpaceDto): Promise<ApiResponse<ParkingSpaceListItemDto>> {
    return this.post<ApiResponse<ParkingSpaceListItemDto>>('CreateParkingSpace', data);
  }

  /**
   * @deprecated 請使用 updateParkingSpaceNew
   */
  public updateParkingSpace(parkingSpaceId: number, data: UpdateParkingSpaceDto): Promise<ApiResponse<ParkingSpaceListItemDto>> {
    return this.put<ApiResponse<ParkingSpaceListItemDto>>(`UpdateParkingSpace/${parkingSpaceId}`, data);
  }

  /**
   * @deprecated 請使用 deleteParkingSpaceNew
   */
  public deleteParkingSpace(parkingSpaceId: number): Promise<ApiResponse<null>> {
    return this.delete<ApiResponse<null>>(`DeleteParkingSpace/${parkingSpaceId}`);
  }
}

export const parkingSpaceApi = new ParkingSpaceApi(); 