import { BaseApi } from './baseApi';
import {
  Project,
  CreateProjectDto,
  UpdateProjectDto,
  ProjectQueryDto,
  ProjectListResponse
} from '../../interfaces/dto/project.dto';

class ProjectApi extends BaseApi {
  constructor() {
    super('projects');
  }

  // 獲取案場列表（帶分頁和篩選）
  async getProjects(query: ProjectQueryDto): Promise<ProjectListResponse> {
    return this.get<ProjectListResponse>('/GetList', { params: query });
  }

  // 獲取所有案場（不分頁）
  async getAllProjects(): Promise<Project[]> {
    return this.get<Project[]>('/all');
  }

  // 獲取單個案場
  async getProject(id: number): Promise<Project> {
    return this.get<Project>(`/GetById/${id}`);
  }

  // 創建案場
  async createProject(data: CreateProjectDto): Promise<Project> {
    return this.post<Project>('/Create', data);
  }

  // 更新案場
  async updateProject(id: number, data: UpdateProjectDto): Promise<Project> {
    return this.put<Project>(`/Update/${id}`, data);
  }

  // 刪除案場
  async deleteProject(id: number): Promise<void> {
    return this.delete<void>(`/Delete/${id}`);
  }
}

// 導出單例實例
export const projectApi = new ProjectApi(); 