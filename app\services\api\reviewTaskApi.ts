import { BaseApi } from './baseApi';
import { 
  CreateReviewTaskDto, 
  ReviewTaskResponse, 
  ReviewTaskDetailResponse,
  ReviewHistoryResponse,
  ReviewNextStepResponse,
  ReviewTaskListResponse,
  AddReviewHistoryDto,
  ReviewTaskQueryParams,
  ReviewUserResponse,
  ReviewTaskStatus
} from '@/app/interfaces/dto/reviewTask.dto';

// 添加更新狀態的介面
export interface UpdateReviewTaskStatusDto {
  Status: ReviewTaskStatus;
}

class ReviewTaskApi extends BaseApi {
  constructor() {
    super('/ReviewTask');
  }

  // 建立審核流程
  async createReviewTask(data: CreateReviewTaskDto): Promise<ReviewTaskResponse> {
    return this.post<ReviewTaskResponse>('/CreateReviewTask', data);
  }

  // 取得審核流程清單
  async getReviewTasks(params: ReviewTaskQueryParams): Promise<ReviewTaskListResponse> {
    return this.post<ReviewTaskListResponse>('/GetReviewTasks', params);
  }

  // 取得審核流程詳細資料
  async getReviewTaskDetail(taskId: number): Promise<ReviewTaskDetailResponse> {
    return this.get<ReviewTaskDetailResponse>(`/GetReviewTaskDetail/${taskId}`);
  }

  // 更新審核流程
  async updateReviewTask(taskId: number, data: CreateReviewTaskDto): Promise<ReviewTaskResponse> {
    return this.put<ReviewTaskResponse>(`/UpdateReviewTask/${taskId}`, data);
  }

  // 刪除審核流程
  async deleteReviewTask(taskId: number): Promise<void> {
    return this.delete<void>(`/DeleteReviewTask/${taskId}`);
  }

  // 取得審核流程歷史記錄
  async getReviewHistory(taskId: number): Promise<ReviewHistoryResponse> {
    return this.get<ReviewHistoryResponse>(`/GetReviewHistory/${taskId}/history`);
  }

  // 添加審核歷史記錄
  async addReviewHistory(data: AddReviewHistoryDto): Promise<ReviewTaskResponse> {
    return this.post<ReviewTaskResponse>('/AddReviewHistory/history', data);
  }

  // 獲取下一步驟資訊
  async getNextStep(taskId: number): Promise<ReviewNextStepResponse> {
    return this.get<ReviewNextStepResponse>(`/GetNextStep/${taskId}/next-step`);
  }

  // 獲取審核人員清單
  async getReviewUsers(): Promise<ReviewUserResponse> {
    return this.get<ReviewUserResponse>('/GetReviewUsers/users');
  }

  // 更新審核流程狀態
  async updateReviewTaskStatus(taskId: number, status: ReviewTaskStatus): Promise<ReviewTaskResponse> {
    return this.put<ReviewTaskResponse>(`/UpdateReviewTask/${taskId}`, { Status: status });
  }
}

// 導出單例實例
export const reviewTaskApi = new ReviewTaskApi(); 