import { BaseApi } from './baseApi';
import type { BaseQueryParams, DropdownItem, ApiResponse } from '../../interfaces/dto/common.dto';

// 權限樹節點
export interface PermissionTreeNode {
  Name: string;
  Id: string;
  Selected: boolean;
  Children: PermissionTreeNode[];
}

// 角色組詳細資訊
export interface RoleGroupDetailItem {
  RoleGroupId: string;
  Name: string;
  SiteCode: string;
  SiteName: string;
  IsAdmin: boolean;
  CreatedUserId: string;
  CreatedUserName: string;
  CreatedTime: string;
  UpdatedUserId: string;
  UpdatedUserName: string;
  UpdatedTime: string;
  UserIds: string[];
  Permission: string;
  PermissionTree: PermissionTreeNode[];
}

// 角色組列表響應
export interface RoleGroupListResponse {
  Detail: RoleGroupDetailItem[];
  TotalPages: number;
  RecordCount: number;
  NumberOfPperPage: number;
  PageIndex: number;
  UsingPaging: boolean;
  SortOrderInfos: {
    SortField: string;
    SortOrder: string;
  }[];
  SearchTermInfos: {
    SearchField: string;
    SearchValue: string;
  }[];
}

// 建立角色組DTO
export interface CreateRoleGroupDto {
  Name: string;
  Permissions: string;
  SiteCode: string;
  UserIds: string[];
}

// 更新角色組權限DTO
export interface UpdateRoleGroupPermissionDto {
  Id: string;
  Name: string;
  Permissions: string;
  UserIds: string[];
}

// 更新角色組用戶DTO
export interface UpdateRoleGroupUserDto {
  id: string;
  userIds: string[];
}

// 角色選單樹結構
export interface MenuTree {
  id: string;
  name: string;
  children?: MenuTree[];
}

class RoleGroupApi extends BaseApi {
  constructor() {
    super('/RoleGroupControllerPg');
  }

  // 獲取角色組列表
  async getRoleGroupList(params: BaseQueryParams): Promise<ApiResponse<RoleGroupListResponse>> {
    return this.post<ApiResponse<RoleGroupListResponse>>('/GetRoleGroupList/GetList', params);
  }

  // 根據ID獲取角色組詳情
  async getRoleGroupById(id: string): Promise<ApiResponse<RoleGroupDetailItem>> {
    return this.get<ApiResponse<RoleGroupDetailItem>>(`/GetRoleGroupById/${id}`);
  }

  // 建立角色組
  async createRoleGroup(data: CreateRoleGroupDto): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>('/CreateRoleGroup/Create', data);
  }

  // 更新角色組權限
  async updateRoleGroupPermission(data: UpdateRoleGroupPermissionDto): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>('/UpdateRoleGroupPermission/UpdatePermission', data);
  }

  // 更新角色組用戶
  public updateRoleGroupUser(data: UpdateRoleGroupUserDto): Promise<any> {
    // 轉換駝峰式命名
    const requestData = {
      Id: data.id,
      UserIds: data.userIds
    };
    return this.post<any>('/UpdateRoleGroupUser/UpdateUser', requestData);
  }

  // 刪除角色組
  async deleteRoleGroup(id: string): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>('/DeleteRoleGroup/Delete', { Id: id });
  }

  // 獲取權限選單樹
  public getMenuTree(): Promise<ApiResponse<PermissionTreeNode[]>> {
    return this.get<ApiResponse<PermissionTreeNode[]>>('/GetMenuTree/MenuTree');
  }

  // 獲取角色下拉選單 (從 userInfoApi 移入)
  async getRoleGroupDropdownList(siteCode?: string): Promise<ApiResponse<DropdownItem>> { // 使用引入的 DropdownItem
    const data = siteCode ? { SiteCode: siteCode } : {};
    // 即使 baseApi 的前綴是 /RoleGroupControllerPg，我們仍需指定正確的 /RoleGroup 前綴
    return this.callWithPrefix<ApiResponse<DropdownItem>>('post', '/RoleGroup', '/GetRoleGroupDropdownList', data);
  }
}

// 導出單例實例
export const roleGroupApi = new RoleGroupApi(); 