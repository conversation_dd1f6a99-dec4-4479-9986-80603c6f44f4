import { BaseApi } from './baseApi';
import type { ApiResponse } from '../../interfaces/dto/common.dto';
import type {
  SalesControlData,
  GetSalesControlParams,
  UpdateUnitStatusDto,
  SalesStatistics
} from '../../interfaces/dto/sales-control.dto';

class SalesControlApi extends BaseApi {
  constructor() {
    super('sales'); // 設定 API 路徑前綴為 sales
  }

  /**
   * 取得銷控表資料
   * POST /api/sales/GetSalesControl/sales-control
   */
  async getSalesControl(params: GetSalesControlParams): Promise<ApiResponse<SalesControlData>> {
    return this.post<ApiResponse<SalesControlData>>('/GetSalesControl/sales-control', params);
  }

  /**
   * 取得案場銷售統計摘要
   * GET /api/sales/GetSalesStatistics/statistics/{siteCode}
   */
  async getSalesStatistics(siteCode: string): Promise<ApiResponse<SalesStatistics>> {
    return this.get<ApiResponse<SalesStatistics>>(`/GetSalesStatistics/statistics/${siteCode}`);
  }

  /**
   * 更新銷控表單位狀態
   * PUT /api/sales/UpdateSalesControlUnit/sales-control-unit/{unitId}
   */
  async updateSalesControlUnit(unitId: number, data: UpdateUnitStatusDto): Promise<ApiResponse<any>> {
    return this.put<ApiResponse<any>>(`/UpdateSalesControlUnit/sales-control-unit/${unitId}`, data);
  }
}

// 導出單例實例
export const salesControlApi = new SalesControlApi(); 