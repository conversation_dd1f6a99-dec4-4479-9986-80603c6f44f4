import { BaseApi } from './baseApi';
import type { ApiResponse, DropdownItem, BaseQueryParams } from '../../interfaces/dto/common.dto';

// --- Site API DTOs and Interfaces ---

// 案場列表項目 (對應 GetSiteList 回應的 Detail)
export interface SiteListItem {
  SiteCode: string;
  CompanyId: string; // 可能需要顯示公司名稱，後端若沒給，前端可能要另外處理
  SiteName: string;
  PromotionType: string;
  Chairman: string;
  ViceChairman: string;
  ProjectManager: string;
  DeputyProjectManager: string;
  BusinessIds: string; // 可能需要轉換為名字列表
  RunnerIds: string;   // 可能需要轉換為名字列表
  ReceptionCenter: string;
  SitePhone: string;
  Broker: string;
  City: string;
  District: string;
  Developer: string;
  SiteLocation: string;
  Address: string;
  LandArea: number;
  AboveGroundFloors: string;
  BelowGroundFloors: string;
  Zoning: string;
  PublicFacilityRatio: number;
  Structure: string;
  PlannedResidentialUnits: number;
  PlannedStoreUnits: number;
  PlannedParkingSpaces: number;
  UnitSize: string;
  TotalSalePrice: number;
  ParkingType: string;
  ContractPeriod: string; // YYYY-MM-DD
  ExtensionPeriod: string; // YYYY-MM-DD
  SellableTotalPrice: number;
  ServiceFeeCalculation: string;
  ServiceFeeRate: number;
  ReserveAmount: number;
  AdvertisingBudget: number;
  AdvertisingBudgetRate: number;
  ExcessPriceAllocation: string;
  ContractedAmount: number;
  ControlReserveRate: number;
  PaidAmount: number;
  CreatedUserId: string;
  CreatedUserName: string;
  CreatedTime: string; // ISO Date String
  UpdatedUserId: string;
  UpdatedUserName: string;
  UpdatedTime: string; // ISO Date String
}

// 取得案場列表的完整回應結構
export interface SiteListResponse {
  Detail: SiteListItem[];
  TotalPages: number;
  RecordCount: number;
  NumberOfPperPage: number;
  PageIndex: number;
  UsingPaging: boolean;
  SortOrderInfos?: { SortField: string; SortOrder: string }[];
  SearchTermInfos?: { SearchField: string; SearchValue: string }[];
}

// 建立案場 DTO
export interface CreateSiteDto {
  SiteCode: string;
  CompanyId: string;
  SiteName: string;
  PromotionType: string;
  Chairman: string;
  ViceChairman: string;
  ProjectManager: string;
  DeputyProjectManager: string;
  BusinessIds: string;
  RunnerIds: string;
  ReceptionCenter: string;
  SitePhone: string;
  Broker: string;
  City: string;
  District: string;
  Developer: string;
  SiteLocation: string;
  LandArea: number;
  // TotalPlannedFloors: number; // 注意: Create DTO 中有，但 Update DTO 和 List Item 中沒有
  Zoning: string;
  PublicFacilityRatio: number;
  Structure: string;
  // PlannedFloors: string; // 注意: Create/Update DTO 中有，但 List Item 中沒有
  // PlannedUnitsAndParking: string; // 注意: Create/Update DTO 中有，但 List Item 中沒有
  UnitSize: string;
  TotalSalePrice: number;
  ParkingType: string;
  ContractPeriod: string; // YYYY-MM-DD
  ExtensionPeriod: string; // YYYY-MM-DD
  SellableTotalPrice: number;
  ServiceFeeCalculation: string;
  ServiceFeeRate: number;
  ReserveAmount: number;
  AdvertisingBudget: number;
  AdvertisingBudgetRate: number;
  ExcessPriceAllocation: string;
  ContractedAmount: number;
  ControlReserveRate: number;
  PaidAmount: number;
  AboveGroundFloors: string;
  BelowGroundFloors: string;
  PlannedResidentialUnits: number;
  PlannedStoreUnits: number;
  PlannedParkingSpaces: number;
}

// 更新案場 DTO (結構與 Create 相同，但用途不同)
export type UpdateSiteDto = CreateSiteDto; // 直接使用 CreateSiteDto 作為類型別名

// --- End Site API DTOs and Interfaces ---

class SiteApi extends BaseApi {
  constructor() {
    super('Site');
  }

  // 獲取案場列表 (POST /api/Site/GetSiteList/GetList)
  async getSiteList(params: BaseQueryParams): Promise<ApiResponse<SiteListResponse>> {
    return this.post<ApiResponse<SiteListResponse>>('/GetSiteList/GetList', params);
  }

  // 根據代碼獲取案場 (GET /api/Site/GetSiteByCode/{siteCode})
  async getSiteByCode(siteCode: string): Promise<ApiResponse<SiteListItem>> {
    return this.get<ApiResponse<SiteListItem>>(`/GetSiteByCode/${siteCode}`);
  }

  // 建立案場 (POST /api/Site/CreateSite/Create)
  async createSite(data: CreateSiteDto): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>('/CreateSite/Create', data);
  }

  // 更新案場 (POST /api/Site/UpdateSite/Update)
  async updateSite(data: UpdateSiteDto): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>('/UpdateSite/Update', data);
  }

  // 刪除案場 (DELETE /api/Site/DeleteSite/{siteCode})
  async deleteSite(siteCode: string): Promise<ApiResponse<any>> {
    return this.delete<ApiResponse<any>>(`/DeleteSite/${siteCode}`);
  }

  // 獲取案場下拉選單 (GET /api/Site/GetSiteDropdownList/Dropdown)
  async getSiteDropdownList(): Promise<ApiResponse<DropdownItem[]>> {
    return this.get<ApiResponse<DropdownItem[]>>('/GetSiteDropdownList/Dropdown');
  }
}

// 導出單例實例
export const siteApi = new SiteApi(); 