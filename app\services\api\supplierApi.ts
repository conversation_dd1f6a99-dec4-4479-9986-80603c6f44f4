import { BaseApi } from './baseApi';
import {
  GetSuppliersParams,
  GetSuppliersResponse,
  CreateSupplierDto,
  SupplierDetail,
  UpdateSupplierDto,
  ApiResponse,
  SupplierFileBase
} from '../../interfaces/dto/supplier.dto';

class SupplierApi extends BaseApi {
  constructor() {
    super('Suppliers'); // API 前綴為 /api/Suppliers
  }

  /**
   * 取得供應商列表 (分頁)
   * POST /api/Suppliers/GetSuppliers
   */
  public getSuppliers(params: GetSuppliersParams): Promise<ApiResponse<GetSuppliersResponse>> {
    return this.post<ApiResponse<GetSuppliersResponse>>('/GetSuppliers', params);
  }

  /**
   * 根據供應商ID取得詳細資訊
   * GET /api/Suppliers/GetSupplier/{supplierId}
   */
  public getSupplierById(supplierId: number): Promise<ApiResponse<SupplierDetail>> {
    return this.get<ApiResponse<SupplierDetail>>(`/GetSupplier/${supplierId}`) as Promise<ApiResponse<SupplierDetail>>;
  }

  /**
   * 新增供應商
   * POST /api/Suppliers/CreateSupplier
   */
  public createSupplier(data: CreateSupplierDto, filesToUpload: File[]): Promise<ApiResponse> {
    const formData = new FormData();

    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = data[key as keyof CreateSupplierDto];

        if (key === 'Files' && Array.isArray(value)) {
          (value as SupplierFileBase[]).forEach((meta, index) => {
            // 後端期望新檔案的 SupplierFileId 為 0
            if (meta.FormType !== undefined && meta.FormType !== null) formData.append(`Files[${index}].FormType`, meta.FormType);
            if (meta.FileName !== undefined && meta.FileName !== null) formData.append(`Files[${index}].FileName`, meta.FileName);
            if (meta.FilePath !== undefined && meta.FilePath !== null) formData.append(`Files[${index}].FilePath`, meta.FilePath);
            if (meta.Remark !== undefined && meta.Remark !== null) formData.append(`Files[${index}].Remark`, meta.Remark);
            if (meta.Agent !== undefined && meta.Agent !== null) formData.append(`Files[${index}].Agent`, meta.Agent);
            // 對於 createSupplier，所有 Files 項目都應視為新檔案，其 SupplierFileId 在 page.tsx 中已設為 0
            formData.append(`Files[${index}].SupplierFileId`, String(meta.SupplierFileId === undefined || meta.SupplierFileId === null ? 0 : meta.SupplierFileId));
          });
        } else if (value !== undefined && value !== null) {
          if (typeof value === 'string') {
            formData.append(key, value);
          } else if (typeof value === 'number' || typeof value === 'boolean') {
            formData.append(key, String(value));
          } 
        }
      }
    }

    filesToUpload.forEach((file) => {
      formData.append('ActualFiles', file, file.name);
    });

    return this.post<ApiResponse>('/CreateSupplier', formData);
  }

  /**
   * 更新供應商
   * PUT /api/Suppliers/UpdateSupplier/{supplierId}
   */
  public updateSupplier(supplierId: number | string, data: UpdateSupplierDto, filesToUpload: File[]): Promise<ApiResponse<any>> {
    const formData = new FormData();

    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = data[key as keyof UpdateSupplierDto];

        if (key === 'Files' && Array.isArray(value)) {
          (value as SupplierFileBase[]).forEach((meta, index) => {
            if (meta.FormType !== undefined && meta.FormType !== null) formData.append(`Files[${index}].FormType`, meta.FormType);
            if (meta.FileName !== undefined && meta.FileName !== null) formData.append(`Files[${index}].FileName`, meta.FileName);
            if (meta.FilePath !== undefined && meta.FilePath !== null) formData.append(`Files[${index}].FilePath`, meta.FilePath);
            if (meta.Remark !== undefined && meta.Remark !== null) formData.append(`Files[${index}].Remark`, meta.Remark);
            if (meta.Agent !== undefined && meta.Agent !== null) formData.append(`Files[${index}].Agent`, meta.Agent);
            // 對於 updateSupplier，SupplierFileId 可以是 0 (新檔案) 或 >0 (舊檔案)
            // page.tsx 中的 handleSubmit 應該已經正確設定了 meta.SupplierFileId
            if (meta.SupplierFileId !== undefined && meta.SupplierFileId !== null) {
                formData.append(`Files[${index}].SupplierFileId`, String(meta.SupplierFileId));
            } else {
                // 如果意外地 SupplierFileId 是 undefined/null，則視為新檔案 (ID 0)
                formData.append(`Files[${index}].SupplierFileId`, '0');
            }
          });
        } else if (value !== undefined && value !== null) {
          if (typeof value === 'string') {
            formData.append(key, value);
          } else if (typeof value === 'number' || typeof value === 'boolean') {
            formData.append(key, String(value));
          }
        }
      }
    }

    filesToUpload.forEach((file) => {
      formData.append('ActualFiles', file, file.name);
    });

    return this.put<ApiResponse<any>>(`/UpdateSupplier/${supplierId}`, formData);
  }

  /**
   * 刪除供應商
   * DELETE /api/Suppliers/DeleteSupplier/{supplierId}
   */
  public deleteSupplier(supplierId: number | string): Promise<ApiResponse<any>> {
    return this.delete<ApiResponse<any>>(`/DeleteSupplier/${supplierId}`);
  }
}

export const supplierApi = new SupplierApi(); 