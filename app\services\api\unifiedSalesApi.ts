// 統一銷售管理 API 服務

import { BaseApi } from './baseApi';
import type { 
  ApiResponse, 
  BaseQueryParams 
} from '../../interfaces/dto/common.dto';
import type {
  UnifiedSalesQueryParams,
  UnifiedSalesListResponse,
  UnifiedSalesStatistics,
  HouseListItem,
  ParkingListItem,
} from '../../interfaces/dto/unified-sales.dto';

class UnifiedSalesApi extends BaseApi {
  constructor() {
    super('UnifiedSales');
  }

  /**
   * 查詢統一銷售物件列表
   * @param params 查詢參數
   * @returns Promise<ApiResponse<UnifiedSalesListResponse>>
   */
  async getUnifiedSalesList(params: UnifiedSalesQueryParams): Promise<ApiResponse<UnifiedSalesListResponse>> {
    return this.post<ApiResponse<UnifiedSalesListResponse>>('/list', params);
  }

  /**
   * 查詢可售物件列表
   * @param params 查詢參數 (會自動篩選可售狀態)
   * @returns Promise<ApiResponse<UnifiedSalesListResponse>>
   */
  async getAvailableList(params: UnifiedSalesQueryParams): Promise<ApiResponse<UnifiedSalesListResponse>> {
    return this.post<ApiResponse<UnifiedSalesListResponse>>('/available', params);
  }

  /**
   * 查詢保留物件列表
   * @param params 查詢參數 (會自動篩選保留狀態)
   * @returns Promise<ApiResponse<UnifiedSalesListResponse>>
   */
  async getReservedList(params: UnifiedSalesQueryParams): Promise<ApiResponse<UnifiedSalesListResponse>> {
    return this.post<ApiResponse<UnifiedSalesListResponse>>('/reserved', params);
  }

  /**
   * 查詢案場銷售統計
   * @param siteCode 案場編號
   * @returns Promise<ApiResponse<UnifiedSalesStatistics>>
   */
  async getSalesStatistics(siteCode: string): Promise<ApiResponse<UnifiedSalesStatistics>> {
    return this.get<ApiResponse<UnifiedSalesStatistics>>(`/statistics/${siteCode}`);
  }

  /**
   * 查詢房屋可售/保留列表
   * @param params 查詢參數
   * @returns Promise<ApiResponse<UnifiedSalesListResponse>>
   */
  async getHousesAvailableReserved(params: Omit<UnifiedSalesQueryParams, 'itemType'>): Promise<ApiResponse<UnifiedSalesListResponse>> {
    return this.getUnifiedSalesList({
      ...params,
      itemType: '房屋',
    });
  }

  /**
   * 查詢車位可售/保留列表
   * @param params 查詢參數
   * @returns Promise<ApiResponse<UnifiedSalesListResponse>>
   */
  async getParkingAvailableReserved(params: Omit<UnifiedSalesQueryParams, 'itemType'>): Promise<ApiResponse<UnifiedSalesListResponse>> {
    return this.getUnifiedSalesList({
      ...params,
      itemType: '車位',
    });
  }
}

// 創建全域實例
export const unifiedSalesApi = new UnifiedSalesApi(); 