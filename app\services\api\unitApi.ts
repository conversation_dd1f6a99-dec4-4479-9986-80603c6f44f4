import { BaseApi } from './baseApi';
import type { ApiResponse } from '@/app/interfaces/dto/common.dto';
import type { 
  CreateUnitDto, 
  UpdateUnitDto, 
  UnitQueryDto,
  UnitListPagedResult,
  UnitDetailDto
} from '@/app/interfaces/dto/unit.dto';

class UnitApi extends BaseApi {
  constructor() {
    super('Units'); // 使用 PascalCase
  }

  // POST /api/Units/GetList/list
  public getUnits(params: UnitQueryDto) {
    return this.post<ApiResponse<UnitListPagedResult>>('/GetUnits', params);
  }
  
  // GET /api/Units/Get/{id}
  public getUnitById(id: number) {
    return this.get<ApiResponse<UnitDetailDto>>(`/Get/${id}`);
  }

  // POST /api/Units/Create
  public createUnit(data: CreateUnitDto) {
    return this.post<ApiResponse<any>>('/Create', data);
  }

  // PUT /api/Units/Update/{id}
  public updateUnit(id: number, data: UpdateUnitDto) {
    return this.put<ApiResponse<any>>(`/Update/${id}`, data);
  }

  // DELETE /api/Units/Delete/{id}
  public deleteUnit(id: number) {
    return this.delete<ApiResponse<any>>(`/Delete/${id}`);
  }
}

export const unitApi = new UnitApi();
