import { BaseApi } from './baseApi';
import { ApiResponse, PagedResult } from '@/app/interfaces/dto/common.dto';
import type { 
    UnitListPagedResult, 
    UnitQueryDto, 
    UnitDetailDto, 
    CreateUnitDto, 
    UpdateUnitDto 
} from '@/app/interfaces/dto/unit.dto';

class UnitApi extends BaseApi {
  constructor() {
    super('Units');
  }

  getUnits(params: UnitQueryDto): Promise<ApiResponse<UnitListPagedResult>> {
    return this.post<ApiResponse<UnitListPagedResult>>('/GetUnits', params);
  }

  getUnit(unitId: number): Promise<ApiResponse<UnitDetailDto>> {
    return this.get<ApiResponse<UnitDetailDto>>(`/GetUnit/${unitId}`);
  }

  createUnit(data: CreateUnitDto): Promise<ApiResponse<UnitDetailDto>> {
    return this.post<ApiResponse<UnitDetailDto>>('/CreateUnit', data);
  }

  updateUnit(unitId: number, data: UpdateUnitDto): Promise<ApiResponse<UnitDetailDto>> {
    return this.put<ApiResponse<UnitDetailDto>>(`/UpdateUnit/${unitId}`, data);
  }

  deleteUnit(unitId: number): Promise<ApiResponse<any>> {
    return this.delete<ApiResponse<any>>(`/DeleteUnit/${unitId}`);
  }
}

export const unitApi = new UnitApi(); 