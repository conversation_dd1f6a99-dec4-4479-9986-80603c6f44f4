import { UserDropdownDto } from '@/app/interfaces/dto/user.dto';
import { BaseApi } from './baseApi';
import type { ApiResponse, BaseQueryParams, DropdownItem } from '@/app/interfaces/dto/common.dto';

// 使用者列表項目
export interface UserInfoListItem {
  UserInfoId: string;
  Name: string;
  Email: string;
  MobileNumber: string;
  DepartmentId: string;
  DepartmentName: string;
  JobTitleId: string;
  JobTitleName: string;
  Status: boolean;
  IsAdmin: boolean;
  LastLoginTime: string;
  LastLoginIp: string;
  LoginFailedCount: number;
  IsLock: boolean;
  RoleGroups: Array<{
    RoleGroupId: string;
    Name: string;
  }>;
  CompanyId: string;
  Gender: string;
  BirthDate: string;
  TelephoneNumber: string;
  RegisteredAddress: string;
  MailingAddress: string;
  EmergencyContactName: string;
  EmergencyContactPhone: string;
  EmergencyContactRelation: string;
  ServiceUnit: string;
  HireDate: string;
  LastLogoutTime: string;
  IsInside: boolean;
  IsM365: boolean;
  IsEmailNotificationEnabled: boolean;
  Identity: string;
}

// 使用者列表響應
export interface UserInfoListResponse {
  Detail: UserInfoListItem[];
  TotalPages: number;
  RecordCount: number;
  NumberOfPperPage: number;
  PageIndex: number;
  UsingPaging: boolean;
  SortOrderInfos: {
    SortField: string;
    SortOrder: string;
  }[];
  SearchTermInfos: {
    SearchField: string;
    SearchValue: string;
  }[];
}

// 定義查詢參數接口
export interface UserInfoDropdownParams {
  DepartmentId?: string;
  JobTitleId?: string;
  RoleGroupId?: string;
  Name?: string;
}

// 建立使用者DTO
export interface CreateUserInfoDto {
  UserInfoId: string;
  Name: string;
  Email: string;
  MobileNumber: string;
  Identity: string;
  DepartmentId: string;
  JobTitleId: string;
  CompanyId: string;
  ServiceUnit: string;
  Status: boolean;
  Gender: string;
  BirthDate: string;
  TelephoneNumber: string;
  RegisteredAddress: string;
  MailingAddress: string;
  EmergencyContactName: string;
  EmergencyContactPhone: string;
  EmergencyContactRelation: string;
  HireDate: string;
  IsInside: boolean;
  IsM365: boolean;
  IsEmailNotificationEnabled: boolean;
  RoleGroupIds: string[];
}

// 更新使用者DTO (移除 IsAdmin)
export interface UpdateUserInfoDto extends Omit<CreateUserInfoDto, 'UserInfoId' | 'IsAdmin'> { // 從 Omit 中也移除 IsAdmin
  UserInfoId: string;
  Password?: string;
}

// 更新使用者狀態DTO
export interface UpdateUserStatusDto {
  UserInfoId: string;
  Status: boolean;
}

// 重設密碼DTO
export interface ResetPasswordDto {
  UserInfoId: string;
  NewPassword: string;
}

class UserInfoApi extends BaseApi {
  constructor() {
    super('/UserInfo');
  }

  // 獲取公司下拉選單
  async getCompanyDropdownList(): Promise<ApiResponse<DropdownItem[]>> {
    return this.callWithPrefix<ApiResponse<DropdownItem[]>>('get', '/Company', '/GetCompanyDropdownList');
  }

  // 獲取部門下拉選單
  async getDepartmentDropdownList(companyId?: string): Promise<ApiResponse<DropdownItem[]>> {
    const data = companyId ? { CompanyId: companyId } : {};
    return this.callWithPrefix<ApiResponse<DropdownItem[]>>('post', '/Department', '/GetDepartmentDropdownList', data);
  }

  // 獲取職稱下拉選單
  async getJobTitleDropdownList(departmentId?: string): Promise<ApiResponse<DropdownItem[]>> {
    const data = departmentId ? { DepartmentId: departmentId } : {};
    return this.callWithPrefix<ApiResponse<DropdownItem[]>>('post', '/JobTitle', '/GetJobTitleDropdownList', data);
  }
  // 獲取角色下拉選單
  async getRoleGroupDropdownList(siteCode?: string): Promise<ApiResponse<DropdownItem[]>> {
    const data = siteCode ? { SiteCode: siteCode } : {};
    // 即使 baseApi 的前綴是 /RoleGroupControllerPg，我們仍需指定正確的 /RoleGroup 前綴
    return this.callWithPrefix<ApiResponse<DropdownItem[]>>('post', '/RoleGroupControllerPg', '/GetRoleGroupDropdownList', data);
  }
  // 獲取人員下拉選單
  async getUserInfoDropdownList(params?: UserInfoDropdownParams): Promise<ApiResponse<DropdownItem[]>> {
    return this.post<ApiResponse<DropdownItem[]>>('/GetUserInfoDropdownList', params || {});
  }

  // 獲取使用者列表
  async getUserInfoList(params: BaseQueryParams): Promise<ApiResponse<UserInfoListResponse>> {
    return this.post<ApiResponse<UserInfoListResponse>>('/GetUserInfoList', params);
  }

  // 建立使用者
  async createUserInfo(data: CreateUserInfoDto): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>('/CreateUserInfo', data);
  }

  // 更新使用者 (修改 API 路徑和方法)
  async updateUserInfo(data: UpdateUserInfoDto): Promise<ApiResponse<any>> {
    // 使用 POST 方法，並將 UserInfoId 放在 data 中
    return this.post<ApiResponse<any>>('/UpdateUserInfo', data);
  }

  // 更新使用者狀態 (修改 API 路徑和方法)
  async updateUserStatus(data: UpdateUserStatusDto): Promise<ApiResponse<any>> {
    // return this.put<ApiResponse<any>>('/UpdateUserStatus', data); // 舊的 PUT 方法
    return this.post<ApiResponse<any>>('/UpdateUserInfoStatus', data); // 改為 POST 新端點
  }

  // 重設密碼
  async resetPassword(data: ResetPasswordDto): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>('/ResetPassword', data);
  }

  // 刪除使用者
  async deleteUserInfo(userId: string): Promise<ApiResponse<any>> {
    return this.delete<ApiResponse<any>>(`/DeleteUserInfo/${userId}`);
  }
}

// 導出單例實例
export const userInfoApi = new UserInfoApi(); 