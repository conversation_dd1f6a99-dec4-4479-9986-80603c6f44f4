'use client';

import { ChartData, ChartDataGenerator } from '../interfaces/dto/chart.dto';

// 顏色常量
const COLORS = {
  RED: 'rgba(255, 99, 132, 0.7)',
  BLUE: 'rgba(54, 162, 235, 0.7)',
  YELLOW: 'rgba(255, 206, 86, 0.7)',
  GREEN: 'rgba(75, 192, 192, 0.7)',
  PURPLE: 'rgba(153, 102, 255, 0.7)',
  ORANGE: 'rgba(255, 159, 64, 0.7)',
  PINK: 'rgba(255, 99, 132, 0.7)',
  LIGHT_BLUE: 'rgba(54, 162, 235, 0.7)',
  LIGHT_YELLOW: 'rgba(255, 206, 86, 0.7)',
  LIGHT_GREEN: 'rgba(75, 192, 192, 0.7)',
};

// 邊框顏色常量
const BORDER_COLORS = {
  RED: 'rgba(255, 99, 132, 1)',
  BLUE: 'rgba(54, 162, 235, 1)',
  YELLOW: 'rgba(255, 206, 86, 1)',
  GREEN: 'rgba(75, 192, 192, 1)',
  PURPLE: 'rgba(153, 102, 255, 1)',
  ORANGE: 'rgba(255, 159, 64, 1)',
  PINK: 'rgba(255, 99, 132, 1)',
  LIGHT_BLUE: 'rgba(54, 162, 235, 1)',
  LIGHT_YELLOW: 'rgba(255, 206, 86, 1)',
  LIGHT_GREEN: 'rgba(75, 192, 192, 1)',
};

// 隨機數據生成函數
const generateRandomData = (count: number, min: number = 0, max: number = 100): number[] => {
  return Array.from({ length: count }, () => Math.floor(Math.random() * (max - min + 1)) + min);
};

// 圖表數據生成服務
class ChartDataService implements ChartDataGenerator {
  // 生成折線圖數據
  generateLineChartData(months: number = 12): ChartData {
    const labels = [
      '一月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '十一月', '十二月'
    ].slice(0, months);

    return {
      labels,
      datasets: [
        {
          label: '銷售額',
          data: generateRandomData(months, 50, 200),
          borderColor: BORDER_COLORS.BLUE,
          backgroundColor: COLORS.BLUE,
          tension: 0.4,
          fill: false
        },
        {
          label: '利潤',
          data: generateRandomData(months, 20, 100),
          borderColor: BORDER_COLORS.GREEN,
          backgroundColor: COLORS.GREEN,
          tension: 0.4,
          fill: false
        }
      ]
    };
  }

  // 生成柱狀圖數據
  generateBarChartData(categories: number = 6): ChartData {
    const projectNames = ['太平洋花園', '陽光大廈', '翠湖山莊', '藍海灣', '金色年華', '綠地公園'];
    const labels = projectNames.slice(0, categories);

    return {
      labels,
      datasets: [
        {
          label: '已售出',
          data: generateRandomData(categories, 10, 50),
          backgroundColor: COLORS.BLUE,
          borderColor: BORDER_COLORS.BLUE,
          borderWidth: 1
        },
        {
          label: '庫存',
          data: generateRandomData(categories, 5, 30),
          backgroundColor: COLORS.ORANGE,
          borderColor: BORDER_COLORS.ORANGE,
          borderWidth: 1
        }
      ]
    };
  }

  // 生成餅圖數據
  generatePieChartData(slices: number = 5): ChartData {
    const customerSources = ['網路廣告', '朋友介紹', '路過看到', '房產中介', '其他'];
    const labels = customerSources.slice(0, slices);
    const backgroundColors = [
      COLORS.RED, COLORS.BLUE, COLORS.YELLOW, COLORS.GREEN, COLORS.PURPLE
    ].slice(0, slices);
    const borderColors = [
      BORDER_COLORS.RED, BORDER_COLORS.BLUE, BORDER_COLORS.YELLOW, 
      BORDER_COLORS.GREEN, BORDER_COLORS.PURPLE
    ].slice(0, slices);

    return {
      labels,
      datasets: [
        {
          label: '客戶來源',
          data: generateRandomData(slices, 10, 100),
          backgroundColor: backgroundColors,
          borderColor: borderColors,
          borderWidth: 1
        }
      ]
    };
  }

  // 生成環形圖數據
  generateDoughnutChartData(slices: number = 4): ChartData {
    const budgetCategories = ['行銷費用', '人事費用', '營運費用', '其他費用'];
    const labels = budgetCategories.slice(0, slices);
    const backgroundColors = [
      COLORS.RED, COLORS.BLUE, COLORS.YELLOW, COLORS.GREEN
    ].slice(0, slices);
    const borderColors = [
      BORDER_COLORS.RED, BORDER_COLORS.BLUE, BORDER_COLORS.YELLOW, BORDER_COLORS.GREEN
    ].slice(0, slices);

    return {
      labels,
      datasets: [
        {
          label: '預算分配',
          data: generateRandomData(slices, 10, 100),
          backgroundColor: backgroundColors,
          borderColor: borderColors,
          borderWidth: 1
        }
      ]
    };
  }

  // 生成雷達圖數據
  generateRadarChartData(points: number = 6): ChartData {
    const performanceMetrics = ['銷售能力', '客戶服務', '產品知識', '談判技巧', '團隊合作', '時間管理'];
    const labels = performanceMetrics.slice(0, points);

    return {
      labels,
      datasets: [
        {
          label: '業務 A',
          data: generateRandomData(points, 50, 100),
          backgroundColor: COLORS.RED,
          borderColor: BORDER_COLORS.RED,
          borderWidth: 1
        },
        {
          label: '業務 B',
          data: generateRandomData(points, 40, 90),
          backgroundColor: COLORS.BLUE,
          borderColor: BORDER_COLORS.BLUE,
          borderWidth: 1
        }
      ]
    };
  }
}

// 導出單例實例
export const chartDataService = new ChartDataService(); 