/**
 * 將日期字串 (可能是 ISO 格式) 格式化為 YYYY-MM-DD 以適用於 <input type="date">。
 * @param dateString 可能的日期字串 (例如 "YYYY-MM-DDTHH:mm:ss" 或 "YYYY-MM-DD")。
 * @returns 返回 YYYY-MM-DD 格式的字串，如果輸入無效或為空，則返回空字串。
 */
export function formatDateForInput(dateString: string | null | undefined): string {
  if (!dateString) {
    return '';
  }
  try {
    // 嘗試直接取前10個字符，這對 "YYYY-MM-DDTHH:mm:ss" 和 "YYYY-MM-DD" 都適用
    const datePart = dateString.substring(0, 10);
    // 簡單驗證格式是否為 YYYY-MM-DD
    if (/^\d{4}-\d{2}-\d{2}$/.test(datePart)) {
      return datePart;
    } else {
      // 如果格式不對，嘗試用 Date 物件解析再格式化，增加兼容性
      const date = new Date(dateString);
      // 檢查日期是否有效
      if (isNaN(date.getTime())) {
        return '';
      }
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  } catch (error) {
    console.error("Error formatting date:", dateString, error);
    return ''; // 出錯時返回空字串
  }
} 