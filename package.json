{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/nextjs-registry": "^1.0.2", "@stagewise/toolbar-next": "^0.4.8", "@svgdotjs/svg.js": "^3.2.4", "@types/lodash": "^4.17.16", "@types/react-signature-canvas": "^1.0.7", "antd": "^5.24.7", "axios": "^1.8.1", "chart.js": "^4.4.8", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "echarts-gl": "^2.0.9", "echarts-stat": "^1.2.0", "lodash": "^4.17.21", "lucide-react": "^0.477.0", "moment": "^2.30.1", "next": "15.1.6", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-signature-canvas": "^1.1.0-alpha.1", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/moment": "^2.13.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}